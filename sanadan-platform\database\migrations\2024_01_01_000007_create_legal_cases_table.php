<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل الهجرة
     */
    public function up(): void
    {
        Schema::create('legal_cases', function (Blueprint $table) {
            $table->id();
            $table->string('case_number')->unique(); // رقم القضية
            $table->string('title'); // عنوان القضية
            $table->text('description')->nullable(); // وصف القضية
            $table->foreignId('case_type_id')->constrained('case_types');
            $table->enum('status', [
                'open', 'in_progress', 'pending', 'on_hold', 
                'closed', 'dismissed', 'settled', 'appealed'
            ])->default('open');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->foreignId('client_id')->constrained('clients');
            $table->foreignId('lawyer_id')->constrained('users'); // المحامي المسؤول
            $table->foreignId('court_id')->nullable()->constrained('courts');
            $table->date('filing_date')->nullable(); // تاريخ رفع القضية
            $table->datetime('next_hearing_date')->nullable(); // تاريخ الجلسة القادمة
            $table->date('estimated_completion_date')->nullable(); // التاريخ المتوقع للانتهاء
            $table->decimal('estimated_value', 15, 3)->nullable(); // القيمة المتوقعة للقضية
            $table->decimal('fees_amount', 10, 3)->nullable(); // أتعاب المحاماة
            $table->enum('fees_type', ['fixed', 'hourly', 'percentage'])->default('fixed');
            $table->json('case_parties')->nullable(); // أطراف القضية
            $table->json('legal_references')->nullable(); // المراجع القانونية
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['status', 'priority']);
            $table->index(['lawyer_id', 'status']);
            $table->index(['client_id', 'status']);
            $table->index('case_number');
            $table->index('next_hearing_date');
        });
    }

    /**
     * التراجع عن الهجرة
     */
    public function down(): void
    {
        Schema::dropIfExists('legal_cases');
    }
};

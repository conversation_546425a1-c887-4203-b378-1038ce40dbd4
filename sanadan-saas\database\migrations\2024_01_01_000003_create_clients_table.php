<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained()->onDelete('cascade');
            $table->foreignId('created_by')->constrained('users');
            $table->foreignId('assigned_to')->nullable()->constrained('users');
            
            // معلومات أساسية
            $table->enum('type', ['individual', 'company']); // فرد أو شركة
            $table->string('name'); // الاسم أو اسم الشركة
            $table->string('name_en')->nullable(); // الاسم بالإنجليزية
            $table->string('civil_id')->nullable(); // الرقم المدني
            $table->string('commercial_registration')->nullable(); // السجل التجاري
            $table->string('license_number')->nullable(); // رقم الترخيص
            
            // معلومات الاتصال
            $table->string('email')->nullable();
            $table->string('phone');
            $table->string('phone_2')->nullable();
            $table->string('fax')->nullable();
            $table->string('website')->nullable();
            
            // العنوان
            $table->string('country')->default('Kuwait');
            $table->string('governorate')->nullable(); // المحافظة
            $table->string('area')->nullable(); // المنطقة
            $table->string('block')->nullable(); // القطعة
            $table->string('street')->nullable(); // الشارع
            $table->string('building')->nullable(); // المبنى
            $table->string('floor')->nullable(); // الطابق
            $table->string('apartment')->nullable(); // الشقة
            $table->text('address_details')->nullable(); // تفاصيل العنوان
            $table->string('postal_code')->nullable();
            
            // معلومات إضافية للأفراد
            $table->date('birth_date')->nullable();
            $table->enum('gender', ['male', 'female'])->nullable();
            $table->enum('marital_status', ['single', 'married', 'divorced', 'widowed'])->nullable();
            $table->string('nationality')->nullable();
            $table->string('passport_number')->nullable();
            
            // معلومات إضافية للشركات
            $table->string('company_type')->nullable(); // نوع الشركة
            $table->string('authorized_person')->nullable(); // الشخص المفوض
            $table->string('authorized_person_position')->nullable(); // منصب الشخص المفوض
            $table->date('establishment_date')->nullable(); // تاريخ التأسيس
            $table->decimal('capital', 15, 3)->nullable(); // رأس المال
            
            // إعدادات العميل
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->enum('status', ['active', 'inactive', 'blocked'])->default('active');
            $table->decimal('credit_limit', 10, 3)->default(0); // الحد الائتماني
            $table->decimal('current_balance', 10, 3)->default(0); // الرصيد الحالي
            $table->text('notes')->nullable(); // ملاحظات
            $table->json('custom_fields')->nullable(); // حقول مخصصة
            
            // معلومات التواصل المفضلة
            $table->enum('preferred_contact_method', ['phone', 'email', 'whatsapp', 'sms'])->default('phone');
            $table->enum('preferred_language', ['ar', 'en'])->default('ar');
            
            // معلومات المصدر
            $table->string('source')->nullable(); // مصدر العميل
            $table->string('referral_source')->nullable(); // مصدر الإحالة
            
            $table->timestamps();
            $table->softDeletes();
            
            // فهارس
            $table->index(['tenant_id', 'type']);
            $table->index(['tenant_id', 'status']);
            $table->index(['tenant_id', 'assigned_to']);
            $table->index('civil_id');
            $table->index('commercial_registration');
            $table->index('phone');
            $table->index('email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};

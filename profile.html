<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الملف الشخصي - منصة سندان</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-50 font-arabic" x-data="profileApp()" x-init="init()">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between h-16">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <a href="dashboard.html" class="flex items-center text-gray-600 hover:text-gray-900">
                            <i class="fas fa-arrow-right ml-2"></i>
                            <span>العودة للوحة التحكم</span>
                        </a>
                    </div>
                    
                    <div class="flex items-center space-x-3 space-x-reverse">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-balance-scale text-white text-sm"></i>
                        </div>
                        <h1 class="text-xl font-bold text-gray-900">الملف الشخصي</h1>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <div class="space-y-6">
                <!-- Profile Header -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden fade-in">
                    <div class="bg-gradient-to-r from-primary-600 to-primary-700 px-6 py-8">
                        <div class="flex items-center space-x-6 space-x-reverse">
                            <div class="relative">
                                <img :src="user.avatar" :alt="user.name" class="w-24 h-24 rounded-full border-4 border-white shadow-lg">
                                <button @click="changeAvatar()" class="absolute bottom-0 right-0 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg hover:bg-gray-50 transition-colors duration-200">
                                    <i class="fas fa-camera text-gray-600 text-sm"></i>
                                </button>
                            </div>
                            <div class="text-white">
                                <h2 class="text-2xl font-bold" x-text="user.name"></h2>
                                <p class="text-primary-100" x-text="user.email"></p>
                                <p class="text-primary-200 text-sm mt-1" x-text="user.role === 'owner' ? 'مالك المكتب' : 'محامي'"></p>
                                <div class="mt-3 flex items-center space-x-4 space-x-reverse text-sm">
                                    <div class="flex items-center">
                                        <i class="fas fa-calendar ml-2"></i>
                                        <span>انضم في {{ formatDate(user.registrationTime || user.loginTime) }}</span>
                                    </div>
                                    <div class="flex items-center" x-show="user.trialEndsAt">
                                        <i class="fas fa-clock ml-2"></i>
                                        <span>النسخة التجريبية تنتهي في {{ getTrialDaysLeft() }} يوم</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Profile Form -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <!-- Personal Information -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 fade-in">
                            <div class="p-6 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-900">المعلومات الشخصية</h3>
                                <p class="text-sm text-gray-600 mt-1">قم بتحديث معلوماتك الشخصية</p>
                            </div>
                            
                            <form @submit.prevent="updateProfile()" class="p-6 space-y-6">
                                <!-- Name -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الأول</label>
                                        <input type="text" x-model="profileData.firstName" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الأخير</label>
                                        <input type="text" x-model="profileData.lastName" required
                                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                                    </div>
                                </div>

                                <!-- Email -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                                    <input type="email" x-model="profileData.email" required
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                                </div>

                                <!-- Phone -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                                    <input type="tel" x-model="profileData.phone"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                           placeholder="+965 9999 9999">
                                </div>

                                <!-- Office Name -->
                                <div x-show="user.role === 'owner'">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">اسم المكتب</label>
                                    <input type="text" x-model="profileData.officeName"
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
                                </div>

                                <!-- Bio -->
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">نبذة شخصية</label>
                                    <textarea x-model="profileData.bio" rows="4"
                                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                                              placeholder="اكتب نبذة مختصرة عنك..."></textarea>
                                </div>

                                <!-- Submit Button -->
                                <div class="flex justify-end">
                                    <button type="submit" :disabled="loading"
                                            class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
                                        <span x-show="!loading">حفظ التغييرات</span>
                                        <span x-show="loading" class="flex items-center">
                                            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                            جاري الحفظ...
                                        </span>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="space-y-6">
                        <!-- Account Status -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 fade-in">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">حالة الحساب</h3>
                                
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-600">نوع الحساب</span>
                                        <span class="text-sm font-medium text-primary-600" x-text="user.trialEndsAt ? 'نسخة تجريبية' : 'حساب مدفوع'"></span>
                                    </div>
                                    
                                    <div class="flex items-center justify-between" x-show="user.trialEndsAt">
                                        <span class="text-sm text-gray-600">الأيام المتبقية</span>
                                        <span class="text-sm font-medium text-orange-600" x-text="getTrialDaysLeft() + ' يوم'"></span>
                                    </div>
                                    
                                    <div class="flex items-center justify-between">
                                        <span class="text-sm text-gray-600">آخر تسجيل دخول</span>
                                        <span class="text-sm text-gray-500" x-text="formatDate(user.loginTime)"></span>
                                    </div>
                                </div>

                                <div class="mt-6" x-show="user.trialEndsAt">
                                    <button class="w-full bg-gradient-to-r from-green-500 to-green-600 text-white py-2 px-4 rounded-lg font-medium hover:from-green-600 hover:to-green-700 transition-all duration-200">
                                        <i class="fas fa-crown ml-2"></i>
                                        ترقية الحساب
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 fade-in">
                            <div class="p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h3>
                                
                                <div class="space-y-3">
                                    <a href="settings.html" class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                        <i class="fas fa-cog w-5 h-5 text-gray-400 ml-3"></i>
                                        <span class="text-sm font-medium text-gray-700">إعدادات الحساب</span>
                                        <i class="fas fa-chevron-left mr-auto text-gray-400"></i>
                                    </a>
                                    
                                    <a href="security.html" class="flex items-center p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                                        <i class="fas fa-shield-alt w-5 h-5 text-gray-400 ml-3"></i>
                                        <span class="text-sm font-medium text-gray-700">الأمان والخصوصية</span>
                                        <i class="fas fa-chevron-left mr-auto text-gray-400"></i>
                                    </a>
                                    
                                    <button @click="logout()" class="flex items-center w-full p-3 rounded-lg hover:bg-red-50 transition-colors duration-200 text-red-600">
                                        <i class="fas fa-sign-out-alt w-5 h-5 ml-3"></i>
                                        <span class="text-sm font-medium">تسجيل الخروج</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Notifications -->
    <div id="notifications" class="fixed top-4 left-4 z-50 space-y-2"></div>

    <script>
        function profileApp() {
            return {
                user: {},
                loading: false,
                profileData: {
                    firstName: '',
                    lastName: '',
                    email: '',
                    phone: '',
                    officeName: '',
                    bio: ''
                },
                
                init() {
                    // تحقق من تسجيل الدخول
                    const userData = localStorage.getItem('sanadan_user');
                    if (!userData) {
                        window.location.href = 'login.html';
                        return;
                    }
                    
                    this.user = JSON.parse(userData);
                    this.loadProfileData();
                },
                
                loadProfileData() {
                    // تحميل البيانات الحالية
                    const nameParts = this.user.name.split(' ');
                    this.profileData = {
                        firstName: nameParts[0] || '',
                        lastName: nameParts.slice(1).join(' ') || '',
                        email: this.user.email || '',
                        phone: this.user.phone || '',
                        officeName: this.user.officeName || '',
                        bio: this.user.bio || ''
                    };
                },
                
                async updateProfile() {
                    this.loading = true;
                    
                    try {
                        // محاكاة حفظ البيانات
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        
                        // تحديث بيانات المستخدم
                        this.user.name = `${this.profileData.firstName} ${this.profileData.lastName}`.trim();
                        this.user.email = this.profileData.email;
                        this.user.phone = this.profileData.phone;
                        this.user.officeName = this.profileData.officeName;
                        this.user.bio = this.profileData.bio;
                        
                        // حفظ في التخزين المحلي
                        localStorage.setItem('sanadan_user', JSON.stringify(this.user));
                        
                        this.showNotification('تم بنجاح', 'تم حفظ التغييرات بنجاح', 'success');
                        
                    } catch (error) {
                        this.showNotification('خطأ', 'حدث خطأ أثناء حفظ التغييرات', 'error');
                    } finally {
                        this.loading = false;
                    }
                },
                
                changeAvatar() {
                    // محاكاة تغيير الصورة الشخصية
                    const newAvatar = `https://ui-avatars.com/api/?name=${encodeURIComponent(this.user.name)}&background=${Math.floor(Math.random()*16777215).toString(16)}&color=ffffff`;
                    this.user.avatar = newAvatar;
                    localStorage.setItem('sanadan_user', JSON.stringify(this.user));
                    this.showNotification('تم التحديث', 'تم تغيير الصورة الشخصية', 'success');
                },
                
                formatDate(dateString) {
                    if (!dateString) return 'غير محدد';
                    return new Date(dateString).toLocaleDateString('ar-KW', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                    });
                },
                
                getTrialDaysLeft() {
                    if (!this.user.trialEndsAt) return 0;
                    const trialEnd = new Date(this.user.trialEndsAt);
                    const now = new Date();
                    const diffTime = trialEnd - now;
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    return Math.max(0, diffDays);
                },
                
                showNotification(title, message, type = 'info') {
                    const notification = document.createElement('div');
                    notification.className = `bg-white border-r-4 border-${type === 'success' ? 'green' : type === 'error' ? 'red' : 'blue'}-500 rounded-lg shadow-lg p-4 mb-2 fade-in`;
                    notification.innerHTML = `
                        <div class="flex items-center">
                            <i class="fas fa-${type === 'success' ? 'check-circle text-green-500' : type === 'error' ? 'exclamation-circle text-red-500' : 'info-circle text-blue-500'} ml-3"></i>
                            <div>
                                <p class="font-semibold text-gray-800">${title}</p>
                                <p class="text-sm text-gray-600">${message}</p>
                            </div>
                            <button onclick="this.parentElement.parentElement.remove()" class="mr-auto text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    `;
                    
                    document.getElementById('notifications').appendChild(notification);
                    
                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 5000);
                },
                
                logout() {
                    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
                        localStorage.removeItem('sanadan_user');
                        window.location.href = 'login.html';
                    }
                }
            }
        }
    </script>
</body>
</html>

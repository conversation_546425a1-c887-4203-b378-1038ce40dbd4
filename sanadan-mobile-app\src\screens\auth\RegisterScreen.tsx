import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  Checkbox,
  SegmentedButtons,
  HelperText,
} from 'react-native-paper';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

// المكونات والخدمات
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import LoadingSpinner from '../../components/LoadingSpinner';

interface RegisterFormValues {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
  phone: string;
  lawFirmName: string;
  licenseNumber: string;
  subscriptionPlan: 'basic' | 'professional' | 'enterprise';
  agreeToTerms: boolean;
  agreeToPrivacy: boolean;
}

const registerSchema = Yup.object().shape({
  name: Yup.string()
    .min(2, 'الاسم يجب أن يكون حرفين على الأقل')
    .required('الاسم مطلوب'),
  email: Yup.string()
    .email('البريد الإلكتروني غير صحيح')
    .required('البريد الإلكتروني مطلوب'),
  password: Yup.string()
    .min(8, 'كلمة المرور يجب أن تكون 8 أحرف على الأقل')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'كلمة المرور يجب أن تحتوي على حرف كبير وصغير ورقم')
    .required('كلمة المرور مطلوبة'),
  confirmPassword: Yup.string()
    .oneOf([Yup.ref('password')], 'كلمات المرور غير متطابقة')
    .required('تأكيد كلمة المرور مطلوب'),
  phone: Yup.string()
    .matches(/^(\+965)?[0-9]{8}$/, 'رقم الهاتف غير صحيح')
    .required('رقم الهاتف مطلوب'),
  lawFirmName: Yup.string()
    .min(2, 'اسم المكتب يجب أن يكون حرفين على الأقل')
    .required('اسم مكتب المحاماة مطلوب'),
  licenseNumber: Yup.string()
    .required('رقم الترخيص مطلوب'),
  agreeToTerms: Yup.boolean()
    .oneOf([true], 'يجب الموافقة على شروط الاستخدام'),
  agreeToPrivacy: Yup.boolean()
    .oneOf([true], 'يجب الموافقة على سياسة الخصوصية'),
});

export default function RegisterScreen() {
  const navigation = useNavigation();
  const { register } = useAuth();
  const { theme } = useTheme();
  const { t } = useLanguage();

  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const subscriptionPlans = [
    { value: 'basic', label: 'أساسية - 50 د.ك/شهر' },
    { value: 'professional', label: 'متقدمة - 100 د.ك/شهر' },
    { value: 'enterprise', label: 'مؤسسية - 200 د.ك/شهر' },
  ];

  const handleRegister = async (values: RegisterFormValues) => {
    try {
      setIsLoading(true);
      await register({
        name: values.name,
        email: values.email,
        password: values.password,
        phone: values.phone,
        lawFirmName: values.lawFirmName,
        licenseNumber: values.licenseNumber,
        subscriptionPlan: values.subscriptionPlan,
      });
    } catch (error: any) {
      Alert.alert(
        'خطأ في التسجيل',
        error.message || 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
        [{ text: 'موافق' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogin = () => {
    navigation.navigate('Login');
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* الشعار والعنوان */}
        <View style={styles.header}>
          <Icon
            name="scale-balance"
            size={60}
            color={theme.colors.primary}
            style={styles.logo}
          />
          <Text style={[styles.title, { color: theme.colors.onBackground }]}>
            إنشاء حساب جديد
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
            انضم إلى منصة سندان وابدأ إدارة مكتبك بكفاءة
          </Text>
        </View>

        {/* نموذج التسجيل */}
        <Card style={styles.registerCard}>
          <Card.Content>
            <Formik
              initialValues={{
                name: '',
                email: '',
                password: '',
                confirmPassword: '',
                phone: '',
                lawFirmName: '',
                licenseNumber: '',
                subscriptionPlan: 'basic' as const,
                agreeToTerms: false,
                agreeToPrivacy: false,
              }}
              validationSchema={registerSchema}
              onSubmit={handleRegister}
            >
              {({
                handleChange,
                handleBlur,
                handleSubmit,
                values,
                errors,
                touched,
                setFieldValue,
              }) => (
                <View style={styles.form}>
                  {/* الاسم الكامل */}
                  <TextInput
                    label="الاسم الكامل"
                    value={values.name}
                    onChangeText={handleChange('name')}
                    onBlur={handleBlur('name')}
                    error={touched.name && !!errors.name}
                    autoCapitalize="words"
                    textContentType="name"
                    left={<TextInput.Icon icon="account" />}
                    style={styles.input}
                  />
                  {touched.name && errors.name && (
                    <HelperText type="error">{errors.name}</HelperText>
                  )}

                  {/* البريد الإلكتروني */}
                  <TextInput
                    label="البريد الإلكتروني"
                    value={values.email}
                    onChangeText={handleChange('email')}
                    onBlur={handleBlur('email')}
                    error={touched.email && !!errors.email}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoComplete="email"
                    textContentType="emailAddress"
                    left={<TextInput.Icon icon="email" />}
                    style={styles.input}
                  />
                  {touched.email && errors.email && (
                    <HelperText type="error">{errors.email}</HelperText>
                  )}

                  {/* رقم الهاتف */}
                  <TextInput
                    label="رقم الهاتف"
                    value={values.phone}
                    onChangeText={handleChange('phone')}
                    onBlur={handleBlur('phone')}
                    error={touched.phone && !!errors.phone}
                    keyboardType="phone-pad"
                    textContentType="telephoneNumber"
                    placeholder="+965 XXXX XXXX"
                    left={<TextInput.Icon icon="phone" />}
                    style={styles.input}
                  />
                  {touched.phone && errors.phone && (
                    <HelperText type="error">{errors.phone}</HelperText>
                  )}

                  {/* اسم مكتب المحاماة */}
                  <TextInput
                    label="اسم مكتب المحاماة"
                    value={values.lawFirmName}
                    onChangeText={handleChange('lawFirmName')}
                    onBlur={handleBlur('lawFirmName')}
                    error={touched.lawFirmName && !!errors.lawFirmName}
                    autoCapitalize="words"
                    left={<TextInput.Icon icon="office-building" />}
                    style={styles.input}
                  />
                  {touched.lawFirmName && errors.lawFirmName && (
                    <HelperText type="error">{errors.lawFirmName}</HelperText>
                  )}

                  {/* رقم الترخيص */}
                  <TextInput
                    label="رقم ترخيص المحاماة"
                    value={values.licenseNumber}
                    onChangeText={handleChange('licenseNumber')}
                    onBlur={handleBlur('licenseNumber')}
                    error={touched.licenseNumber && !!errors.licenseNumber}
                    left={<TextInput.Icon icon="certificate" />}
                    style={styles.input}
                  />
                  {touched.licenseNumber && errors.licenseNumber && (
                    <HelperText type="error">{errors.licenseNumber}</HelperText>
                  )}

                  {/* كلمة المرور */}
                  <TextInput
                    label="كلمة المرور"
                    value={values.password}
                    onChangeText={handleChange('password')}
                    onBlur={handleBlur('password')}
                    error={touched.password && !!errors.password}
                    secureTextEntry={!showPassword}
                    autoComplete="password-new"
                    textContentType="newPassword"
                    left={<TextInput.Icon icon="lock" />}
                    right={
                      <TextInput.Icon
                        icon={showPassword ? 'eye-off' : 'eye'}
                        onPress={() => setShowPassword(!showPassword)}
                      />
                    }
                    style={styles.input}
                  />
                  {touched.password && errors.password && (
                    <HelperText type="error">{errors.password}</HelperText>
                  )}

                  {/* تأكيد كلمة المرور */}
                  <TextInput
                    label="تأكيد كلمة المرور"
                    value={values.confirmPassword}
                    onChangeText={handleChange('confirmPassword')}
                    onBlur={handleBlur('confirmPassword')}
                    error={touched.confirmPassword && !!errors.confirmPassword}
                    secureTextEntry={!showConfirmPassword}
                    autoComplete="password-new"
                    textContentType="newPassword"
                    left={<TextInput.Icon icon="lock-check" />}
                    right={
                      <TextInput.Icon
                        icon={showConfirmPassword ? 'eye-off' : 'eye'}
                        onPress={() => setShowConfirmPassword(!showConfirmPassword)}
                      />
                    }
                    style={styles.input}
                  />
                  {touched.confirmPassword && errors.confirmPassword && (
                    <HelperText type="error">{errors.confirmPassword}</HelperText>
                  )}

                  {/* خطة الاشتراك */}
                  <Text style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                    اختر خطة الاشتراك
                  </Text>
                  <SegmentedButtons
                    value={values.subscriptionPlan}
                    onValueChange={(value) => setFieldValue('subscriptionPlan', value)}
                    buttons={subscriptionPlans}
                    style={styles.segmentedButtons}
                  />

                  {/* الموافقات */}
                  <View style={styles.agreementSection}>
                    <View style={styles.checkboxContainer}>
                      <Checkbox
                        status={values.agreeToTerms ? 'checked' : 'unchecked'}
                        onPress={() => setFieldValue('agreeToTerms', !values.agreeToTerms)}
                      />
                      <Text style={[styles.checkboxLabel, { color: theme.colors.onSurface }]}>
                        أوافق على{' '}
                        <Text style={{ color: theme.colors.primary }}>شروط الاستخدام</Text>
                      </Text>
                    </View>
                    {touched.agreeToTerms && errors.agreeToTerms && (
                      <HelperText type="error">{errors.agreeToTerms}</HelperText>
                    )}

                    <View style={styles.checkboxContainer}>
                      <Checkbox
                        status={values.agreeToPrivacy ? 'checked' : 'unchecked'}
                        onPress={() => setFieldValue('agreeToPrivacy', !values.agreeToPrivacy)}
                      />
                      <Text style={[styles.checkboxLabel, { color: theme.colors.onSurface }]}>
                        أوافق على{' '}
                        <Text style={{ color: theme.colors.primary }}>سياسة الخصوصية</Text>
                      </Text>
                    </View>
                    {touched.agreeToPrivacy && errors.agreeToPrivacy && (
                      <HelperText type="error">{errors.agreeToPrivacy}</HelperText>
                    )}
                  </View>

                  {/* زر التسجيل */}
                  <Button
                    mode="contained"
                    onPress={handleSubmit}
                    style={styles.registerButton}
                    contentStyle={styles.buttonContent}
                    disabled={isLoading}
                  >
                    إنشاء الحساب
                  </Button>
                </View>
              )}
            </Formik>
          </Card.Content>
        </Card>

        {/* رابط تسجيل الدخول */}
        <View style={styles.loginContainer}>
          <Text style={[styles.loginText, { color: theme.colors.onSurfaceVariant }]}>
            لديك حساب بالفعل؟
          </Text>
          <Button mode="text" onPress={handleLogin} compact>
            تسجيل الدخول
          </Button>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 30,
  },
  logo: {
    marginBottom: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.7,
  },
  registerCard: {
    marginBottom: 20,
  },
  form: {
    gap: 16,
  },
  input: {
    backgroundColor: 'transparent',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 8,
    marginBottom: 8,
  },
  segmentedButtons: {
    marginBottom: 16,
  },
  agreementSection: {
    marginVertical: 16,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  checkboxLabel: {
    marginLeft: 8,
    fontSize: 14,
    flex: 1,
  },
  registerButton: {
    marginTop: 16,
  },
  buttonContent: {
    paddingVertical: 8,
  },
  loginContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  loginText: {
    fontSize: 16,
  },
});

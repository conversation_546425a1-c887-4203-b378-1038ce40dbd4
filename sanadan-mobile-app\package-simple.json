{"name": "sanadan-mobile-app", "version": "1.0.0", "description": "منصة سندان لأعمال المحاماة - تطبيق جوال", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~50.0.0", "react": "18.2.0", "react-native": "0.73.0", "@expo/vector-icons": "^14.0.0", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "react-native-screens": "~3.29.0", "react-native-safe-area-context": "4.8.2", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.2", "expo-status-bar": "~1.11.1", "expo-font": "~11.10.2", "expo-splash-screen": "~0.26.4", "expo-constants": "~15.4.5", "expo-secure-store": "~12.8.1", "react-native-paper": "^5.11.6", "react-native-vector-icons": "^10.0.3", "react-native-svg": "14.1.0", "react-query": "^3.39.3", "axios": "^1.6.2", "formik": "^2.4.5", "yup": "^1.4.0", "date-fns": "^3.0.6", "@react-native-async-storage/async-storage": "^1.21.0"}, "devDependencies": {"@babel/core": "^7.23.6", "@types/react": "~18.2.45", "@types/react-native": "~0.73.0", "typescript": "^5.3.3"}, "keywords": ["react-native", "expo", "legal", "law", "kuwait", "saas", "mobile"], "author": "Sanadan Platform Team", "license": "MIT", "private": true}
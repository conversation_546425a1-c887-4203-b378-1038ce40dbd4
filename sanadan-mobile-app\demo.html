<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة سندان - تطبيق جوال لإدارة مكاتب المحاماة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        .phone-frame {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1f2937, #374151);
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            position: relative;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #f9fafb;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .notch {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 30px;
            background: #000;
            border-radius: 0 0 20px 20px;
            z-index: 10;
        }
        .status-bar {
            height: 44px;
            background: #3b82f6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .app-content {
            height: calc(100% - 44px - 80px);
            overflow-y: auto;
            background: #f9fafb;
        }
        .bottom-nav {
            height: 80px;
            background: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 10px 0;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .nav-item.active {
            color: #3b82f6;
        }
        .nav-item:hover {
            color: #3b82f6;
        }
        .card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin: 8px 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin: 16px;
        }
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: .5; }
        }
        .slide-in {
            animation: slideIn 0.5s ease-out;
        }
        @keyframes slideIn {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
    </style>
</head>
<body class="bg-gray-100 font-arabic min-h-screen flex items-center justify-center p-8">
    <!-- معلومات التطبيق -->
    <div class="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <!-- معلومات التطبيق -->
        <div class="space-y-8">
            <div class="text-center lg:text-right">
                <div class="flex items-center justify-center lg:justify-start gap-4 mb-6">
                    <div class="w-16 h-16 bg-primary-600 rounded-2xl flex items-center justify-center">
                        <i class="fas fa-balance-scale text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">منصة سندان</h1>
                        <p class="text-primary-600 font-semibold">تطبيق جوال متطور</p>
                    </div>
                </div>
                <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                    نظام إدارة مكاتب المحاماة
                    <span class="block text-primary-600">الأكثر تطوراً في الكويت</span>
                </h2>
                <p class="text-xl text-gray-600 mb-8">
                    تطبيق جوال شامل لإدارة القضايا والعملاء والجلسات والمستندات والنظام المالي بطريقة احترافية ومتطورة
                </p>
            </div>

            <!-- الميزات الرئيسية -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="flex items-start gap-4">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-folder text-blue-600"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-1">إدارة القضايا</h3>
                        <p class="text-gray-600 text-sm">تتبع شامل لجميع القضايا مع إدارة المواعيد والجلسات</p>
                    </div>
                </div>

                <div class="flex items-start gap-4">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-users text-green-600"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-1">إدارة العملاء</h3>
                        <p class="text-gray-600 text-sm">ملفات شاملة للعملاء مع تتبع التفاعلات</p>
                    </div>
                </div>

                <div class="flex items-start gap-4">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-dollar-sign text-yellow-600"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-1">النظام المالي</h3>
                        <p class="text-gray-600 text-sm">إدارة الفواتير والمدفوعات مع دعم K-Net</p>
                    </div>
                </div>

                <div class="flex items-start gap-4">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-calendar text-purple-600"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-1">إدارة المواعيد</h3>
                        <p class="text-gray-600 text-sm">تقويم تفاعلي مع تنبيهات ذكية</p>
                    </div>
                </div>

                <div class="flex items-start gap-4">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-file-alt text-red-600"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-1">إدارة المستندات</h3>
                        <p class="text-gray-600 text-sm">تخزين آمن ومنظم مع بحث متقدم</p>
                    </div>
                </div>

                <div class="flex items-start gap-4">
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-robot text-indigo-600"></i>
                    </div>
                    <div>
                        <h3 class="font-semibold text-gray-900 mb-1">الذكاء الاصطناعي</h3>
                        <p class="text-gray-600 text-sm">مساعد قانوني ذكي وإنشاء مستندات تلقائي</p>
                    </div>
                </div>
            </div>

            <!-- التقنيات المستخدمة -->
            <div class="bg-white rounded-2xl p-6 shadow-lg">
                <h3 class="text-xl font-bold text-gray-900 mb-4">التقنيات المستخدمة</h3>
                <div class="flex flex-wrap gap-3">
                    <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">React Native</span>
                    <span class="px-3 py-1 bg-purple-100 text-purple-800 rounded-full text-sm font-medium">Expo</span>
                    <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">TypeScript</span>
                    <span class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">React Navigation</span>
                    <span class="px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">React Query</span>
                    <span class="px-3 py-1 bg-indigo-100 text-indigo-800 rounded-full text-sm font-medium">Material Design</span>
                </div>
            </div>
        </div>

        <!-- محاكي الهاتف -->
        <div class="flex justify-center">
            <div class="phone-frame">
                <div class="notch"></div>
                <div class="phone-screen">
                    <!-- شريط الحالة -->
                    <div class="status-bar">
                        <div class="flex items-center gap-2">
                            <span id="current-time">9:41</span>
                        </div>
                        <div class="flex items-center gap-1">
                            <i class="fas fa-signal text-sm"></i>
                            <i class="fas fa-wifi text-sm"></i>
                            <i class="fas fa-battery-three-quarters text-sm"></i>
                        </div>
                    </div>

                    <!-- محتوى التطبيق -->
                    <div class="app-content" id="app-content">
                        <!-- لوحة التحكم -->
                        <div id="dashboard-screen" class="screen active">
                            <!-- ترحيب -->
                            <div class="card bg-gradient-to-r from-primary-600 to-primary-700 text-white slide-in">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-primary-100 text-sm">مرحباً بك</p>
                                        <h2 class="text-xl font-bold">أحمد المحامي</h2>
                                        <p class="text-primary-100 text-sm">مكتب المحاماة الرائد</p>
                                    </div>
                                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- الإحصائيات -->
                            <div class="stats-grid">
                                <div class="stat-card slide-in" style="animation-delay: 0.1s">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-folder text-blue-600 text-sm"></i>
                                    </div>
                                    <div class="text-2xl font-bold text-gray-900" id="total-cases">127</div>
                                    <div class="text-sm text-gray-600">إجمالي القضايا</div>
                                </div>

                                <div class="stat-card slide-in" style="animation-delay: 0.2s">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-folder-open text-green-600 text-sm"></i>
                                    </div>
                                    <div class="text-2xl font-bold text-gray-900" id="active-cases">89</div>
                                    <div class="text-sm text-gray-600">القضايا النشطة</div>
                                </div>

                                <div class="stat-card slide-in" style="animation-delay: 0.3s">
                                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-users text-yellow-600 text-sm"></i>
                                    </div>
                                    <div class="text-2xl font-bold text-gray-900" id="total-clients">245</div>
                                    <div class="text-sm text-gray-600">العملاء</div>
                                </div>

                                <div class="stat-card slide-in" style="animation-delay: 0.4s">
                                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-calendar text-purple-600 text-sm"></i>
                                    </div>
                                    <div class="text-2xl font-bold text-gray-900" id="upcoming-hearings">12</div>
                                    <div class="text-sm text-gray-600">جلسات قادمة</div>
                                </div>
                            </div>

                            <!-- الجلسات القادمة -->
                            <div class="card slide-in" style="animation-delay: 0.5s">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="font-semibold text-gray-900">الجلسات القادمة</h3>
                                    <button class="text-primary-600 text-sm">عرض الكل</button>
                                </div>
                                <div class="space-y-3">
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <div class="font-medium text-gray-900">قضية التجارة الإلكترونية</div>
                                            <div class="text-sm text-gray-500">محكمة الكويت الكلية</div>
                                        </div>
                                        <div class="text-sm text-primary-600 font-medium">غداً 10:00 ص</div>
                                    </div>
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <div>
                                            <div class="font-medium text-gray-900">قضية العقار التجاري</div>
                                            <div class="text-sm text-gray-500">محكمة الجهراء</div>
                                        </div>
                                        <div class="text-sm text-primary-600 font-medium">الأحد 2:30 م</div>
                                    </div>
                                </div>
                            </div>

                            <!-- المهام العاجلة -->
                            <div class="card slide-in" style="animation-delay: 0.6s">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="font-semibold text-gray-900">المهام العاجلة</h3>
                                    <button class="text-primary-600 text-sm">عرض الكل</button>
                                </div>
                                <div class="space-y-3">
                                    <div class="flex items-center p-3 bg-red-50 rounded-lg">
                                        <div class="w-4 h-4 border-2 border-red-500 rounded ml-3"></div>
                                        <div class="flex-1">
                                            <div class="font-medium text-gray-900">مراجعة عقد الشراكة</div>
                                            <div class="text-sm text-red-600">موعد الاستحقاق: اليوم</div>
                                        </div>
                                    </div>
                                    <div class="flex items-center p-3 bg-yellow-50 rounded-lg">
                                        <div class="w-4 h-4 border-2 border-yellow-500 rounded ml-3"></div>
                                        <div class="flex-1">
                                            <div class="font-medium text-gray-900">إعداد مذكرة دفاع</div>
                                            <div class="text-sm text-yellow-600">موعد الاستحقاق: غداً</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- شريط التنقل السفلي -->
                    <div class="bottom-nav">
                        <div class="nav-item active" onclick="switchScreen('dashboard')">
                            <i class="fas fa-home text-lg"></i>
                            <span class="text-xs">الرئيسية</span>
                        </div>
                        <div class="nav-item" onclick="switchScreen('cases')">
                            <i class="fas fa-folder text-lg"></i>
                            <span class="text-xs">القضايا</span>
                        </div>
                        <div class="nav-item" onclick="switchScreen('clients')">
                            <i class="fas fa-users text-lg"></i>
                            <span class="text-xs">العملاء</span>
                        </div>
                        <div class="nav-item" onclick="switchScreen('calendar')">
                            <i class="fas fa-calendar text-lg"></i>
                            <span class="text-xs">التقويم</span>
                        </div>
                        <div class="nav-item" onclick="switchScreen('more')">
                            <i class="fas fa-ellipsis-h text-lg"></i>
                            <span class="text-xs">المزيد</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-KW', { 
                hour: '2-digit', 
                minute: '2-digit',
                hour12: false 
            });
            document.getElementById('current-time').textContent = timeString;
        }

        // تحديث الإحصائيات بشكل متحرك
        function animateCounter(elementId, targetValue, duration = 2000) {
            const element = document.getElementById(elementId);
            const startValue = 0;
            const increment = targetValue / (duration / 16);
            let currentValue = startValue;

            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= targetValue) {
                    currentValue = targetValue;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(currentValue);
            }, 16);
        }

        // تبديل الشاشات
        function switchScreen(screenName) {
            // إزالة الفئة النشطة من جميع عناصر التنقل
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // إضافة الفئة النشطة للعنصر المحدد
            event.currentTarget.classList.add('active');
            
            // هنا يمكن إضافة منطق تبديل المحتوى
            console.log('تم التبديل إلى:', screenName);
        }

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);
            
            // تحريك الإحصائيات
            setTimeout(() => {
                animateCounter('total-cases', 127);
                animateCounter('active-cases', 89);
                animateCounter('total-clients', 245);
                animateCounter('upcoming-hearings', 12);
            }, 1000);
        });

        // إضافة تأثيرات تفاعلية
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);
            });
        });
    </script>
</body>
</html>

<template>
  <aside 
    class="fixed inset-y-0 end-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out"
    :class="{ 'translate-x-0': isOpen, 'translate-x-full': !isOpen }"
  >
    <!-- رأس الشريط الجانبي -->
    <div class="flex items-center justify-between h-16 px-6 border-b border-gray-200">
      <div class="flex items-center">
        <img 
          src="/images/logo.svg" 
          alt="منصة سندان" 
          class="h-8 w-auto"
        >
        <span class="ms-3 text-xl font-bold text-primary-600">سندان</span>
      </div>
      
      <!-- زر إغلاق الشريط الجانبي (للشاشات الصغيرة) -->
      <button 
        @click="$emit('close')"
        class="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100"
      >
        <XMarkIcon class="h-5 w-5" />
      </button>
    </div>

    <!-- معلومات المستخدم -->
    <div class="p-6 border-b border-gray-200">
      <div class="flex items-center">
        <Avatar 
          :src="user.avatar_url" 
          :name="user.name"
          size="md"
          class="flex-shrink-0"
        />
        <div class="ms-3 min-w-0 flex-1">
          <p class="text-sm font-medium text-gray-900 truncate">
            {{ user.name }}
          </p>
          <p class="text-xs text-gray-500 truncate">
            {{ getUserRoleLabel(user.user_type) }}
          </p>
        </div>
      </div>
    </div>

    <!-- قائمة التنقل -->
    <nav class="flex-1 px-4 py-6 space-y-1 overflow-y-auto">
      <!-- لوحة التحكم -->
      <SidebarLink 
        :href="route('dashboard')"
        :active="currentRoute === '/dashboard'"
        icon="ChartBarIcon"
      >
        لوحة التحكم
      </SidebarLink>

      <!-- القضايا -->
      <SidebarGroup 
        title="إدارة القضايا"
        :expanded="expandedGroups.cases"
        @toggle="toggleGroup('cases')"
      >
        <SidebarLink 
          :href="route('cases.index')"
          :active="currentRoute.startsWith('/cases')"
          icon="FolderIcon"
          :badge="caseStats.active"
        >
          القضايا
        </SidebarLink>
        
        <SidebarLink 
          :href="route('cases.create')"
          icon="PlusIcon"
          class="ms-4"
        >
          قضية جديدة
        </SidebarLink>
        
        <SidebarLink 
          :href="route('hearings.index')"
          :active="currentRoute.startsWith('/hearings')"
          icon="CalendarIcon"
          :badge="hearingStats.upcoming"
        >
          الجلسات
        </SidebarLink>
        
        <SidebarLink 
          :href="route('tasks.index')"
          :active="currentRoute.startsWith('/tasks')"
          icon="CheckCircleIcon"
          :badge="taskStats.pending"
        >
          المهام
        </SidebarLink>
      </SidebarGroup>

      <!-- العملاء -->
      <SidebarGroup 
        title="إدارة العملاء"
        :expanded="expandedGroups.clients"
        @toggle="toggleGroup('clients')"
      >
        <SidebarLink 
          :href="route('clients.index')"
          :active="currentRoute.startsWith('/clients')"
          icon="UsersIcon"
          :badge="clientStats.total"
        >
          العملاء
        </SidebarLink>
        
        <SidebarLink 
          :href="route('clients.create')"
          icon="UserPlusIcon"
          class="ms-4"
        >
          عميل جديد
        </SidebarLink>
      </SidebarGroup>

      <!-- المستندات -->
      <SidebarLink 
        :href="route('documents.index')"
        :active="currentRoute.startsWith('/documents')"
        icon="DocumentIcon"
      >
        المستندات
      </SidebarLink>

      <!-- المالية -->
      <SidebarGroup 
        title="الإدارة المالية"
        :expanded="expandedGroups.finance"
        @toggle="toggleGroup('finance')"
      >
        <SidebarLink 
          :href="route('invoices.index')"
          :active="currentRoute.startsWith('/invoices')"
          icon="DocumentTextIcon"
          :badge="invoiceStats.pending"
        >
          الفواتير
        </SidebarLink>
        
        <SidebarLink 
          :href="route('payments.index')"
          :active="currentRoute.startsWith('/payments')"
          icon="CreditCardIcon"
        >
          المدفوعات
        </SidebarLink>
        
        <SidebarLink 
          :href="route('reports.financial')"
          icon="ChartPieIcon"
        >
          التقارير المالية
        </SidebarLink>
      </SidebarGroup>

      <!-- التقويم -->
      <SidebarLink 
        :href="route('calendar.index')"
        :active="currentRoute.startsWith('/calendar')"
        icon="CalendarDaysIcon"
      >
        التقويم
      </SidebarLink>

      <!-- التقارير -->
      <SidebarLink 
        :href="route('reports.index')"
        :active="currentRoute.startsWith('/reports')"
        icon="DocumentChartBarIcon"
      >
        التقارير
      </SidebarLink>

      <!-- الإدارة (للمديرين فقط) -->
      <SidebarGroup 
        v-if="canManageUsers"
        title="الإدارة"
        :expanded="expandedGroups.admin"
        @toggle="toggleGroup('admin')"
      >
        <SidebarLink 
          :href="route('users.index')"
          :active="currentRoute.startsWith('/users')"
          icon="UserGroupIcon"
        >
          المستخدمون
        </SidebarLink>
        
        <SidebarLink 
          :href="route('settings.index')"
          :active="currentRoute.startsWith('/settings')"
          icon="CogIcon"
        >
          الإعدادات
        </SidebarLink>
      </SidebarGroup>
    </nav>

    <!-- أسفل الشريط الجانبي -->
    <div class="p-4 border-t border-gray-200">
      <!-- إحصائيات سريعة -->
      <div class="grid grid-cols-2 gap-2 mb-4">
        <div class="text-center p-2 bg-primary-50 rounded-lg">
          <div class="text-lg font-semibold text-primary-600">
            {{ caseStats.active }}
          </div>
          <div class="text-xs text-primary-500">قضايا نشطة</div>
        </div>
        
        <div class="text-center p-2 bg-yellow-50 rounded-lg">
          <div class="text-lg font-semibold text-yellow-600">
            {{ taskStats.pending }}
          </div>
          <div class="text-xs text-yellow-500">مهام معلقة</div>
        </div>
      </div>

      <!-- روابط سريعة -->
      <div class="space-y-1">
        <button 
          @click="openQuickCreate"
          class="w-full flex items-center px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors"
        >
          <PlusIcon class="h-4 w-4 me-2" />
          إنشاء سريع
        </button>
        
        <button 
          @click="openQuickSearch"
          class="w-full flex items-center px-3 py-2 text-sm text-gray-600 hover:text-primary-600 hover:bg-primary-50 rounded-md transition-colors"
        >
          <MagnifyingGlassIcon class="h-4 w-4 me-2" />
          بحث سريع
        </button>
      </div>
    </div>
  </aside>

  <!-- خلفية مظلمة للشاشات الصغيرة -->
  <div 
    v-if="isOpen"
    @click="$emit('close')"
    class="fixed inset-0 z-40 bg-black bg-opacity-50 md:hidden"
  ></div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { usePage } from '@inertiajs/vue3'
import {
  XMarkIcon,
  ChartBarIcon,
  FolderIcon,
  PlusIcon,
  CalendarIcon,
  CheckCircleIcon,
  UsersIcon,
  UserPlusIcon,
  DocumentIcon,
  DocumentTextIcon,
  CreditCardIcon,
  ChartPieIcon,
  CalendarDaysIcon,
  DocumentChartBarIcon,
  UserGroupIcon,
  CogIcon,
  MagnifyingGlassIcon
} from '@heroicons/vue/24/outline'

import Avatar from '@/Components/Avatar.vue'
import SidebarLink from '@/Components/SidebarLink.vue'
import SidebarGroup from '@/Components/SidebarGroup.vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  user: {
    type: Object,
    required: true
  },
  currentRoute: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['close'])

const page = usePage()

// حالة المجموعات المتوسعة
const expandedGroups = ref({
  cases: true,
  clients: false,
  finance: false,
  admin: false
})

// إحصائيات سريعة
const caseStats = ref({ active: 0, total: 0 })
const taskStats = ref({ pending: 0, total: 0 })
const clientStats = ref({ total: 0, active: 0 })
const invoiceStats = ref({ pending: 0, overdue: 0 })
const hearingStats = ref({ upcoming: 0, today: 0 })

// الصلاحيات
const canManageUsers = computed(() => {
  return page.props.auth.permissions.includes('manage_users') || 
         props.user.user_type === 'admin'
})

// وظائف المساعدة
const getUserRoleLabel = (userType) => {
  const roles = {
    admin: 'مدير النظام',
    lawyer: 'محامي',
    employee: 'موظف',
    client: 'عميل'
  }
  return roles[userType] || userType
}

const toggleGroup = (groupName) => {
  expandedGroups.value[groupName] = !expandedGroups.value[groupName]
  // حفظ الحالة في localStorage
  localStorage.setItem('sidebarGroups', JSON.stringify(expandedGroups.value))
}

const openQuickCreate = () => {
  document.dispatchEvent(new CustomEvent('open-quick-create'))
}

const openQuickSearch = () => {
  document.dispatchEvent(new CustomEvent('open-quick-search'))
}

// تحميل الإحصائيات
const loadStats = async () => {
  try {
    const response = await axios.get('/api/dashboard/stats')
    const stats = response.data
    
    caseStats.value = {
      active: stats.active_cases || 0,
      total: stats.total_cases || 0
    }
    
    taskStats.value = {
      pending: stats.pending_tasks || 0,
      total: stats.total_tasks || 0
    }
    
    clientStats.value = {
      total: stats.total_clients || 0,
      active: stats.active_clients || 0
    }
    
    invoiceStats.value = {
      pending: stats.pending_invoices || 0,
      overdue: stats.overdue_invoices || 0
    }
    
    hearingStats.value = {
      upcoming: stats.upcoming_hearings || 0,
      today: stats.today_hearings || 0
    }
  } catch (error) {
    console.error('خطأ في تحميل الإحصائيات:', error)
  }
}

// تحميل البيانات عند تحميل المكون
onMounted(() => {
  // استرجاع حالة المجموعات المحفوظة
  const savedGroups = localStorage.getItem('sidebarGroups')
  if (savedGroups) {
    try {
      expandedGroups.value = { ...expandedGroups.value, ...JSON.parse(savedGroups) }
    } catch (error) {
      console.error('خطأ في استرجاع حالة المجموعات:', error)
    }
  }
  
  // تحميل الإحصائيات
  loadStats()
  
  // تحديث الإحصائيات كل 5 دقائق
  setInterval(loadStats, 300000)
})
</script>

<style scoped>
/* تحسينات إضافية للشريط الجانبي */
.sidebar-transition {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسين عرض الشريط الجانبي على الشاشات المختلفة */
@media (max-width: 768px) {
  aside {
    width: 100%;
    max-width: 320px;
  }
}

/* تحسين شريط التمرير */
nav {
  scrollbar-width: thin;
  scrollbar-color: rgba(156, 163, 175, 0.5) transparent;
}

nav::-webkit-scrollbar {
  width: 4px;
}

nav::-webkit-scrollbar-track {
  background: transparent;
}

nav::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 2px;
}

nav::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}
</style>

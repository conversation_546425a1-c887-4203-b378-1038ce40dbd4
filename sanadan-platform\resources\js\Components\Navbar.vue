<template>
  <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
    <div class="px-6">
      <div class="flex justify-between h-16">
        <!-- الجانب الأيمن -->
        <div class="flex items-center">
          <!-- زر تبديل الشريط الجانبي -->
          <button
            @click="$emit('toggle-sidebar')"
            class="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <Bars3Icon class="h-6 w-6" />
          </button>

          <!-- شريط البحث السريع -->
          <div class="ms-4 relative">
            <div class="relative">
              <MagnifyingGlassIcon class="absolute start-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="بحث سريع... (Ctrl+K)"
                class="w-64 ps-10 pe-4 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                @focus="openQuickSearch"
                readonly
              />
              <kbd class="absolute end-3 top-1/2 transform -translate-y-1/2 px-2 py-1 text-xs text-gray-500 bg-gray-100 rounded">
                ⌘K
              </kbd>
            </div>
          </div>
        </div>

        <!-- الجانب الأيسر -->
        <div class="flex items-center space-x-4 space-x-reverse">
          <!-- زر الإنشاء السريع -->
          <button
            @click="openQuickCreate"
            class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
          >
            <PlusIcon class="h-4 w-4 me-1" />
            إنشاء
          </button>

          <!-- الإشعارات -->
          <div class="relative">
            <button
              @click="toggleNotifications"
              class="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500 relative"
            >
              <BellIcon class="h-6 w-6" />
              <!-- مؤشر الإشعارات الجديدة -->
              <span
                v-if="unreadNotificationsCount > 0"
                class="absolute -top-1 -end-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"
              >
                {{ unreadNotificationsCount > 9 ? '9+' : unreadNotificationsCount }}
              </span>
            </button>

            <!-- قائمة الإشعارات المنسدلة -->
            <div
              v-if="notificationsOpen"
              class="absolute end-0 mt-2 w-80 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 z-50"
            >
              <div class="p-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-medium text-gray-900">الإشعارات</h3>
                  <button
                    v-if="unreadNotificationsCount > 0"
                    @click="markAllAsRead"
                    class="text-sm text-primary-600 hover:text-primary-700"
                  >
                    تحديد الكل كمقروء
                  </button>
                </div>
              </div>

              <div class="max-h-96 overflow-y-auto">
                <div v-if="notifications.length === 0" class="p-4 text-center text-gray-500">
                  لا توجد إشعارات
                </div>

                <div v-else>
                  <div
                    v-for="notification in notifications.slice(0, 10)"
                    :key="notification.id"
                    class="p-4 border-b border-gray-100 hover:bg-gray-50 cursor-pointer"
                    :class="{ 'bg-blue-50': !notification.read_at }"
                    @click="handleNotificationClick(notification)"
                  >
                    <div class="flex items-start">
                      <div class="flex-shrink-0">
                        <div
                          class="h-8 w-8 rounded-full flex items-center justify-center"
                          :class="getNotificationIconClass(notification.type)"
                        >
                          <component :is="getNotificationIcon(notification.type)" class="h-4 w-4" />
                        </div>
                      </div>
                      <div class="ms-3 flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900">
                          {{ notification.data.title }}
                        </p>
                        <p class="text-sm text-gray-500 mt-1">
                          {{ notification.data.message }}
                        </p>
                        <p class="text-xs text-gray-400 mt-1">
                          {{ formatTimeAgo(notification.created_at) }}
                        </p>
                      </div>
                      <div v-if="!notification.read_at" class="flex-shrink-0">
                        <div class="h-2 w-2 bg-blue-500 rounded-full"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div v-if="notifications.length > 10" class="p-4 border-t border-gray-200">
                <Link
                  href="/notifications"
                  class="block text-center text-sm text-primary-600 hover:text-primary-700"
                >
                  عرض جميع الإشعارات
                </Link>
              </div>
            </div>
          </div>

          <!-- تبديل الوضع المظلم -->
          <button
            @click="toggleDarkMode"
            class="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
          >
            <SunIcon v-if="isDarkMode" class="h-6 w-6" />
            <MoonIcon v-else class="h-6 w-6" />
          </button>

          <!-- قائمة المستخدم -->
          <div class="relative">
            <button
              @click="toggleUserMenu"
              class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <Avatar :src="user.avatar_url" :name="user.name" size="sm" />
              <ChevronDownIcon class="ms-2 h-4 w-4 text-gray-400" />
            </button>

            <!-- قائمة المستخدم المنسدلة -->
            <div
              v-if="userMenuOpen"
              class="absolute end-0 mt-2 w-48 bg-white rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 z-50"
            >
              <div class="p-4 border-b border-gray-200">
                <p class="text-sm font-medium text-gray-900">{{ user.name }}</p>
                <p class="text-xs text-gray-500">{{ user.email }}</p>
              </div>

              <div class="py-1">
                <Link
                  href="/profile"
                  class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <UserIcon class="h-4 w-4 me-3" />
                  الملف الشخصي
                </Link>

                <Link
                  href="/settings"
                  class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <CogIcon class="h-4 w-4 me-3" />
                  الإعدادات
                </Link>

                <div class="border-t border-gray-100"></div>

                <button
                  @click="$emit('logout')"
                  class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <ArrowRightOnRectangleIcon class="h-4 w-4 me-3" />
                  تسجيل الخروج
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { Link } from '@inertiajs/vue3'
import {
  Bars3Icon,
  MagnifyingGlassIcon,
  PlusIcon,
  BellIcon,
  SunIcon,
  MoonIcon,
  ChevronDownIcon,
  UserIcon,
  CogIcon,
  ArrowRightOnRectangleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/vue/24/outline'

import Avatar from '@/Components/Avatar.vue'

const props = defineProps({
  user: {
    type: Object,
    required: true
  },
  notifications: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['toggle-sidebar', 'logout'])

// حالة القوائم المنسدلة
const notificationsOpen = ref(false)
const userMenuOpen = ref(false)

// الوضع المظلم
const isDarkMode = ref(false)

// الإشعارات غير المقروءة
const unreadNotificationsCount = computed(() => {
  return props.notifications.filter(n => !n.read_at).length
})

// وظائف القوائم المنسدلة
const toggleNotifications = () => {
  notificationsOpen.value = !notificationsOpen.value
  userMenuOpen.value = false
}

const toggleUserMenu = () => {
  userMenuOpen.value = !userMenuOpen.value
  notificationsOpen.value = false
}

// إغلاق القوائم عند النقر خارجها
const closeMenus = (event) => {
  if (!event.target.closest('.relative')) {
    notificationsOpen.value = false
    userMenuOpen.value = false
  }
}

// تبديل الوضع المظلم
const toggleDarkMode = () => {
  isDarkMode.value = !isDarkMode.value
  document.documentElement.classList.toggle('dark', isDarkMode.value)
  localStorage.setItem('theme', isDarkMode.value ? 'dark' : 'light')
}

// فتح البحث السريع
const openQuickSearch = () => {
  document.dispatchEvent(new CustomEvent('open-quick-search'))
}

// فتح الإنشاء السريع
const openQuickCreate = () => {
  document.dispatchEvent(new CustomEvent('open-quick-create'))
}

// معالجة النقر على الإشعار
const handleNotificationClick = async (notification) => {
  // تحديد الإشعار كمقروء
  if (!notification.read_at) {
    try {
      await axios.post(`/notifications/${notification.id}/mark-as-read`)
      notification.read_at = new Date().toISOString()
    } catch (error) {
      console.error('خطأ في تحديد الإشعار كمقروء:', error)
    }
  }

  // التنقل إلى الرابط المرتبط بالإشعار
  if (notification.data.url) {
    window.location.href = notification.data.url
  }

  notificationsOpen.value = false
}

// تحديد جميع الإشعارات كمقروءة
const markAllAsRead = async () => {
  try {
    await axios.post('/notifications/mark-all-as-read')
    props.notifications.forEach(notification => {
      notification.read_at = new Date().toISOString()
    })
  } catch (error) {
    console.error('خطأ في تحديد الإشعارات كمقروءة:', error)
  }
}

// الحصول على أيقونة الإشعار
const getNotificationIcon = (type) => {
  const icons = {
    success: CheckCircleIcon,
    error: XCircleIcon,
    warning: ExclamationTriangleIcon,
    info: InformationCircleIcon,
    default: BellIcon
  }
  return icons[type] || icons.default
}

// الحصول على فئة CSS لأيقونة الإشعار
const getNotificationIconClass = (type) => {
  const classes = {
    success: 'bg-green-100 text-green-600',
    error: 'bg-red-100 text-red-600',
    warning: 'bg-yellow-100 text-yellow-600',
    info: 'bg-blue-100 text-blue-600',
    default: 'bg-gray-100 text-gray-600'
  }
  return classes[type] || classes.default
}

// تنسيق الوقت النسبي
const formatTimeAgo = (date) => {
  return window.SanadanHelpers.timeAgo(date)
}

// تحميل الوضع المظلم المحفوظ
onMounted(() => {
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme) {
    isDarkMode.value = savedTheme === 'dark'
  } else {
    isDarkMode.value = window.matchMedia('(prefers-color-scheme: dark)').matches
  }

  // إضافة مستمع لإغلاق القوائم
  document.addEventListener('click', closeMenus)
})

// تنظيف المستمعات
onUnmounted(() => {
  document.removeEventListener('click', closeMenus)
})
</script>

<style scoped>
/* تحسينات إضافية للشريط العلوي */
.navbar-transition {
  transition: all 0.2s ease-in-out;
}

/* تحسين عرض شريط البحث على الشاشات الصغيرة */
@media (max-width: 768px) {
  .search-input {
    width: 200px;
  }
}

@media (max-width: 640px) {
  .search-input {
    display: none;
  }
}

/* تحسين القوائم المنسدلة */
.dropdown-enter-active,
.dropdown-leave-active {
  transition: all 0.2s ease;
}

.dropdown-enter-from,
.dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* تحسين مؤشر الإشعارات */
.notification-badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
</style>

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hearings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained()->onDelete('cascade');
            $table->foreignId('case_id')->constrained()->onDelete('cascade');
            $table->foreignId('assigned_lawyer')->constrained('users');
            
            // معلومات الجلسة
            $table->string('hearing_number')->nullable(); // رقم الجلسة
            $table->enum('type', [
                'first_hearing', 'follow_up', 'final_hearing', 
                'appeal', 'execution', 'mediation', 'other'
            ]);
            $table->enum('status', [
                'scheduled', 'postponed', 'completed', 
                'cancelled', 'no_show'
            ])->default('scheduled');
            
            // التوقيت والمكان
            $table->datetime('scheduled_at'); // موعد الجلسة
            $table->datetime('actual_start_time')->nullable(); // وقت البداية الفعلي
            $table->datetime('actual_end_time')->nullable(); // وقت النهاية الفعلي
            $table->string('court_name'); // اسم المحكمة
            $table->string('court_room')->nullable(); // قاعة المحكمة
            $table->string('judge_name')->nullable(); // اسم القاضي
            
            // الحضور
            $table->boolean('lawyer_attended')->default(false); // حضور المحامي
            $table->boolean('client_attended')->default(false); // حضور العميل
            $table->text('attendees')->nullable(); // قائمة الحاضرين
            
            // تفاصيل الجلسة
            $table->text('agenda')->nullable(); // جدول الأعمال
            $table->text('proceedings')->nullable(); // وقائع الجلسة
            $table->text('decisions')->nullable(); // القرارات المتخذة
            $table->text('next_steps')->nullable(); // الخطوات التالية
            $table->text('notes')->nullable(); // ملاحظات
            
            // الجلسة التالية
            $table->datetime('next_hearing_date')->nullable(); // موعد الجلسة التالية
            $table->text('next_hearing_purpose')->nullable(); // غرض الجلسة التالية
            
            // التذكيرات
            $table->boolean('reminder_sent')->default(false); // تم إرسال التذكير
            $table->datetime('reminder_sent_at')->nullable(); // وقت إرسال التذكير
            
            // المرفقات والوثائق
            $table->json('documents')->nullable(); // الوثائق المرفقة
            $table->json('evidence_presented')->nullable(); // الأدلة المقدمة
            
            $table->timestamps();
            $table->softDeletes();
            
            // فهارس
            $table->index(['tenant_id', 'case_id']);
            $table->index(['tenant_id', 'assigned_lawyer']);
            $table->index(['tenant_id', 'scheduled_at']);
            $table->index(['tenant_id', 'status']);
            $table->index('scheduled_at');
            $table->index('next_hearing_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hearings');
    }
};

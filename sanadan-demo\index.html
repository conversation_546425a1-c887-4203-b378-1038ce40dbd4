<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة سندان - نظام إدارة مكاتب المحاماة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
    </style>
</head>
<body class="bg-gray-50 font-arabic">
    <!-- Header -->
    <header class="bg-white shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-primary-600">منصة سندان</h1>
                    </div>
                </div>
                <nav class="hidden md:flex space-x-8 space-x-reverse">
                    <a href="#features" class="text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium">الميزات</a>
                    <a href="#pricing" class="text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium">الأسعار</a>
                    <a href="#contact" class="text-gray-500 hover:text-gray-900 px-3 py-2 text-sm font-medium">تواصل معنا</a>
                    <a href="#demo" class="bg-primary-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-primary-700">تجربة مجانية</a>
                </nav>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="gradient-bg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
                    نظام إدارة مكاتب المحاماة
                    <span class="block text-yellow-300">الأكثر تطوراً في الكويت</span>
                </h1>
                <p class="text-xl text-gray-200 mb-8 max-w-3xl mx-auto">
                    منصة سندان تقدم حلولاً متكاملة لإدارة القضايا والعملاء والمالية والمستندات بطريقة احترافية ومتطورة
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button onclick="startDemo()" class="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        ابدأ التجربة المجانية
                    </button>
                    <button onclick="watchDemo()" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-primary-600 transition-colors">
                        شاهد العرض التوضيحي
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">ميزات المنصة</h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    مجموعة شاملة من الأدوات المتطورة لإدارة مكتب المحاماة بكفاءة عالية
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Feature 1 -->
                <div class="bg-white p-6 rounded-lg shadow-md card-hover">
                    <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">إدارة القضايا</h3>
                    <p class="text-gray-600">تتبع شامل لجميع القضايا مع إدارة المواعيد والجلسات والمستندات</p>
                </div>

                <!-- Feature 2 -->
                <div class="bg-white p-6 rounded-lg shadow-md card-hover">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">إدارة العملاء</h3>
                    <p class="text-gray-600">ملفات شاملة للعملاء مع تتبع التفاعلات والمراسلات</p>
                </div>

                <!-- Feature 3 -->
                <div class="bg-white p-6 rounded-lg shadow-md card-hover">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">النظام المالي</h3>
                    <p class="text-gray-600">إدارة الفواتير والمدفوعات مع دعم بوابات الدفع الكويتية</p>
                </div>

                <!-- Feature 4 -->
                <div class="bg-white p-6 rounded-lg shadow-md card-hover">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">إدارة المواعيد</h3>
                    <p class="text-gray-600">تقويم تفاعلي مع تنبيهات ذكية للجلسات والمواعيد</p>
                </div>

                <!-- Feature 5 -->
                <div class="bg-white p-6 rounded-lg shadow-md card-hover">
                    <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">إدارة المستندات</h3>
                    <p class="text-gray-600">تخزين آمن ومنظم لجميع المستندات مع إمكانية البحث المتقدم</p>
                </div>

                <!-- Feature 6 -->
                <div class="bg-white p-6 rounded-lg shadow-md card-hover">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-2">التقارير والتحليلات</h3>
                    <p class="text-gray-600">تقارير مفصلة ورسوم بيانية لمراقبة الأداء والإنتاجية</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Demo Section -->
    <section id="demo" class="bg-gray-100 py-20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">جرب المنصة الآن</h2>
                <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                    اكتشف قوة منصة سندان من خلال العرض التوضيحي التفاعلي
                </p>
            </div>

            <div class="bg-white rounded-lg shadow-lg overflow-hidden">
                <div class="bg-gray-800 px-4 py-2 flex items-center space-x-2 space-x-reverse">
                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    <span class="text-gray-300 text-sm mr-4">منصة سندان - لوحة التحكم</span>
                </div>
                
                <div id="demo-content" class="p-8">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-blue-600">127</div>
                            <div class="text-sm text-gray-600">إجمالي القضايا</div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-green-600">89</div>
                            <div class="text-sm text-gray-600">القضايا النشطة</div>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-yellow-600">245</div>
                            <div class="text-sm text-gray-600">العملاء</div>
                        </div>
                        <div class="bg-purple-50 p-4 rounded-lg">
                            <div class="text-2xl font-bold text-purple-600">15,750 د.ك</div>
                            <div class="text-sm text-gray-600">الإيرادات الشهرية</div>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-lg font-semibold mb-4">الجلسات القادمة</h3>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between p-3 bg-white rounded">
                                    <div>
                                        <div class="font-medium">قضية التجارة الإلكترونية</div>
                                        <div class="text-sm text-gray-500">محكمة الكويت الكلية</div>
                                    </div>
                                    <div class="text-sm text-blue-600">غداً 10:00 ص</div>
                                </div>
                                <div class="flex items-center justify-between p-3 bg-white rounded">
                                    <div>
                                        <div class="font-medium">قضية العقار التجاري</div>
                                        <div class="text-sm text-gray-500">محكمة الجهراء</div>
                                    </div>
                                    <div class="text-sm text-blue-600">الأحد 2:30 م</div>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-50 p-6 rounded-lg">
                            <h3 class="text-lg font-semibold mb-4">المهام العاجلة</h3>
                            <div class="space-y-3">
                                <div class="flex items-center p-3 bg-white rounded">
                                    <input type="checkbox" class="ml-3">
                                    <div>
                                        <div class="font-medium">مراجعة عقد الشراكة</div>
                                        <div class="text-sm text-gray-500">موعد الاستحقاق: اليوم</div>
                                    </div>
                                </div>
                                <div class="flex items-center p-3 bg-white rounded">
                                    <input type="checkbox" class="ml-3">
                                    <div>
                                        <div class="font-medium">إعداد مذكرة دفاع</div>
                                        <div class="text-sm text-gray-500">موعد الاستحقاق: غداً</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="bg-primary-600 py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">
                ابدأ رحلتك مع منصة سندان اليوم
            </h2>
            <p class="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
                انضم إلى مئات مكاتب المحاماة التي تثق في منصة سندان لإدارة أعمالها
            </p>
            <button onclick="startTrial()" class="bg-white text-primary-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                ابدأ التجربة المجانية لمدة 30 يوماً
            </button>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-xl font-bold mb-4">منصة سندان</h3>
                    <p class="text-gray-400">
                        نظام إدارة مكاتب المحاماة الأكثر تطوراً في الكويت والمنطقة العربية
                    </p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">المنتج</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">الميزات</a></li>
                        <li><a href="#" class="hover:text-white">الأسعار</a></li>
                        <li><a href="#" class="hover:text-white">الأمان</a></li>
                        <li><a href="#" class="hover:text-white">التحديثات</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">الدعم</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">مركز المساعدة</a></li>
                        <li><a href="#" class="hover:text-white">تواصل معنا</a></li>
                        <li><a href="#" class="hover:text-white">التدريب</a></li>
                        <li><a href="#" class="hover:text-white">الوثائق</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">الشركة</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white">من نحن</a></li>
                        <li><a href="#" class="hover:text-white">المدونة</a></li>
                        <li><a href="#" class="hover:text-white">الوظائف</a></li>
                        <li><a href="#" class="hover:text-white">الشراكات</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 منصة سندان. جميع الحقوق محفوظة.</p>
            </div>
        </div>
    </footer>

    <script>
        function startDemo() {
            alert('مرحباً! هذا عرض توضيحي لمنصة سندان. في النسخة الكاملة، ستتمكن من الوصول إلى جميع الميزات المتطورة لإدارة مكتب المحاماة.');
        }

        function watchDemo() {
            alert('سيتم توفير فيديو توضيحي قريباً يشرح جميع ميزات المنصة بالتفصيل.');
        }

        function startTrial() {
            alert('للحصول على تجربة مجانية، يرجى التواصل معنا على: <EMAIL>');
        }

        // Add some interactivity to the demo
        document.addEventListener('DOMContentLoaded', function() {
            // Animate numbers
            const numbers = document.querySelectorAll('.text-2xl.font-bold');
            numbers.forEach(num => {
                const finalValue = parseInt(num.textContent);
                if (!isNaN(finalValue)) {
                    let currentValue = 0;
                    const increment = finalValue / 50;
                    const timer = setInterval(() => {
                        currentValue += increment;
                        if (currentValue >= finalValue) {
                            currentValue = finalValue;
                            clearInterval(timer);
                        }
                        if (num.textContent.includes('د.ك')) {
                            num.textContent = Math.floor(currentValue).toLocaleString() + ' د.ك';
                        } else {
                            num.textContent = Math.floor(currentValue);
                        }
                    }, 50);
                }
            });
        });
    </script>
</body>
</html>

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained()->onDelete('cascade');
            
            // معلومات أساسية
            $table->string('first_name');
            $table->string('last_name');
            $table->string('email')->unique();
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password');
            $table->string('phone')->nullable();
            $table->date('birth_date')->nullable();
            $table->enum('gender', ['male', 'female'])->nullable();
            $table->string('avatar')->nullable();
            
            // معلومات مهنية
            $table->string('job_title')->nullable(); // المسمى الوظيفي
            $table->string('license_number')->nullable(); // رقم الترخيص المهني
            $table->string('specialization')->nullable(); // التخصص
            $table->text('bio')->nullable(); // نبذة شخصية
            $table->decimal('hourly_rate', 8, 2)->nullable(); // السعر بالساعة
            
            // إعدادات النظام
            $table->enum('role', ['owner', 'admin', 'lawyer', 'assistant', 'client'])->default('lawyer');
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_login_at')->nullable();
            $table->string('last_login_ip')->nullable();
            $table->json('permissions')->nullable(); // صلاحيات مخصصة
            $table->json('preferences')->nullable(); // تفضيلات المستخدم
            
            // إعدادات الإشعارات
            $table->boolean('email_notifications')->default(true);
            $table->boolean('sms_notifications')->default(false);
            $table->boolean('push_notifications')->default(true);
            
            // معلومات الأمان
            $table->string('two_factor_secret')->nullable();
            $table->text('two_factor_recovery_codes')->nullable();
            $table->timestamp('two_factor_confirmed_at')->nullable();
            
            $table->rememberToken();
            $table->timestamps();
            $table->softDeletes();
            
            // فهارس
            $table->index(['tenant_id', 'role']);
            $table->index(['tenant_id', 'is_active']);
            $table->index('email');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};

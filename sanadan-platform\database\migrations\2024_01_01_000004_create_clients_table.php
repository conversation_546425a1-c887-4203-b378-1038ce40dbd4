<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل الهجرة
     */
    public function up(): void
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // الاسم الكامل
            $table->string('email')->nullable();
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            $table->string('id_number')->nullable(); // رقم الهوية/الإقامة
            $table->enum('client_type', ['individual', 'company', 'government'])->default('individual');
            $table->string('company_name')->nullable(); // اسم الشركة
            $table->string('commercial_registration')->nullable(); // السجل التجاري
            $table->string('nationality')->nullable();
            $table->enum('gender', ['male', 'female'])->nullable();
            $table->date('birth_date')->nullable();
            $table->enum('status', ['active', 'inactive', 'blacklisted'])->default('active');
            $table->json('contact_preferences')->nullable(); // تفضيلات التواصل
            $table->json('additional_info')->nullable(); // معلومات إضافية
            $table->text('notes')->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['status', 'client_type']);
            $table->index('email');
            $table->index('phone');
            $table->index('id_number');
        });
    }

    /**
     * التراجع عن الهجرة
     */
    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Alert,
  Dimensions,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  Checkbox,
  Divider,
  IconButton,
} from 'react-native-paper';
import { Formik } from 'formik';
import * as Yup from 'yup';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

// المكونات والخدمات
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import LoadingSpinner from '../../components/LoadingSpinner';
import SocialLoginButtons from '../../components/SocialLoginButtons';
import BiometricLoginButton from '../../components/BiometricLoginButton';

const { width, height } = Dimensions.get('window');

interface LoginFormValues {
  email: string;
  password: string;
  rememberMe: boolean;
}

const loginSchema = Yup.object().shape({
  email: Yup.string()
    .email('البريد الإلكتروني غير صحيح')
    .required('البريد الإلكتروني مطلوب'),
  password: Yup.string()
    .min(6, 'كلمة المرور يجب أن تكون 6 أحرف على الأقل')
    .required('كلمة المرور مطلوبة'),
});

export default function LoginScreen() {
  const navigation = useNavigation();
  const { login, authenticateWithBiometric, checkBiometricAvailability } = useAuth();
  const { theme } = useTheme();
  const { t } = useLanguage();

  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [lastEmail, setLastEmail] = useState('');

  useEffect(() => {
    checkBiometric();
    loadLastEmail();
  }, []);

  const checkBiometric = async () => {
    try {
      const available = await checkBiometricAvailability();
      const enabled = await AsyncStorage.getItem('biometricEnabled');
      setBiometricAvailable(available && enabled === 'true');
    } catch (error) {
      console.error('خطأ في فحص المصادقة البيومترية:', error);
    }
  };

  const loadLastEmail = async () => {
    try {
      const email = await AsyncStorage.getItem('lastEmail');
      if (email) {
        setLastEmail(email);
      }
    } catch (error) {
      console.error('خطأ في تحميل آخر بريد إلكتروني:', error);
    }
  };

  const handleLogin = async (values: LoginFormValues) => {
    try {
      setIsLoading(true);
      await login(values.email, values.password, values.rememberMe);
    } catch (error: any) {
      Alert.alert(
        'خطأ في تسجيل الدخول',
        error.message || 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.',
        [{ text: 'موافق' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleBiometricLogin = async () => {
    try {
      setIsLoading(true);
      await authenticateWithBiometric();
    } catch (error: any) {
      Alert.alert(
        'خطأ في المصادقة البيومترية',
        error.message || 'فشلت المصادقة البيومترية. يرجى المحاولة مرة أخرى.',
        [{ text: 'موافق' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleForgotPassword = () => {
    navigation.navigate('ForgotPassword');
  };

  const handleRegister = () => {
    navigation.navigate('Register');
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: theme.colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView
        contentContainerStyle={styles.scrollContainer}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {/* الشعار والعنوان */}
        <View style={styles.header}>
          <Icon
            name="scale-balance"
            size={80}
            color={theme.colors.primary}
            style={styles.logo}
          />
          <Text style={[styles.title, { color: theme.colors.onBackground }]}>
            منصة سندان
          </Text>
          <Text style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
            نظام إدارة مكاتب المحاماة
          </Text>
        </View>

        {/* نموذج تسجيل الدخول */}
        <Card style={styles.loginCard}>
          <Card.Content>
            <Text style={[styles.cardTitle, { color: theme.colors.onSurface }]}>
              تسجيل الدخول
            </Text>

            <Formik
              initialValues={{
                email: lastEmail,
                password: '',
                rememberMe: false,
              }}
              validationSchema={loginSchema}
              onSubmit={handleLogin}
            >
              {({
                handleChange,
                handleBlur,
                handleSubmit,
                values,
                errors,
                touched,
                setFieldValue,
              }) => (
                <View style={styles.form}>
                  {/* البريد الإلكتروني */}
                  <TextInput
                    label="البريد الإلكتروني"
                    value={values.email}
                    onChangeText={handleChange('email')}
                    onBlur={handleBlur('email')}
                    error={touched.email && !!errors.email}
                    keyboardType="email-address"
                    autoCapitalize="none"
                    autoComplete="email"
                    textContentType="emailAddress"
                    left={<TextInput.Icon icon="email" />}
                    style={styles.input}
                  />
                  {touched.email && errors.email && (
                    <Text style={[styles.errorText, { color: theme.colors.error }]}>
                      {errors.email}
                    </Text>
                  )}

                  {/* كلمة المرور */}
                  <TextInput
                    label="كلمة المرور"
                    value={values.password}
                    onChangeText={handleChange('password')}
                    onBlur={handleBlur('password')}
                    error={touched.password && !!errors.password}
                    secureTextEntry={!showPassword}
                    autoComplete="password"
                    textContentType="password"
                    left={<TextInput.Icon icon="lock" />}
                    right={
                      <TextInput.Icon
                        icon={showPassword ? 'eye-off' : 'eye'}
                        onPress={() => setShowPassword(!showPassword)}
                      />
                    }
                    style={styles.input}
                  />
                  {touched.password && errors.password && (
                    <Text style={[styles.errorText, { color: theme.colors.error }]}>
                      {errors.password}
                    </Text>
                  )}

                  {/* تذكرني */}
                  <View style={styles.checkboxContainer}>
                    <Checkbox
                      status={values.rememberMe ? 'checked' : 'unchecked'}
                      onPress={() => setFieldValue('rememberMe', !values.rememberMe)}
                    />
                    <Text
                      style={[styles.checkboxLabel, { color: theme.colors.onSurface }]}
                      onPress={() => setFieldValue('rememberMe', !values.rememberMe)}
                    >
                      تذكرني
                    </Text>
                  </View>

                  {/* زر تسجيل الدخول */}
                  <Button
                    mode="contained"
                    onPress={handleSubmit}
                    style={styles.loginButton}
                    contentStyle={styles.buttonContent}
                    disabled={isLoading}
                  >
                    تسجيل الدخول
                  </Button>

                  {/* نسيت كلمة المرور */}
                  <Button
                    mode="text"
                    onPress={handleForgotPassword}
                    style={styles.forgotButton}
                  >
                    نسيت كلمة المرور؟
                  </Button>
                </View>
              )}
            </Formik>

            {/* المصادقة البيومترية */}
            {biometricAvailable && (
              <>
                <Divider style={styles.divider} />
                <BiometricLoginButton onPress={handleBiometricLogin} />
              </>
            )}

            {/* تسجيل الدخول بوسائل التواصل الاجتماعي */}
            <Divider style={styles.divider} />
            <SocialLoginButtons />
          </Card.Content>
        </Card>

        {/* رابط التسجيل */}
        <View style={styles.registerContainer}>
          <Text style={[styles.registerText, { color: theme.colors.onSurfaceVariant }]}>
            ليس لديك حساب؟
          </Text>
          <Button mode="text" onPress={handleRegister} compact>
            إنشاء حساب جديد
          </Button>
        </View>

        {/* معلومات إضافية */}
        <View style={styles.footer}>
          <Text style={[styles.footerText, { color: theme.colors.onSurfaceVariant }]}>
            بتسجيل الدخول، أنت توافق على
          </Text>
          <View style={styles.footerLinks}>
            <Button mode="text" compact onPress={() => {}}>
              شروط الاستخدام
            </Button>
            <Text style={[styles.footerSeparator, { color: theme.colors.onSurfaceVariant }]}>
              و
            </Text>
            <Button mode="text" compact onPress={() => {}}>
              سياسة الخصوصية
            </Button>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  header: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logo: {
    marginBottom: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.7,
  },
  loginCard: {
    marginBottom: 20,
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 24,
  },
  form: {
    gap: 16,
  },
  input: {
    backgroundColor: 'transparent',
  },
  errorText: {
    fontSize: 12,
    marginTop: -12,
    marginBottom: 8,
  },
  checkboxContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: 8,
  },
  checkboxLabel: {
    marginLeft: 8,
    fontSize: 16,
  },
  loginButton: {
    marginTop: 16,
  },
  buttonContent: {
    paddingVertical: 8,
  },
  forgotButton: {
    marginTop: 8,
  },
  divider: {
    marginVertical: 20,
  },
  registerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 20,
  },
  registerText: {
    fontSize: 16,
  },
  footer: {
    alignItems: 'center',
    marginTop: 40,
  },
  footerText: {
    fontSize: 12,
    textAlign: 'center',
    marginBottom: 8,
  },
  footerLinks: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  footerSeparator: {
    fontSize: 12,
    marginHorizontal: 4,
  },
});

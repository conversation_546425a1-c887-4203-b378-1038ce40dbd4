-- منصة سندان - قاعدة البيانات الشاملة لإدارة مكاتب المحاماة
-- تم التصميم خصيصاً للبيئة القانونية في الكويت والمنطقة العربية

-- إعدادات قاعدة البيانات
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;
SET collation_connection = utf8mb4_unicode_ci;

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS sanadan_legal_platform 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE sanadan_legal_platform;

-- ===================================
-- جداول النظام الأساسية
-- ===================================

-- جدول المستأجرين (Multi-tenancy)
CREATE TABLE tenants (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(255) NOT NULL,
    domain VARCHAR(100) UNIQUE NOT NULL,
    database_name VARCHAR(100),
    status ENUM('active', 'suspended', 'cancelled') DEFAULT 'active',
    subscription_plan ENUM('basic', 'professional', 'enterprise') DEFAULT 'basic',
    subscription_expires_at TIMESTAMP NULL,
    settings JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_domain (domain),
    INDEX idx_status (status),
    INDEX idx_subscription (subscription_plan, subscription_expires_at)
);

-- جدول النطاقات
CREATE TABLE domains (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    domain VARCHAR(255) NOT NULL,
    tenant_id CHAR(36) NOT NULL,
    is_primary BOOLEAN DEFAULT FALSE,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY unique_domain (domain),
    INDEX idx_tenant (tenant_id)
);

-- ===================================
-- إدارة المستخدمين والصلاحيات
-- ===================================

-- جدول المستخدمين
CREATE TABLE users (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    tenant_id CHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    avatar TEXT,
    role ENUM('admin', 'lawyer', 'employee', 'client') DEFAULT 'employee',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    language CHAR(2) DEFAULT 'ar',
    timezone VARCHAR(50) DEFAULT 'Asia/Kuwait',
    last_login_at TIMESTAMP NULL,
    last_login_ip VARCHAR(45),
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(255),
    remember_token VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY unique_tenant_email (tenant_id, email),
    INDEX idx_tenant (tenant_id),
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_status (status)
);

-- جدول الأدوار
CREATE TABLE roles (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    tenant_id CHAR(36) NOT NULL,
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(255),
    description TEXT,
    permissions JSON,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    UNIQUE KEY unique_tenant_role (tenant_id, name),
    INDEX idx_tenant (tenant_id)
);

-- جدول ربط المستخدمين بالأدوار
CREATE TABLE user_roles (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id CHAR(36) NOT NULL,
    role_id CHAR(36) NOT NULL,
    assigned_by CHAR(36),
    assigned_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_user_role (user_id, role_id)
);

-- ===================================
-- إدارة مكاتب المحاماة
-- ===================================

-- جدول مكاتب المحاماة
CREATE TABLE law_firms (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    tenant_id CHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    logo TEXT,
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    website VARCHAR(255),
    license_number VARCHAR(100),
    commercial_registration VARCHAR(100),
    tax_number VARCHAR(100),
    established_date DATE,
    specializations JSON,
    settings JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    INDEX idx_tenant (tenant_id),
    INDEX idx_license (license_number)
);

-- جدول المحامين
CREATE TABLE lawyers (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id CHAR(36) NOT NULL,
    law_firm_id CHAR(36) NOT NULL,
    license_number VARCHAR(100) UNIQUE,
    bar_association VARCHAR(255),
    specialization JSON,
    experience_years INT DEFAULT 0,
    education JSON,
    certifications JSON,
    bio TEXT,
    hourly_rate DECIMAL(10,3),
    is_partner BOOLEAN DEFAULT FALSE,
    joined_at DATE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (law_firm_id) REFERENCES law_firms(id) ON DELETE CASCADE,
    INDEX idx_user (user_id),
    INDEX idx_firm (law_firm_id),
    INDEX idx_license (license_number)
);

-- ===================================
-- إدارة العملاء
-- ===================================

-- جدول العملاء
CREATE TABLE clients (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    tenant_id CHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(20),
    mobile VARCHAR(20),
    address TEXT,
    id_number VARCHAR(50),
    passport_number VARCHAR(50),
    nationality VARCHAR(100),
    client_type ENUM('individual', 'company', 'government') DEFAULT 'individual',
    company_name VARCHAR(255),
    commercial_registration VARCHAR(100),
    tax_number VARCHAR(100),
    gender ENUM('male', 'female'),
    birth_date DATE,
    occupation VARCHAR(255),
    notes TEXT,
    status ENUM('active', 'inactive', 'blacklisted') DEFAULT 'active',
    source VARCHAR(100),
    referral_source VARCHAR(255),
    preferred_language CHAR(2) DEFAULT 'ar',
    emergency_contact JSON,
    created_by CHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_tenant (tenant_id),
    INDEX idx_name (name),
    INDEX idx_email (email),
    INDEX idx_phone (phone),
    INDEX idx_id_number (id_number),
    INDEX idx_type (client_type),
    INDEX idx_status (status),
    FULLTEXT idx_search (name, name_en, email, company_name)
);

-- ===================================
-- إدارة القضايا
-- ===================================

-- جدول أنواع القضايا
CREATE TABLE case_types (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    tenant_id CHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    description TEXT,
    color VARCHAR(7) DEFAULT '#3B82F6',
    icon VARCHAR(50) DEFAULT 'folder',
    is_active BOOLEAN DEFAULT TRUE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    INDEX idx_tenant (tenant_id),
    INDEX idx_active (is_active)
);

-- جدول حالات القضايا
CREATE TABLE case_statuses (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    tenant_id CHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    description TEXT,
    color VARCHAR(7) DEFAULT '#6B7280',
    is_active BOOLEAN DEFAULT TRUE,
    is_final BOOLEAN DEFAULT FALSE,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    INDEX idx_tenant (tenant_id),
    INDEX idx_active (is_active)
);

-- جدول القضايا
CREATE TABLE cases (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    tenant_id CHAR(36) NOT NULL,
    case_number VARCHAR(100) NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    case_type_id CHAR(36),
    status_id CHAR(36),
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    client_id CHAR(36) NOT NULL,
    lawyer_id CHAR(36),
    court_id CHAR(36),
    filing_date DATE,
    estimated_duration INT, -- بالأيام
    estimated_value DECIMAL(15,3),
    fees_amount DECIMAL(15,3),
    fees_type ENUM('fixed', 'hourly', 'percentage') DEFAULT 'fixed',
    fees_percentage DECIMAL(5,2),
    currency CHAR(3) DEFAULT 'KWD',
    is_billable BOOLEAN DEFAULT TRUE,
    is_archived BOOLEAN DEFAULT FALSE,
    archived_at TIMESTAMP NULL,
    notes TEXT,
    tags JSON,
    custom_fields JSON,
    created_by CHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (case_type_id) REFERENCES case_types(id) ON DELETE SET NULL,
    FOREIGN KEY (status_id) REFERENCES case_statuses(id) ON DELETE SET NULL,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE,
    FOREIGN KEY (lawyer_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_tenant_case_number (tenant_id, case_number),
    INDEX idx_tenant (tenant_id),
    INDEX idx_case_number (case_number),
    INDEX idx_client (client_id),
    INDEX idx_lawyer (lawyer_id),
    INDEX idx_type (case_type_id),
    INDEX idx_status (status_id),
    INDEX idx_priority (priority),
    INDEX idx_archived (is_archived),
    INDEX idx_filing_date (filing_date),
    FULLTEXT idx_search (title, description, case_number)
);

-- ===================================
-- إدارة المحاكم والدوائر
-- ===================================

-- جدول المحاكم
CREATE TABLE courts (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    tenant_id CHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    type ENUM('civil', 'criminal', 'commercial', 'administrative', 'family', 'labor') DEFAULT 'civil',
    address TEXT,
    phone VARCHAR(20),
    email VARCHAR(255),
    website VARCHAR(255),
    working_hours TEXT,
    location_lat DECIMAL(10, 8),
    location_lng DECIMAL(11, 8),
    is_active BOOLEAN DEFAULT TRUE,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    INDEX idx_tenant (tenant_id),
    INDEX idx_type (type),
    INDEX idx_active (is_active),
    INDEX idx_location (location_lat, location_lng)
);

-- جدول دوائر المحاكم
CREATE TABLE court_chambers (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    court_id CHAR(36) NOT NULL,
    name VARCHAR(255) NOT NULL,
    chamber_number VARCHAR(50),
    judge_name VARCHAR(255),
    judge_title VARCHAR(100),
    clerk_name VARCHAR(255),
    specialization VARCHAR(255),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (court_id) REFERENCES courts(id) ON DELETE CASCADE,
    INDEX idx_court (court_id),
    INDEX idx_active (is_active)
);

-- ===================================
-- إدارة الجلسات
-- ===================================

-- جدول الجلسات
CREATE TABLE hearings (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    tenant_id CHAR(36) NOT NULL,
    case_id CHAR(36) NOT NULL,
    court_id CHAR(36),
    chamber_id CHAR(36),
    hearing_date DATE NOT NULL,
    hearing_time TIME NOT NULL,
    duration INT DEFAULT 60, -- بالدقائق
    type ENUM('initial', 'follow_up', 'final', 'appeal', 'execution') DEFAULT 'follow_up',
    status ENUM('scheduled', 'completed', 'postponed', 'cancelled') DEFAULT 'scheduled',
    agenda TEXT,
    notes TEXT,
    outcome TEXT,
    next_hearing_date DATE,
    next_hearing_time TIME,
    attendees JSON,
    documents JSON,
    reminder_sent BOOLEAN DEFAULT FALSE,
    created_by CHAR(36),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (case_id) REFERENCES cases(id) ON DELETE CASCADE,
    FOREIGN KEY (court_id) REFERENCES courts(id) ON DELETE SET NULL,
    FOREIGN KEY (chamber_id) REFERENCES court_chambers(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_tenant (tenant_id),
    INDEX idx_case (case_id),
    INDEX idx_court (court_id),
    INDEX idx_date (hearing_date),
    INDEX idx_status (status),
    INDEX idx_type (type),
    INDEX idx_datetime (hearing_date, hearing_time)
);

-- ===================================
-- إدارة المهام
-- ===================================

-- جدول المهام
CREATE TABLE tasks (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    tenant_id CHAR(36) NOT NULL,
    title VARCHAR(500) NOT NULL,
    description TEXT,
    assigned_to CHAR(36),
    assigned_by CHAR(36),
    case_id CHAR(36),
    client_id CHAR(36),
    due_date DATETIME,
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    status ENUM('pending', 'in_progress', 'completed', 'cancelled', 'overdue') DEFAULT 'pending',
    type ENUM('call', 'meeting', 'document', 'research', 'court', 'follow_up', 'other') DEFAULT 'other',
    estimated_hours DECIMAL(5,2),
    actual_hours DECIMAL(5,2),
    completion_percentage INT DEFAULT 0,
    tags JSON,
    attachments JSON,
    reminder_date DATETIME,
    reminder_sent BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (case_id) REFERENCES cases(id) ON DELETE CASCADE,
    FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE SET NULL,
    INDEX idx_tenant (tenant_id),
    INDEX idx_assigned (assigned_to),
    INDEX idx_case (case_id),
    INDEX idx_client (client_id),
    INDEX idx_due_date (due_date),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_type (type),
    FULLTEXT idx_search (title, description)
);

-- جدول تعليقات المهام
CREATE TABLE task_comments (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    task_id CHAR(36) NOT NULL,
    user_id CHAR(36) NOT NULL,
    content TEXT NOT NULL,
    attachments JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_task (task_id),
    INDEX idx_user (user_id)
);

-- إدراج البيانات الأساسية
INSERT INTO case_types (id, tenant_id, name, name_en, color, icon) VALUES
(UUID(), '00000000-0000-0000-0000-000000000000', 'قضايا مدنية', 'Civil Cases', '#3B82F6', 'gavel'),
(UUID(), '00000000-0000-0000-0000-000000000000', 'قضايا جنائية', 'Criminal Cases', '#EF4444', 'shield'),
(UUID(), '00000000-0000-0000-0000-000000000000', 'قضايا تجارية', 'Commercial Cases', '#10B981', 'briefcase'),
(UUID(), '00000000-0000-0000-0000-000000000000', 'قضايا عمالية', 'Labor Cases', '#F59E0B', 'users'),
(UUID(), '00000000-0000-0000-0000-000000000000', 'قضايا أسرة', 'Family Cases', '#8B5CF6', 'heart');

INSERT INTO case_statuses (id, tenant_id, name, name_en, color, is_final) VALUES
(UUID(), '00000000-0000-0000-0000-000000000000', 'مفتوحة', 'Open', '#3B82F6', FALSE),
(UUID(), '00000000-0000-0000-0000-000000000000', 'قيد المتابعة', 'In Progress', '#F59E0B', FALSE),
(UUID(), '00000000-0000-0000-0000-000000000000', 'معلقة', 'Pending', '#6B7280', FALSE),
(UUID(), '00000000-0000-0000-0000-000000000000', 'مغلقة', 'Closed', '#10B981', TRUE),
(UUID(), '00000000-0000-0000-0000-000000000000', 'مرفوضة', 'Dismissed', '#EF4444', TRUE);

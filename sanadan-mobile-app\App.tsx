import React, { useEffect, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { Provider as PaperProvider } from 'react-native-paper';
import { QueryClient, QueryClientProvider } from 'react-query';
import { I18nManager, Platform } from 'react-native';
import * as SplashScreen from 'expo-splash-screen';
import * as Font from 'expo-font';
import Toast from 'react-native-toast-message';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

// المكونات والخدمات
import { AuthProvider } from './src/contexts/AuthContext';
import { ThemeProvider } from './src/contexts/ThemeContext';
import { LanguageProvider } from './src/contexts/LanguageContext';
import { NotificationProvider } from './src/contexts/NotificationContext';
import AppNavigator from './src/navigation/AppNavigator';
import { theme } from './src/theme/theme';
import { setupNotifications } from './src/services/notificationService';
import { initializeApp } from './src/services/appService';
import LoadingScreen from './src/components/LoadingScreen';
import ErrorBoundary from './src/components/ErrorBoundary';

// إعداد RTL للعربية
I18nManager.allowRTL(true);
I18nManager.forceRTL(true);

// منع إخفاء شاشة التحميل تلقائياً
SplashScreen.preventAutoHideAsync();

// إعداد React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      staleTime: 5 * 60 * 1000, // 5 دقائق
      cacheTime: 10 * 60 * 1000, // 10 دقائق
    },
  },
});

export default function App() {
  const [isReady, setIsReady] = useState(false);
  const [initializing, setInitializing] = useState(true);

  useEffect(() => {
    async function prepare() {
      try {
        // تحميل الخطوط
        await Font.loadAsync({
          'Cairo-Regular': require('./assets/fonts/Cairo-Regular.ttf'),
          'Cairo-Bold': require('./assets/fonts/Cairo-Bold.ttf'),
          'Cairo-SemiBold': require('./assets/fonts/Cairo-SemiBold.ttf'),
          'Cairo-Light': require('./assets/fonts/Cairo-Light.ttf'),
          'Amiri-Regular': require('./assets/fonts/Amiri-Regular.ttf'),
          'Amiri-Bold': require('./assets/fonts/Amiri-Bold.ttf'),
        });

        // إعداد الإشعارات
        await setupNotifications();

        // تهيئة التطبيق
        await initializeApp();

        // تأخير قصير لضمان تحميل كل شيء
        await new Promise(resolve => setTimeout(resolve, 2000));

      } catch (error) {
        console.warn('خطأ في تحضير التطبيق:', error);
      } finally {
        setIsReady(true);
        setInitializing(false);
      }
    }

    prepare();
  }, []);

  const onLayoutRootView = React.useCallback(async () => {
    if (isReady) {
      await SplashScreen.hideAsync();
    }
  }, [isReady]);

  if (!isReady || initializing) {
    return <LoadingScreen />;
  }

  return (
    <ErrorBoundary>
      <GestureHandlerRootView style={{ flex: 1 }} onLayout={onLayoutRootView}>
        <QueryClientProvider client={queryClient}>
          <PaperProvider theme={theme}>
            <ThemeProvider>
              <LanguageProvider>
                <AuthProvider>
                  <NotificationProvider>
                    <NavigationContainer>
                      <StatusBar style="auto" />
                      <AppNavigator />
                      <Toast />
                    </NavigationContainer>
                  </NotificationProvider>
                </AuthProvider>
              </LanguageProvider>
            </ThemeProvider>
          </PaperProvider>
        </QueryClientProvider>
      </GestureHandlerRootView>
    </ErrorBoundary>
  );
}

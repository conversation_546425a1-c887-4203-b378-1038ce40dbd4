<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Lara<PERSON>\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * نموذج المستخدم - يمثل المحامين والموظفين في مكتب المحاماة
 * 
 * @property int $id
 * @property string $name الاسم الكامل
 * @property string $email البريد الإلكتروني
 * @property string $phone رقم الهاتف
 * @property string $password كلمة المرور
 * @property string $avatar صورة المستخدم
 * @property string $status حالة المستخدم (active, inactive, suspended)
 * @property string $user_type نوع المستخدم (admin, lawyer, employee, client)
 * @property array $settings إعدادات المستخدم الشخصية
 * @property \Carbon\Carbon $email_verified_at
 * @property \Carbon\Carbon $last_login_at آخر تسجيل دخول
 * @property string $two_factor_secret سر المصادقة الثنائية
 * @property array $two_factor_recovery_codes رموز الاسترداد
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class User extends Authenticatable implements MustVerifyEmail, HasMedia
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, LogsActivity, InteractsWithMedia;

    /**
     * الحقول القابلة للتعبئة
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'password',
        'status',
        'user_type',
        'settings',
        'last_login_at',
        'two_factor_secret',
        'two_factor_recovery_codes',
    ];

    /**
     * الحقول المخفية من التسلسل
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_secret',
        'two_factor_recovery_codes',
    ];

    /**
     * تحويل الحقول إلى أنواع البيانات المناسبة
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'last_login_at' => 'datetime',
        'password' => 'hashed',
        'settings' => 'array',
        'two_factor_recovery_codes' => 'array',
    ];

    /**
     * القيم الافتراضية للحقول
     */
    protected $attributes = [
        'status' => 'active',
        'user_type' => 'employee',
        'settings' => '{}',
    ];

    /**
     * إعدادات تسجيل النشاط
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'email', 'phone', 'status', 'user_type'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * تكوين مجموعات الوسائط
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('avatar')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif']);

        $this->addMediaCollection('documents')
            ->acceptsMimeTypes(['application/pdf', 'image/jpeg', 'image/png']);
    }

    /**
     * تحويلات الوسائط
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(150)
            ->height(150)
            ->sharpen(10)
            ->performOnCollections('avatar');

        $this->addMediaConversion('preview')
            ->width(500)
            ->height(500)
            ->performOnCollections('avatar');
    }

    /**
     * العلاقة مع ملف المحامي
     */
    public function lawyerProfile()
    {
        return $this->hasOne(Lawyer::class);
    }

    /**
     * العلاقة مع القضايا المسندة للمحامي
     */
    public function assignedCases()
    {
        return $this->hasMany(LegalCase::class, 'lawyer_id');
    }

    /**
     * العلاقة مع المهام المسندة للمستخدم
     */
    public function assignedTasks()
    {
        return $this->hasMany(Task::class, 'assigned_to');
    }

    /**
     * العلاقة مع ساعات العمل المسجلة
     */
    public function timesheets()
    {
        return $this->hasMany(Timesheet::class);
    }

    /**
     * العلاقة مع الفواتير المنشأة بواسطة المستخدم
     */
    public function createdInvoices()
    {
        return $this->hasMany(Invoice::class, 'created_by');
    }

    /**
     * التحقق من كون المستخدم محامي
     */
    public function isLawyer(): bool
    {
        return $this->user_type === 'lawyer' && $this->lawyerProfile()->exists();
    }

    /**
     * التحقق من كون المستخدم مدير
     */
    public function isAdmin(): bool
    {
        return $this->user_type === 'admin' || $this->hasRole('admin');
    }

    /**
     * التحقق من كون المستخدم عميل
     */
    public function isClient(): bool
    {
        return $this->user_type === 'client';
    }

    /**
     * الحصول على صورة المستخدم
     */
    public function getAvatarUrlAttribute(): string
    {
        $avatar = $this->getFirstMedia('avatar');
        
        if ($avatar) {
            return $avatar->getUrl('thumb');
        }

        // إنشاء صورة افتراضية بناءً على الاسم
        $name = urlencode($this->name);
        return "https://ui-avatars.com/api/?name={$name}&color=1e40af&background=e0e7ff&size=150";
    }

    /**
     * الحصول على الاسم المختصر
     */
    public function getInitialsAttribute(): string
    {
        $words = explode(' ', $this->name);
        $initials = '';
        
        foreach ($words as $word) {
            if (!empty($word)) {
                $initials .= mb_substr($word, 0, 1);
            }
        }
        
        return mb_strtoupper($initials);
    }

    /**
     * تحديث آخر تسجيل دخول
     */
    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }

    /**
     * تفعيل المصادقة الثنائية
     */
    public function enableTwoFactorAuth(string $secret, array $recoveryCodes): void
    {
        $this->update([
            'two_factor_secret' => encrypt($secret),
            'two_factor_recovery_codes' => array_map('encrypt', $recoveryCodes),
        ]);
    }

    /**
     * إلغاء تفعيل المصادقة الثنائية
     */
    public function disableTwoFactorAuth(): void
    {
        $this->update([
            'two_factor_secret' => null,
            'two_factor_recovery_codes' => null,
        ]);
    }

    /**
     * التحقق من تفعيل المصادقة الثنائية
     */
    public function hasTwoFactorAuth(): bool
    {
        return !is_null($this->two_factor_secret);
    }

    /**
     * الحصول على إعداد معين
     */
    public function getSetting(string $key, $default = null)
    {
        return $this->settings[$key] ?? $default;
    }

    /**
     * تحديث إعداد معين
     */
    public function updateSetting(string $key, $value): void
    {
        $settings = $this->settings;
        $settings[$key] = $value;
        $this->update(['settings' => $settings]);
    }

    /**
     * تعليق المستخدم
     */
    public function suspend(string $reason = null): void
    {
        $this->update([
            'status' => 'suspended',
        ]);

        if ($reason) {
            $this->updateSetting('suspension_reason', $reason);
            $this->updateSetting('suspended_at', now()->toISOString());
        }
    }

    /**
     * إعادة تفعيل المستخدم
     */
    public function reactivate(): void
    {
        $settings = $this->settings;
        unset($settings['suspension_reason'], $settings['suspended_at']);
        
        $this->update([
            'status' => 'active',
            'settings' => $settings
        ]);
    }

    /**
     * التحقق من حالة المستخدم النشطة
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }
}

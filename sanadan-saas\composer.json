{"name": "sanadan/legal-saas", "type": "project", "description": "منصة سندان SaaS لإدارة مكاتب المحاماة", "keywords": ["laravel", "saas", "legal", "law-firm", "kuwait"], "license": "proprietary", "require": {"php": "^8.2", "laravel/framework": "^11.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "laravel/cashier": "^15.0", "spatie/laravel-permission": "^6.0", "spatie/laravel-activitylog": "^4.8", "spatie/laravel-backup": "^8.0", "spatie/laravel-medialibrary": "^11.0", "barryvdh/laravel-dompdf": "^2.0", "maatwebsite/excel": "^3.1", "pusher/pusher-php-server": "^7.2", "laravel/horizon": "^5.21", "laravel/telescope": "^5.0", "intervention/image": "^3.0", "league/flysystem-aws-s3-v3": "^3.0", "predis/predis": "^2.0", "guzzlehttp/guzzle": "^7.2"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}
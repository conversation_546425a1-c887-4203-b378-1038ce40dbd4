<?php

namespace App\Models;

use Stancl\Tenancy\Database\Models\Tenant as BaseTenant;
use Stancl\Tenancy\Contracts\TenantWithDatabase;
use Stancl\Tenancy\Database\Concerns\HasDatabase;
use Stancl\Tenancy\Database\Concerns\HasDomains;
use Illuminate\Database\Eloquent\Factories\HasFactory;

/**
 * نموذج المستأجر (Tenant) - يمثل كل مكتب محاماة منفصل
 * 
 * @property string $id
 * @property string $name اسم مكتب المحاماة
 * @property string $email البريد الإلكتروني الرئيسي
 * @property string $phone رقم الهاتف
 * @property string $address العنوان
 * @property string $license_number رقم الترخيص
 * @property array $settings الإعدادات المخصصة
 * @property string $subscription_plan باقة الاشتراك
 * @property \Carbon\Carbon $subscription_ends_at تاريخ انتهاء الاشتراك
 * @property string $status حالة المستأجر (active, suspended, expired)
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class Tenant extends BaseTenant implements TenantWithDatabase
{
    use HasDatabase, HasDomains, HasFactory;

    /**
     * الحقول القابلة للتعبئة
     */
    protected $fillable = [
        'id',
        'name',
        'email',
        'phone',
        'address',
        'license_number',
        'settings',
        'subscription_plan',
        'subscription_ends_at',
        'status',
    ];

    /**
     * تحويل الحقول إلى أنواع البيانات المناسبة
     */
    protected $casts = [
        'settings' => 'array',
        'subscription_ends_at' => 'datetime',
    ];

    /**
     * القيم الافتراضية للحقول
     */
    protected $attributes = [
        'status' => 'active',
        'subscription_plan' => 'basic',
        'settings' => '{}',
    ];

    /**
     * إنشاء مستأجر جديد مع الإعدادات الافتراضية
     */
    public static function createWithDefaults(array $data): self
    {
        $tenant = static::create([
            'id' => $data['id'] ?? \Str::random(8),
            'name' => $data['name'],
            'email' => $data['email'],
            'phone' => $data['phone'] ?? null,
            'address' => $data['address'] ?? null,
            'license_number' => $data['license_number'] ?? null,
            'subscription_plan' => $data['subscription_plan'] ?? 'basic',
            'subscription_ends_at' => $data['subscription_ends_at'] ?? now()->addMonth(),
            'settings' => array_merge([
                'timezone' => 'Asia/Kuwait',
                'currency' => 'KWD',
                'language' => 'ar',
                'date_format' => 'd/m/Y',
                'time_format' => 'H:i',
                'fiscal_year_start' => '01-01',
                'tax_rate' => 0.0,
                'features' => [
                    'cases_limit' => 100,
                    'users_limit' => 2,
                    'storage_limit' => 5, // GB
                    'ai_assistant' => false,
                    'advanced_reports' => false,
                    'api_access' => false,
                ]
            ], $data['settings'] ?? []),
        ]);

        return $tenant;
    }

    /**
     * التحقق من صحة الاشتراك
     */
    public function hasValidSubscription(): bool
    {
        return $this->status === 'active' && 
               $this->subscription_ends_at && 
               $this->subscription_ends_at->isFuture();
    }

    /**
     * التحقق من توفر ميزة معينة
     */
    public function hasFeature(string $feature): bool
    {
        return $this->settings['features'][$feature] ?? false;
    }

    /**
     * الحصول على حد ميزة معينة
     */
    public function getFeatureLimit(string $feature): int
    {
        return $this->settings['features'][$feature] ?? 0;
    }

    /**
     * تحديث باقة الاشتراك
     */
    public function updateSubscription(string $plan, \Carbon\Carbon $endsAt = null): void
    {
        $features = $this->getSubscriptionFeatures($plan);
        
        $this->update([
            'subscription_plan' => $plan,
            'subscription_ends_at' => $endsAt ?? now()->addMonth(),
            'status' => 'active',
            'settings' => array_merge($this->settings, [
                'features' => $features
            ])
        ]);
    }

    /**
     * الحصول على ميزات باقة الاشتراك
     */
    private function getSubscriptionFeatures(string $plan): array
    {
        $features = [
            'basic' => [
                'cases_limit' => 100,
                'users_limit' => 2,
                'storage_limit' => 5,
                'ai_assistant' => false,
                'advanced_reports' => false,
                'api_access' => false,
            ],
            'professional' => [
                'cases_limit' => -1, // unlimited
                'users_limit' => 10,
                'storage_limit' => 50,
                'ai_assistant' => true,
                'advanced_reports' => true,
                'api_access' => false,
            ],
            'enterprise' => [
                'cases_limit' => -1,
                'users_limit' => -1,
                'storage_limit' => 500,
                'ai_assistant' => true,
                'advanced_reports' => true,
                'api_access' => true,
            ],
        ];

        return $features[$plan] ?? $features['basic'];
    }

    /**
     * تعليق المستأجر
     */
    public function suspend(string $reason = null): void
    {
        $this->update([
            'status' => 'suspended',
            'settings' => array_merge($this->settings, [
                'suspension_reason' => $reason,
                'suspended_at' => now()->toISOString(),
            ])
        ]);
    }

    /**
     * إعادة تفعيل المستأجر
     */
    public function reactivate(): void
    {
        $settings = $this->settings;
        unset($settings['suspension_reason'], $settings['suspended_at']);
        
        $this->update([
            'status' => 'active',
            'settings' => $settings
        ]);
    }
}

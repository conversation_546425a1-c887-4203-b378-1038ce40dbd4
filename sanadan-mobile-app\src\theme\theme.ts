import { DefaultTheme, MD3DarkTheme } from 'react-native-paper';
import { Platform } from 'react-native';

// ألوان المنصة الأساسية
export const colors = {
  // الألوان الأساسية
  primary: {
    50: '#eff6ff',
    100: '#dbeafe',
    200: '#bfdbfe',
    300: '#93c5fd',
    400: '#60a5fa',
    500: '#3b82f6',
    600: '#2563eb',
    700: '#1d4ed8',
    800: '#1e40af',
    900: '#1e3a8a',
    950: '#172554',
  },
  
  // الألوان الثانوية
  secondary: {
    50: '#fef2f2',
    100: '#fee2e2',
    200: '#fecaca',
    300: '#fca5a5',
    400: '#f87171',
    500: '#ef4444',
    600: '#dc2626',
    700: '#b91c1c',
    800: '#991b1b',
    900: '#7f1d1d',
    950: '#450a0a',
  },
  
  // ألوان النجاح
  success: {
    50: '#f0fdf4',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#86efac',
    400: '#4ade80',
    500: '#22c55e',
    600: '#16a34a',
    700: '#15803d',
    800: '#166534',
    900: '#14532d',
    950: '#052e16',
  },
  
  // ألوان التحذير
  warning: {
    50: '#fffbeb',
    100: '#fef3c7',
    200: '#fde68a',
    300: '#fcd34d',
    400: '#fbbf24',
    500: '#f59e0b',
    600: '#d97706',
    700: '#b45309',
    800: '#92400e',
    900: '#78350f',
    950: '#451a03',
  },
  
  // ألوان المعلومات
  info: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
    950: '#082f49',
  },
  
  // الألوان الرمادية
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
    950: '#030712',
  },
  
  // الألوان الذهبية
  gold: {
    50: '#fefce8',
    100: '#fef9c3',
    200: '#fef08a',
    300: '#fde047',
    400: '#facc15',
    500: '#eab308',
    600: '#ca8a04',
    700: '#a16207',
    800: '#854d0e',
    900: '#713f12',
    950: '#422006',
  },
  
  // ألوان خاصة بالحالات
  status: {
    open: '#3b82f6',
    in_progress: '#f59e0b',
    pending: '#6b7280',
    on_hold: '#f97316',
    closed: '#22c55e',
    dismissed: '#ef4444',
    settled: '#10b981',
    appealed: '#8b5cf6',
  },
  
  // ألوان الأولويات
  priority: {
    low: '#22c55e',
    medium: '#f59e0b',
    high: '#f97316',
    urgent: '#ef4444',
  },
  
  // ألوان إضافية
  white: '#ffffff',
  black: '#000000',
  transparent: 'transparent',
};

// الخطوط
export const fonts = {
  regular: Platform.select({
    ios: 'Cairo-Regular',
    android: 'Cairo-Regular',
    default: 'Cairo-Regular',
  }),
  medium: Platform.select({
    ios: 'Cairo-SemiBold',
    android: 'Cairo-SemiBold',
    default: 'Cairo-SemiBold',
  }),
  bold: Platform.select({
    ios: 'Cairo-Bold',
    android: 'Cairo-Bold',
    default: 'Cairo-Bold',
  }),
  light: Platform.select({
    ios: 'Cairo-Light',
    android: 'Cairo-Light',
    default: 'Cairo-Light',
  }),
  arabic: Platform.select({
    ios: 'Amiri-Regular',
    android: 'Amiri-Regular',
    default: 'Amiri-Regular',
  }),
  arabicBold: Platform.select({
    ios: 'Amiri-Bold',
    android: 'Amiri-Bold',
    default: 'Amiri-Bold',
  }),
};

// أحجام الخطوط
export const fontSizes = {
  xs: 12,
  sm: 14,
  base: 16,
  lg: 18,
  xl: 20,
  '2xl': 24,
  '3xl': 30,
  '4xl': 36,
  '5xl': 48,
  '6xl': 60,
};

// المسافات
export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  '2xl': 48,
  '3xl': 64,
  '4xl': 80,
  '5xl': 96,
};

// نصف الأقطار
export const borderRadius = {
  none: 0,
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
  '2xl': 24,
  '3xl': 32,
  full: 9999,
};

// الظلال
export const shadows = {
  sm: {
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  md: {
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
  },
  lg: {
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.15,
    shadowRadius: 15,
    elevation: 5,
  },
  xl: {
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 20 },
    shadowOpacity: 0.25,
    shadowRadius: 25,
    elevation: 8,
  },
};

// ثيم الوضع الفاتح
export const lightTheme = {
  ...DefaultTheme,
  colors: {
    ...DefaultTheme.colors,
    primary: colors.primary[600],
    primaryContainer: colors.primary[100],
    secondary: colors.secondary[600],
    secondaryContainer: colors.secondary[100],
    tertiary: colors.info[600],
    tertiaryContainer: colors.info[100],
    surface: colors.white,
    surfaceVariant: colors.gray[50],
    background: colors.gray[50],
    error: colors.secondary[600],
    errorContainer: colors.secondary[100],
    onPrimary: colors.white,
    onPrimaryContainer: colors.primary[800],
    onSecondary: colors.white,
    onSecondaryContainer: colors.secondary[800],
    onTertiary: colors.white,
    onTertiaryContainer: colors.info[800],
    onSurface: colors.gray[900],
    onSurfaceVariant: colors.gray[600],
    onBackground: colors.gray[900],
    onError: colors.white,
    onErrorContainer: colors.secondary[800],
    outline: colors.gray[300],
    outlineVariant: colors.gray[200],
    inverseSurface: colors.gray[800],
    inverseOnSurface: colors.gray[100],
    inversePrimary: colors.primary[300],
    shadow: colors.black,
    scrim: colors.black,
    surfaceDisabled: colors.gray[100],
    onSurfaceDisabled: colors.gray[400],
    backdrop: 'rgba(0, 0, 0, 0.5)',
  },
  fonts: {
    ...DefaultTheme.fonts,
    default: {
      fontFamily: fonts.regular,
    },
    displayLarge: {
      fontFamily: fonts.bold,
      fontSize: fontSizes['5xl'],
      lineHeight: 64,
    },
    displayMedium: {
      fontFamily: fonts.bold,
      fontSize: fontSizes['4xl'],
      lineHeight: 52,
    },
    displaySmall: {
      fontFamily: fonts.bold,
      fontSize: fontSizes['3xl'],
      lineHeight: 44,
    },
    headlineLarge: {
      fontFamily: fonts.bold,
      fontSize: fontSizes['2xl'],
      lineHeight: 32,
    },
    headlineMedium: {
      fontFamily: fonts.medium,
      fontSize: fontSizes.xl,
      lineHeight: 28,
    },
    headlineSmall: {
      fontFamily: fonts.medium,
      fontSize: fontSizes.lg,
      lineHeight: 24,
    },
    titleLarge: {
      fontFamily: fonts.medium,
      fontSize: fontSizes.base,
      lineHeight: 24,
    },
    titleMedium: {
      fontFamily: fonts.medium,
      fontSize: fontSizes.sm,
      lineHeight: 20,
    },
    titleSmall: {
      fontFamily: fonts.medium,
      fontSize: fontSizes.xs,
      lineHeight: 16,
    },
    bodyLarge: {
      fontFamily: fonts.regular,
      fontSize: fontSizes.base,
      lineHeight: 24,
    },
    bodyMedium: {
      fontFamily: fonts.regular,
      fontSize: fontSizes.sm,
      lineHeight: 20,
    },
    bodySmall: {
      fontFamily: fonts.regular,
      fontSize: fontSizes.xs,
      lineHeight: 16,
    },
    labelLarge: {
      fontFamily: fonts.medium,
      fontSize: fontSizes.sm,
      lineHeight: 20,
    },
    labelMedium: {
      fontFamily: fonts.medium,
      fontSize: fontSizes.xs,
      lineHeight: 16,
    },
    labelSmall: {
      fontFamily: fonts.medium,
      fontSize: 10,
      lineHeight: 14,
    },
  },
};

// ثيم الوضع المظلم
export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: colors.primary[400],
    primaryContainer: colors.primary[800],
    secondary: colors.secondary[400],
    secondaryContainer: colors.secondary[800],
    tertiary: colors.info[400],
    tertiaryContainer: colors.info[800],
    surface: colors.gray[900],
    surfaceVariant: colors.gray[800],
    background: colors.gray[950],
    error: colors.secondary[400],
    errorContainer: colors.secondary[800],
    onPrimary: colors.gray[900],
    onPrimaryContainer: colors.primary[100],
    onSecondary: colors.gray[900],
    onSecondaryContainer: colors.secondary[100],
    onTertiary: colors.gray[900],
    onTertiaryContainer: colors.info[100],
    onSurface: colors.gray[100],
    onSurfaceVariant: colors.gray[400],
    onBackground: colors.gray[100],
    onError: colors.gray[900],
    onErrorContainer: colors.secondary[100],
    outline: colors.gray[600],
    outlineVariant: colors.gray[700],
    inverseSurface: colors.gray[100],
    inverseOnSurface: colors.gray[800],
    inversePrimary: colors.primary[600],
    shadow: colors.black,
    scrim: colors.black,
    surfaceDisabled: colors.gray[800],
    onSurfaceDisabled: colors.gray[600],
    backdrop: 'rgba(0, 0, 0, 0.7)',
  },
  fonts: lightTheme.fonts,
};

// الثيم الافتراضي
export const theme = lightTheme;

// دالة للحصول على لون الحالة
export const getStatusColor = (status: string): string => {
  return colors.status[status as keyof typeof colors.status] || colors.gray[500];
};

// دالة للحصول على لون الأولوية
export const getPriorityColor = (priority: string): string => {
  return colors.priority[priority as keyof typeof colors.priority] || colors.gray[500];
};

// دالة للحصول على الثيم حسب الوضع
export const getTheme = (isDark: boolean) => {
  return isDark ? darkTheme : lightTheme;
};

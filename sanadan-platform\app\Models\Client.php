<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * نموذج العميل - يمثل عملاء مكتب المحاماة
 * 
 * @property int $id
 * @property string $name الاسم الكامل
 * @property string $email البريد الإلكتروني
 * @property string $phone رقم الهاتف
 * @property string $address العنوان
 * @property string $id_number رقم الهوية/الإقامة
 * @property string $client_type نوع العميل (individual, company, government)
 * @property string $company_name اسم الشركة (إذا كان العميل شركة)
 * @property string $commercial_registration السجل التجاري
 * @property string $nationality الجنسية
 * @property string $gender الجنس
 * @property \Carbon\Carbon $birth_date تاريخ الميلاد
 * @property string $status حالة العميل (active, inactive, blacklisted)
 * @property array $contact_preferences تفضيلات التواصل
 * @property array $additional_info معلومات إضافية
 * @property string $notes ملاحظات
 * @property int $created_by المستخدم الذي أنشأ العميل
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon $deleted_at
 */
class Client extends Model implements HasMedia
{
    use HasFactory, SoftDeletes, LogsActivity, InteractsWithMedia;

    /**
     * الحقول القابلة للتعبئة
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'address',
        'id_number',
        'client_type',
        'company_name',
        'commercial_registration',
        'nationality',
        'gender',
        'birth_date',
        'status',
        'contact_preferences',
        'additional_info',
        'notes',
        'created_by',
    ];

    /**
     * تحويل الحقول إلى أنواع البيانات المناسبة
     */
    protected $casts = [
        'birth_date' => 'date',
        'contact_preferences' => 'array',
        'additional_info' => 'array',
    ];

    /**
     * القيم الافتراضية للحقول
     */
    protected $attributes = [
        'status' => 'active',
        'client_type' => 'individual',
        'contact_preferences' => '{}',
        'additional_info' => '{}',
    ];

    /**
     * إعدادات تسجيل النشاط
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'email', 'phone', 'status', 'client_type'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * تكوين مجموعات الوسائط
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('avatar')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/gif']);

        $this->addMediaCollection('documents')
            ->acceptsMimeTypes(['application/pdf', 'image/jpeg', 'image/png', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']);

        $this->addMediaCollection('id_documents')
            ->acceptsMimeTypes(['application/pdf', 'image/jpeg', 'image/png']);
    }

    /**
     * تحويلات الوسائط
     */
    public function registerMediaConversions(Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(150)
            ->height(150)
            ->sharpen(10)
            ->performOnCollections('avatar');

        $this->addMediaConversion('preview')
            ->width(800)
            ->height(600)
            ->performOnCollections('documents', 'id_documents');
    }

    /**
     * العلاقة مع المستخدم الذي أنشأ العميل
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * العلاقة مع القضايا
     */
    public function cases()
    {
        return $this->hasMany(LegalCase::class);
    }

    /**
     * العلاقة مع الفواتير
     */
    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * العلاقة مع المدفوعات
     */
    public function payments()
    {
        return $this->hasManyThrough(Payment::class, Invoice::class);
    }

    /**
     * العلاقة مع المهام المتعلقة بالعميل
     */
    public function tasks()
    {
        return $this->hasManyThrough(Task::class, LegalCase::class);
    }

    /**
     * العلاقة مع الجلسات
     */
    public function hearings()
    {
        return $this->hasManyThrough(Hearing::class, LegalCase::class);
    }

    /**
     * الحصول على الاسم المعروض
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->client_type === 'company' && $this->company_name) {
            return $this->company_name;
        }
        
        return $this->name;
    }

    /**
     * الحصول على صورة العميل
     */
    public function getAvatarUrlAttribute(): string
    {
        $avatar = $this->getFirstMedia('avatar');
        
        if ($avatar) {
            return $avatar->getUrl('thumb');
        }

        // إنشاء صورة افتراضية بناءً على الاسم
        $name = urlencode($this->display_name);
        $bgColor = $this->client_type === 'company' ? '059669' : '7c3aed';
        return "https://ui-avatars.com/api/?name={$name}&color=ffffff&background={$bgColor}&size=150";
    }

    /**
     * الحصول على العمر
     */
    public function getAgeAttribute(): ?int
    {
        if (!$this->birth_date) {
            return null;
        }

        return $this->birth_date->diffInYears(now());
    }

    /**
     * التحقق من كون العميل شركة
     */
    public function isCompany(): bool
    {
        return $this->client_type === 'company';
    }

    /**
     * التحقق من كون العميل فرد
     */
    public function isIndividual(): bool
    {
        return $this->client_type === 'individual';
    }

    /**
     * التحقق من كون العميل جهة حكومية
     */
    public function isGovernment(): bool
    {
        return $this->client_type === 'government';
    }

    /**
     * الحصول على إجمالي المبلغ المستحق
     */
    public function getTotalOutstandingAmount(): float
    {
        return $this->invoices()
            ->whereIn('status', ['pending', 'overdue'])
            ->sum('total_amount');
    }

    /**
     * الحصول على إجمالي المبلغ المدفوع
     */
    public function getTotalPaidAmount(): float
    {
        return $this->payments()->sum('amount');
    }

    /**
     * الحصول على عدد القضايا النشطة
     */
    public function getActiveCasesCount(): int
    {
        return $this->cases()
            ->whereNotIn('status', ['closed', 'dismissed'])
            ->count();
    }

    /**
     * الحصول على آخر قضية
     */
    public function getLastCase()
    {
        return $this->cases()->latest()->first();
    }

    /**
     * الحصول على تفضيل تواصل معين
     */
    public function getContactPreference(string $key, $default = null)
    {
        return $this->contact_preferences[$key] ?? $default;
    }

    /**
     * تحديث تفضيل تواصل
     */
    public function updateContactPreference(string $key, $value): void
    {
        $preferences = $this->contact_preferences;
        $preferences[$key] = $value;
        $this->update(['contact_preferences' => $preferences]);
    }

    /**
     * إضافة معلومة إضافية
     */
    public function addAdditionalInfo(string $key, $value): void
    {
        $info = $this->additional_info;
        $info[$key] = $value;
        $this->update(['additional_info' => $info]);
    }

    /**
     * إضافة العميل إلى القائمة السوداء
     */
    public function blacklist(string $reason = null): void
    {
        $this->update(['status' => 'blacklisted']);
        
        if ($reason) {
            $this->addAdditionalInfo('blacklist_reason', $reason);
            $this->addAdditionalInfo('blacklisted_at', now()->toISOString());
        }
    }

    /**
     * إزالة العميل من القائمة السوداء
     */
    public function removeFromBlacklist(): void
    {
        $info = $this->additional_info;
        unset($info['blacklist_reason'], $info['blacklisted_at']);
        
        $this->update([
            'status' => 'active',
            'additional_info' => $info
        ]);
    }

    /**
     * التحقق من وجود العميل في القائمة السوداء
     */
    public function isBlacklisted(): bool
    {
        return $this->status === 'blacklisted';
    }

    /**
     * البحث في العملاء
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('email', 'like', "%{$search}%")
              ->orWhere('phone', 'like', "%{$search}%")
              ->orWhere('id_number', 'like', "%{$search}%")
              ->orWhere('company_name', 'like', "%{$search}%");
        });
    }

    /**
     * فلترة حسب نوع العميل
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('client_type', $type);
    }

    /**
     * فلترة حسب الحالة
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }
}

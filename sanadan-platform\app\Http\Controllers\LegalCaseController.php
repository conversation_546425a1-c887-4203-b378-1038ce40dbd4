<?php

namespace App\Http\Controllers;

use App\Models\LegalCase;
use App\Models\Client;
use App\Models\CaseType;
use App\Models\Court;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

/**
 * متحكم إدارة القضايا القانونية
 */
class LegalCaseController extends Controller
{
    /**
     * عرض قائمة القضايا
     */
    public function index(Request $request)
    {
        $query = LegalCase::with(['client', 'lawyer', 'caseType', 'court'])
            ->latest();

        // تطبيق الفلاتر
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        if ($request->filled('status')) {
            $query->withStatus($request->status);
        }

        if ($request->filled('priority')) {
            $query->withPriority($request->priority);
        }

        if ($request->filled('lawyer_id')) {
            $query->forLawyer($request->lawyer_id);
        }

        if ($request->filled('client_id')) {
            $query->forClient($request->client_id);
        }

        if ($request->filled('case_type_id')) {
            $query->where('case_type_id', $request->case_type_id);
        }

        // فلترة حسب صلاحيات المستخدم
        $user = Auth::user();
        if (!$user->isAdmin()) {
            if ($user->isLawyer()) {
                $query->forLawyer($user->id);
            } else {
                // للموظفين: عرض القضايا التي لديهم مهام فيها
                $query->whereHas('tasks', function($q) use ($user) {
                    $q->where('assigned_to', $user->id);
                });
            }
        }

        $cases = $query->paginate(15);

        // بيانات إضافية للفلاتر
        $filters = [
            'lawyers' => User::where('user_type', 'lawyer')->get(['id', 'name']),
            'clients' => Client::get(['id', 'name']),
            'case_types' => CaseType::where('is_active', true)->get(['id', 'name']),
            'courts' => Court::where('is_active', true)->get(['id', 'name']),
        ];

        return view('cases.index', compact('cases', 'filters'));
    }

    /**
     * عرض نموذج إنشاء قضية جديدة
     */
    public function create()
    {
        $data = [
            'clients' => Client::where('status', 'active')->get(['id', 'name', 'client_type']),
            'lawyers' => User::where('user_type', 'lawyer')->where('status', 'active')->get(['id', 'name']),
            'case_types' => CaseType::where('is_active', true)->orderBy('sort_order')->get(),
            'courts' => Court::where('is_active', true)->get(['id', 'name', 'type']),
        ];

        return view('cases.create', $data);
    }

    /**
     * حفظ قضية جديدة
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'case_type_id' => 'required|exists:case_types,id',
            'client_id' => 'required|exists:clients,id',
            'lawyer_id' => 'required|exists:users,id',
            'court_id' => 'nullable|exists:courts,id',
            'priority' => 'required|in:low,medium,high,urgent',
            'filing_date' => 'nullable|date',
            'estimated_completion_date' => 'nullable|date|after:filing_date',
            'estimated_value' => 'nullable|numeric|min:0',
            'fees_amount' => 'nullable|numeric|min:0',
            'fees_type' => 'required|in:fixed,hourly,percentage',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();
        try {
            // إنشاء رقم قضية تلقائي
            $validated['case_number'] = LegalCase::generateCaseNumber();
            $validated['created_by'] = Auth::id();

            $case = LegalCase::create($validated);

            // إضافة أطراف القضية الأساسية
            $case->addCaseParty('plaintiff', $case->client->name, [
                'type_ar' => 'مدعي',
                'client_id' => $case->client_id,
            ]);

            DB::commit();

            return redirect()->route('cases.show', $case)
                ->with('success', 'تم إنشاء القضية بنجاح');

        } catch (\Exception $e) {
            DB::rollback();
            return back()->withInput()
                ->with('error', 'حدث خطأ أثناء إنشاء القضية: ' . $e->getMessage());
        }
    }

    /**
     * عرض تفاصيل القضية
     */
    public function show(LegalCase $case)
    {
        $case->load([
            'client',
            'lawyer',
            'caseType',
            'court',
            'creator',
            'hearings' => function($query) {
                $query->orderBy('hearing_date', 'desc');
            },
            'tasks' => function($query) {
                $query->with('assignedUser')->orderBy('due_date');
            },
            'documents' => function($query) {
                $query->orderBy('created_at', 'desc');
            },
            'invoices' => function($query) {
                $query->orderBy('created_at', 'desc');
            },
            'timesheets' => function($query) {
                $query->with('user')->orderBy('date', 'desc');
            },
            'expenses' => function($query) {
                $query->orderBy('expense_date', 'desc');
            }
        ]);

        // إحصائيات القضية
        $stats = [
            'total_hours' => $case->getTotalHours(),
            'total_expenses' => $case->getTotalExpenses(),
            'total_invoiced' => $case->getTotalInvoiced(),
            'total_paid' => $case->getTotalPaid(),
            'outstanding_amount' => $case->getOutstandingAmount(),
            'next_hearing' => $case->getNextHearing(),
            'last_hearing' => $case->getLastHearing(),
        ];

        return view('cases.show', compact('case', 'stats'));
    }

    /**
     * عرض نموذج تعديل القضية
     */
    public function edit(LegalCase $case)
    {
        $data = [
            'case' => $case,
            'clients' => Client::where('status', 'active')->get(['id', 'name', 'client_type']),
            'lawyers' => User::where('user_type', 'lawyer')->where('status', 'active')->get(['id', 'name']),
            'case_types' => CaseType::where('is_active', true)->orderBy('sort_order')->get(),
            'courts' => Court::where('is_active', true)->get(['id', 'name', 'type']),
        ];

        return view('cases.edit', $data);
    }

    /**
     * تحديث بيانات القضية
     */
    public function update(Request $request, LegalCase $case)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'case_type_id' => 'required|exists:case_types,id',
            'client_id' => 'required|exists:clients,id',
            'lawyer_id' => 'required|exists:users,id',
            'court_id' => 'nullable|exists:courts,id',
            'status' => 'required|in:open,in_progress,pending,on_hold,closed,dismissed,settled,appealed',
            'priority' => 'required|in:low,medium,high,urgent',
            'filing_date' => 'nullable|date',
            'estimated_completion_date' => 'nullable|date',
            'estimated_value' => 'nullable|numeric|min:0',
            'fees_amount' => 'nullable|numeric|min:0',
            'fees_type' => 'required|in:fixed,hourly,percentage',
            'notes' => 'nullable|string',
        ]);

        $case->update($validated);

        return redirect()->route('cases.show', $case)
            ->with('success', 'تم تحديث بيانات القضية بنجاح');
    }

    /**
     * حذف القضية
     */
    public function destroy(LegalCase $case)
    {
        // التحقق من الصلاحيات
        if (!Auth::user()->isAdmin()) {
            abort(403, 'غير مصرح لك بحذف القضايا');
        }

        // التحقق من وجود بيانات مرتبطة
        if ($case->hearings()->count() > 0 || $case->invoices()->count() > 0) {
            return back()->with('error', 'لا يمكن حذف القضية لوجود بيانات مرتبطة بها');
        }

        $case->delete();

        return redirect()->route('cases.index')
            ->with('success', 'تم حذف القضية بنجاح');
    }

    /**
     * إغلاق القضية
     */
    public function close(Request $request, LegalCase $case)
    {
        $validated = $request->validate([
            'outcome' => 'nullable|string',
            'notes' => 'nullable|string',
        ]);

        $case->close($validated['outcome'] ?? null, $validated['notes'] ?? null);

        return back()->with('success', 'تم إغلاق القضية بنجاح');
    }

    /**
     * إعادة فتح القضية
     */
    public function reopen(Request $request, LegalCase $case)
    {
        $validated = $request->validate([
            'reason' => 'required|string',
        ]);

        $case->reopen($validated['reason']);

        return back()->with('success', 'تم إعادة فتح القضية بنجاح');
    }

    /**
     * إضافة طرف للقضية
     */
    public function addParty(Request $request, LegalCase $case)
    {
        $validated = $request->validate([
            'type' => 'required|string',
            'name' => 'required|string',
            'role' => 'nullable|string',
            'contact_info' => 'nullable|string',
        ]);

        $case->addCaseParty(
            $validated['type'],
            $validated['name'],
            [
                'role' => $validated['role'] ?? null,
                'contact_info' => $validated['contact_info'] ?? null,
            ]
        );

        return back()->with('success', 'تم إضافة الطرف بنجاح');
    }

    /**
     * إضافة مرجع قانوني
     */
    public function addLegalReference(Request $request, LegalCase $case)
    {
        $validated = $request->validate([
            'type' => 'required|string',
            'reference' => 'required|string',
            'description' => 'nullable|string',
        ]);

        $case->addLegalReference(
            $validated['type'],
            $validated['reference'],
            $validated['description'] ?? null
        );

        return back()->with('success', 'تم إضافة المرجع القانوني بنجاح');
    }

    /**
     * تحديث الجلسة القادمة
     */
    public function updateNextHearing(LegalCase $case)
    {
        $case->updateNextHearing();

        return back()->with('success', 'تم تحديث موعد الجلسة القادمة');
    }

    /**
     * تصدير القضايا
     */
    public function export(Request $request)
    {
        // سيتم تنفيذ هذه الوظيفة لاحقاً باستخدام Laravel Excel
        return back()->with('info', 'ميزة التصدير قيد التطوير');
    }

    /**
     * البحث السريع في القضايا (AJAX)
     */
    public function quickSearch(Request $request)
    {
        $search = $request->get('q');
        
        $cases = LegalCase::with(['client', 'lawyer'])
            ->where(function($query) use ($search) {
                $query->where('case_number', 'like', "%{$search}%")
                      ->orWhere('title', 'like', "%{$search}%")
                      ->orWhereHas('client', function($q) use ($search) {
                          $q->where('name', 'like', "%{$search}%");
                      });
            })
            ->limit(10)
            ->get(['id', 'case_number', 'title', 'client_id', 'lawyer_id', 'status']);

        return response()->json($cases);
    }

    /**
     * الحصول على إحصائيات القضايا (API)
     */
    public function getStats()
    {
        $user = Auth::user();
        
        $query = LegalCase::query();
        
        if (!$user->isAdmin()) {
            if ($user->isLawyer()) {
                $query->forLawyer($user->id);
            } else {
                $query->whereHas('tasks', function($q) use ($user) {
                    $q->where('assigned_to', $user->id);
                });
            }
        }

        $stats = [
            'total' => $query->count(),
            'active' => $query->active()->count(),
            'overdue' => $query->overdue()->count(),
            'by_status' => $query->select('status', DB::raw('count(*) as count'))
                ->groupBy('status')
                ->pluck('count', 'status'),
            'by_priority' => $query->select('priority', DB::raw('count(*) as count'))
                ->groupBy('priority')
                ->pluck('count', 'priority'),
        ];

        return response()->json($stats);
    }
}

<template>
  <AuthenticatedLayout>
    <Head title="لوحة التحكم" />

    <div class="space-y-6">
      <!-- ترحيب وإحصائيات سريعة -->
      <div class="bg-gradient-to-l from-primary-600 to-primary-800 rounded-lg p-6 text-white">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-2xl font-bold mb-2">
              مرحباً، {{ $page.props.auth.user.name }}
            </h1>
            <p class="text-primary-100">
              {{ getGreetingMessage() }}
            </p>
          </div>
          <div class="text-end">
            <div class="text-3xl font-bold">{{ formatDate(new Date(), 'date') }}</div>
            <div class="text-primary-200">{{ formatDate(new Date(), 'time') }}</div>
          </div>
        </div>
      </div>

      <!-- بطاقات الإحصائيات الرئيسية -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="إجمالي القضايا"
          :value="stats.total_cases"
          :change="stats.cases_change"
          icon="FolderIcon"
          color="blue"
          :loading="loading"
        />
        
        <StatsCard
          title="القضايا النشطة"
          :value="stats.active_cases"
          :change="stats.active_cases_change"
          icon="ClockIcon"
          color="yellow"
          :loading="loading"
        />
        
        <StatsCard
          title="العملاء"
          :value="stats.total_clients"
          :change="stats.clients_change"
          icon="UsersIcon"
          color="green"
          :loading="loading"
        />
        
        <StatsCard
          title="الإيرادات الشهرية"
          :value="formatCurrency(stats.monthly_revenue)"
          :change="stats.revenue_change"
          icon="CurrencyDollarIcon"
          color="purple"
          :loading="loading"
        />
      </div>

      <!-- الصف الثاني من الإحصائيات -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatsCard
          title="المهام المعلقة"
          :value="stats.pending_tasks"
          icon="CheckCircleIcon"
          color="orange"
          :loading="loading"
        />
        
        <StatsCard
          title="الجلسات القادمة"
          :value="stats.upcoming_hearings"
          icon="CalendarIcon"
          color="indigo"
          :loading="loading"
        />
        
        <StatsCard
          title="الفواتير المعلقة"
          :value="stats.pending_invoices"
          icon="DocumentTextIcon"
          color="red"
          :loading="loading"
        />
        
        <StatsCard
          title="ساعات العمل"
          :value="stats.total_hours"
          icon="ClockIcon"
          color="gray"
          :loading="loading"
        />
      </div>

      <!-- الرسوم البيانية والتحليلات -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- رسم بياني للقضايا -->
        <Card title="إحصائيات القضايا" class="h-96">
          <Chart
            type="doughnut"
            :data="casesChartData"
            :options="chartOptions"
            :loading="loading"
          />
        </Card>

        <!-- رسم بياني للإيرادات -->
        <Card title="الإيرادات الشهرية" class="h-96">
          <Chart
            type="line"
            :data="revenueChartData"
            :options="revenueChartOptions"
            :loading="loading"
          />
        </Card>
      </div>

      <!-- الجداول والقوائم -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- الجلسات القادمة -->
        <Card title="الجلسات القادمة" class="h-96">
          <template #actions>
            <Link href="/hearings" class="text-primary-600 hover:text-primary-700 text-sm">
              عرض الكل
            </Link>
          </template>

          <div v-if="loading" class="space-y-4">
            <div v-for="i in 5" :key="i" class="animate-pulse">
              <div class="flex items-center space-x-4 space-x-reverse">
                <div class="w-12 h-12 bg-gray-200 rounded-lg"></div>
                <div class="flex-1 space-y-2">
                  <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div class="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          </div>

          <div v-else-if="upcomingHearings.length === 0" class="text-center py-8 text-gray-500">
            لا توجد جلسات قادمة
          </div>

          <div v-else class="space-y-4">
            <div
              v-for="hearing in upcomingHearings"
              :key="hearing.id"
              class="flex items-center p-3 rounded-lg hover:bg-gray-50 cursor-pointer"
              @click="goToHearing(hearing.id)"
            >
              <div class="flex-shrink-0">
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <CalendarIcon class="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div class="ms-4 flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 truncate">
                  {{ hearing.legal_case.title }}
                </p>
                <p class="text-sm text-gray-500">
                  {{ hearing.court.name }}
                </p>
                <p class="text-xs text-gray-400">
                  {{ formatDate(hearing.hearing_date, 'datetime') }}
                </p>
              </div>
              <div class="flex-shrink-0">
                <Badge :color="getHearingStatusColor(hearing.status)">
                  {{ getHearingStatusLabel(hearing.status) }}
                </Badge>
              </div>
            </div>
          </div>
        </Card>

        <!-- المهام العاجلة -->
        <Card title="المهام العاجلة" class="h-96">
          <template #actions>
            <Link href="/tasks" class="text-primary-600 hover:text-primary-700 text-sm">
              عرض الكل
            </Link>
          </template>

          <div v-if="loading" class="space-y-4">
            <div v-for="i in 5" :key="i" class="animate-pulse">
              <div class="flex items-center space-x-4 space-x-reverse">
                <div class="w-8 h-8 bg-gray-200 rounded"></div>
                <div class="flex-1 space-y-2">
                  <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div class="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          </div>

          <div v-else-if="urgentTasks.length === 0" class="text-center py-8 text-gray-500">
            لا توجد مهام عاجلة
          </div>

          <div v-else class="space-y-3">
            <div
              v-for="task in urgentTasks"
              :key="task.id"
              class="flex items-center p-3 rounded-lg hover:bg-gray-50 cursor-pointer"
              @click="goToTask(task.id)"
            >
              <div class="flex-shrink-0">
                <Checkbox
                  :checked="task.status === 'completed'"
                  @change="toggleTaskStatus(task)"
                />
              </div>
              <div class="ms-3 flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 truncate">
                  {{ task.title }}
                </p>
                <p class="text-xs text-gray-500">
                  {{ task.legal_case?.title || 'مهمة عامة' }}
                </p>
                <p class="text-xs text-gray-400">
                  موعد الاستحقاق: {{ formatDate(task.due_date, 'date') }}
                </p>
              </div>
              <div class="flex-shrink-0">
                <Badge :color="getPriorityColor(task.priority)">
                  {{ getPriorityLabel(task.priority) }}
                </Badge>
              </div>
            </div>
          </div>
        </Card>
      </div>

      <!-- النشاط الأخير -->
      <Card title="النشاط الأخير">
        <div v-if="loading" class="space-y-4">
          <div v-for="i in 5" :key="i" class="animate-pulse">
            <div class="flex items-center space-x-4 space-x-reverse">
              <div class="w-8 h-8 bg-gray-200 rounded-full"></div>
              <div class="flex-1 space-y-2">
                <div class="h-4 bg-gray-200 rounded w-3/4"></div>
                <div class="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="recentActivities.length === 0" class="text-center py-8 text-gray-500">
          لا يوجد نشاط حديث
        </div>

        <div v-else class="space-y-4">
          <div
            v-for="activity in recentActivities"
            :key="activity.id"
            class="flex items-start space-x-3 space-x-reverse"
          >
            <div class="flex-shrink-0">
              <Avatar
                :src="activity.causer?.avatar_url"
                :name="activity.causer?.name || 'النظام'"
                size="sm"
              />
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm text-gray-900">
                <span class="font-medium">{{ activity.causer?.name || 'النظام' }}</span>
                {{ activity.description }}
              </p>
              <p class="text-xs text-gray-500">
                {{ formatDate(activity.created_at, 'datetime') }}
              </p>
            </div>
          </div>
        </div>
      </Card>
    </div>
  </AuthenticatedLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Head, Link, router } from '@inertiajs/vue3'
import { useToast } from 'vue-toastification'
import {
  FolderIcon,
  ClockIcon,
  UsersIcon,
  CurrencyDollarIcon,
  CheckCircleIcon,
  CalendarIcon,
  DocumentTextIcon
} from '@heroicons/vue/24/outline'

import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue'
import StatsCard from '@/Components/StatsCard.vue'
import Card from '@/Components/Card.vue'
import Chart from '@/Components/Chart.vue'
import Badge from '@/Components/Badge.vue'
import Checkbox from '@/Components/Checkbox.vue'
import Avatar from '@/Components/Avatar.vue'

const toast = useToast()

// البيانات التفاعلية
const loading = ref(true)
const stats = ref({})
const upcomingHearings = ref([])
const urgentTasks = ref([])
const recentActivities = ref([])

// بيانات الرسوم البيانية
const casesChartData = computed(() => ({
  labels: ['مفتوحة', 'قيد المتابعة', 'معلقة', 'مغلقة'],
  datasets: [{
    data: [
      stats.value.cases_by_status?.open || 0,
      stats.value.cases_by_status?.in_progress || 0,
      stats.value.cases_by_status?.pending || 0,
      stats.value.cases_by_status?.closed || 0
    ],
    backgroundColor: ['#3B82F6', '#F59E0B', '#6B7280', '#10B981'],
    borderWidth: 0
  }]
}))

const revenueChartData = computed(() => ({
  labels: stats.value.revenue_trend?.labels || [],
  datasets: [{
    label: 'الإيرادات',
    data: stats.value.revenue_trend?.data || [],
    borderColor: '#3B82F6',
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    tension: 0.4,
    fill: true
  }]
}))

const chartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom'
    }
  }
}

const revenueChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    }
  },
  scales: {
    y: {
      beginAtZero: true
    }
  }
}

// وظائف المساعدة
const getGreetingMessage = () => {
  const hour = new Date().getHours()
  if (hour < 12) return 'صباح الخير! نتمنى لك يوماً مثمراً'
  if (hour < 17) return 'مساء الخير! استمر في العمل الرائع'
  return 'مساء الخير! نتمنى أن يكون يومك كان مثمراً'
}

const formatDate = (date, format = 'date') => {
  return window.SanadanHelpers.formatDate(date, format)
}

const formatCurrency = (amount) => {
  return window.SanadanHelpers.formatCurrency(amount)
}

const getHearingStatusColor = (status) => {
  const colors = {
    scheduled: 'blue',
    completed: 'green',
    postponed: 'yellow',
    cancelled: 'red'
  }
  return colors[status] || 'gray'
}

const getHearingStatusLabel = (status) => {
  const labels = {
    scheduled: 'مجدولة',
    completed: 'مكتملة',
    postponed: 'مؤجلة',
    cancelled: 'ملغية'
  }
  return labels[status] || status
}

const getPriorityColor = (priority) => {
  const colors = {
    low: 'green',
    medium: 'yellow',
    high: 'orange',
    urgent: 'red'
  }
  return colors[priority] || 'gray'
}

const getPriorityLabel = (priority) => {
  const labels = {
    low: 'منخفضة',
    medium: 'متوسطة',
    high: 'عالية',
    urgent: 'عاجل'
  }
  return labels[priority] || priority
}

// وظائف التنقل
const goToHearing = (hearingId) => {
  router.visit(`/hearings/${hearingId}`)
}

const goToTask = (taskId) => {
  router.visit(`/tasks/${taskId}`)
}

// تبديل حالة المهمة
const toggleTaskStatus = async (task) => {
  try {
    const newStatus = task.status === 'completed' ? 'pending' : 'completed'
    await axios.patch(`/tasks/${task.id}`, { status: newStatus })
    task.status = newStatus
    toast.success('تم تحديث حالة المهمة بنجاح')
  } catch (error) {
    toast.error('حدث خطأ أثناء تحديث المهمة')
  }
}

// تحميل البيانات
const loadDashboardData = async () => {
  try {
    loading.value = true
    const response = await axios.get('/dashboard/stats')
    const data = response.data
    
    stats.value = data.stats
    upcomingHearings.value = data.upcoming_hearings
    urgentTasks.value = data.urgent_tasks
    recentActivities.value = data.recent_activities
  } catch (error) {
    console.error('خطأ في تحميل بيانات لوحة التحكم:', error)
    toast.error('حدث خطأ أثناء تحميل البيانات')
  } finally {
    loading.value = false
  }
}

// تحميل البيانات عند تحميل المكون
onMounted(() => {
  loadDashboardData()
  
  // تحديث البيانات كل 5 دقائق
  setInterval(loadDashboardData, 300000)
})
</script>

<style scoped>
/* تحسينات إضافية لصفحة لوحة التحكم */
.dashboard-grid {
  display: grid;
  gap: 1.5rem;
}

.stats-card {
  transition: transform 0.2s ease-in-out;
}

.stats-card:hover {
  transform: translateY(-2px);
}

.chart-container {
  position: relative;
  height: 300px;
}

/* تحسين الرسوم المتحركة */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* تحسين عرض البيانات على الشاشات الصغيرة */
@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
}
</style>

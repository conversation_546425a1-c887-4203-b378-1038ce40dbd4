import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  StyleSheet,
  Dimensions,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  FAB,
  Portal,
  Modal,
  List,
  Divider,
  Badge,
  Avatar,
  IconButton,
} from 'react-native-paper';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-native-chart-kit';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import { useQuery } from 'react-query';

// المكونات والخدمات
import { useAuth } from '../../contexts/AuthContext';
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { dashboardService } from '../../services/dashboardService';
import { DashboardStats, LegalCase, Hearing, Task } from '../../types';
import LoadingSpinner from '../../components/LoadingSpinner';
import ErrorMessage from '../../components/ErrorMessage';
import StatsCard from '../../components/StatsCard';
import QuickActionCard from '../../components/QuickActionCard';
import RecentActivityCard from '../../components/RecentActivityCard';
import UpcomingHearingsCard from '../../components/UpcomingHearingsCard';
import PendingTasksCard from '../../components/PendingTasksCard';

const { width } = Dimensions.get('window');

export default function DashboardScreen() {
  const navigation = useNavigation();
  const { user } = useAuth();
  const { theme } = useTheme();
  const { t } = useLanguage();

  const [refreshing, setRefreshing] = useState(false);
  const [fabOpen, setFabOpen] = useState(false);
  const [quickCreateVisible, setQuickCreateVisible] = useState(false);

  // استعلام بيانات لوحة التحكم
  const {
    data: stats,
    isLoading,
    error,
    refetch,
  } = useQuery<DashboardStats>(
    'dashboardStats',
    () => dashboardService.getStats(),
    {
      refetchInterval: 5 * 60 * 1000, // تحديث كل 5 دقائق
      staleTime: 2 * 60 * 1000, // البيانات صالحة لمدة دقيقتين
    }
  );

  // استعلام الجلسات القادمة
  const { data: upcomingHearings } = useQuery<Hearing[]>(
    'upcomingHearings',
    () => dashboardService.getUpcomingHearings(),
    {
      refetchInterval: 10 * 60 * 1000,
    }
  );

  // استعلام المهام المعلقة
  const { data: pendingTasks } = useQuery<Task[]>(
    'pendingTasks',
    () => dashboardService.getPendingTasks(),
    {
      refetchInterval: 10 * 60 * 1000,
    }
  );

  // استعلام القضايا الحديثة
  const { data: recentCases } = useQuery<LegalCase[]>(
    'recentCases',
    () => dashboardService.getRecentCases(),
    {
      refetchInterval: 15 * 60 * 1000,
    }
  );

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('خطأ في تحديث البيانات:', error);
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  const handleQuickAction = (action: string) => {
    setQuickCreateVisible(false);
    setFabOpen(false);

    switch (action) {
      case 'case':
        navigation.navigate('Cases', { screen: 'CreateCase' });
        break;
      case 'client':
        navigation.navigate('Clients', { screen: 'CreateClient' });
        break;
      case 'hearing':
        navigation.navigate('More', { screen: 'CreateHearing' });
        break;
      case 'task':
        navigation.navigate('More', { screen: 'CreateTask' });
        break;
      case 'invoice':
        navigation.navigate('More', { screen: 'CreateInvoice' });
        break;
      default:
        break;
    }
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return t('goodMorning');
    if (hour < 17) return t('goodAfternoon');
    return t('goodEvening');
  };

  const chartConfig = {
    backgroundColor: theme.colors.surface,
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(59, 130, 246, ${opacity})`,
    labelColor: (opacity = 1) => theme.colors.onSurface,
    style: {
      borderRadius: 16,
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: theme.colors.primary,
    },
  };

  if (isLoading && !stats) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <ErrorMessage
        message={t('errorLoadingDashboard')}
        onRetry={refetch}
      />
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* ترحيب المستخدم */}
        <Card style={[styles.welcomeCard, { backgroundColor: theme.colors.primary }]}>
          <Card.Content>
            <View style={styles.welcomeHeader}>
              <View style={styles.welcomeText}>
                <Text style={[styles.greeting, { color: theme.colors.onPrimary }]}>
                  {getGreeting()}
                </Text>
                <Text style={[styles.userName, { color: theme.colors.onPrimary }]}>
                  {user?.name}
                </Text>
                <Text style={[styles.welcomeSubtext, { color: theme.colors.onPrimary }]}>
                  {t('dashboardWelcome')}
                </Text>
              </View>
              <Avatar.Image
                size={60}
                source={{ uri: user?.avatar || 'https://via.placeholder.com/60' }}
              />
            </View>
          </Card.Content>
        </Card>

        {/* بطاقات الإحصائيات */}
        <View style={styles.statsContainer}>
          <View style={styles.statsRow}>
            <StatsCard
              title={t('totalCases')}
              value={stats?.totalCases || 0}
              icon="folder"
              color={theme.colors.primary}
              onPress={() => navigation.navigate('Cases')}
            />
            <StatsCard
              title={t('activeCases')}
              value={stats?.activeCases || 0}
              icon="folder-open"
              color={theme.colors.warning}
              onPress={() => navigation.navigate('Cases')}
            />
          </View>
          <View style={styles.statsRow}>
            <StatsCard
              title={t('upcomingHearings')}
              value={stats?.upcomingHearings || 0}
              icon="calendar"
              color={theme.colors.info}
              onPress={() => navigation.navigate('More', { screen: 'Hearings' })}
            />
            <StatsCard
              title={t('pendingTasks')}
              value={stats?.pendingTasks || 0}
              icon="check-circle"
              color={theme.colors.success}
              onPress={() => navigation.navigate('More', { screen: 'Tasks' })}
            />
          </View>
        </View>

        {/* الرسوم البيانية */}
        {stats?.revenueByMonth && (
          <Card style={styles.chartCard}>
            <Card.Title title={t('monthlyRevenue')} />
            <Card.Content>
              <LineChart
                data={{
                  labels: stats.revenueByMonth.map(item => item.month),
                  datasets: [{
                    data: stats.revenueByMonth.map(item => item.revenue),
                  }],
                }}
                width={width - 60}
                height={220}
                chartConfig={chartConfig}
                bezier
                style={styles.chart}
              />
            </Card.Content>
          </Card>
        )}

        {/* توزيع القضايا */}
        {stats?.casesByStatus && (
          <Card style={styles.chartCard}>
            <Card.Title title={t('casesByStatus')} />
            <Card.Content>
              <PieChart
                data={Object.entries(stats.casesByStatus).map(([status, count], index) => ({
                  name: t(`caseStatus.${status}`),
                  population: count,
                  color: getStatusColor(status, index),
                  legendFontColor: theme.colors.onSurface,
                  legendFontSize: 12,
                }))}
                width={width - 60}
                height={220}
                chartConfig={chartConfig}
                accessor="population"
                backgroundColor="transparent"
                paddingLeft="15"
                center={[10, 50]}
                absolute
              />
            </Card.Content>
          </Card>
        )}

        {/* الجلسات القادمة */}
        <UpcomingHearingsCard
          hearings={upcomingHearings || []}
          onViewAll={() => navigation.navigate('More', { screen: 'Hearings' })}
          onHearingPress={(hearingId) =>
            navigation.navigate('More', { screen: 'HearingDetails', params: { hearingId } })
          }
        />

        {/* المهام المعلقة */}
        <PendingTasksCard
          tasks={pendingTasks || []}
          onViewAll={() => navigation.navigate('More', { screen: 'Tasks' })}
          onTaskPress={(taskId) =>
            navigation.navigate('More', { screen: 'TaskDetails', params: { taskId } })
          }
        />

        {/* القضايا الحديثة */}
        <RecentActivityCard
          cases={recentCases || []}
          onViewAll={() => navigation.navigate('Cases')}
          onCasePress={(caseId) =>
            navigation.navigate('Cases', { screen: 'CaseDetails', params: { caseId } })
          }
        />

        {/* مساحة إضافية في الأسفل */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* زر الإجراءات السريعة */}
      <Portal>
        <FAB.Group
          open={fabOpen}
          visible
          icon={fabOpen ? 'close' : 'plus'}
          actions={[
            {
              icon: 'folder-plus',
              label: t('newCase'),
              onPress: () => handleQuickAction('case'),
            },
            {
              icon: 'account-plus',
              label: t('newClient'),
              onPress: () => handleQuickAction('client'),
            },
            {
              icon: 'calendar-plus',
              label: t('newHearing'),
              onPress: () => handleQuickAction('hearing'),
            },
            {
              icon: 'check-circle-outline',
              label: t('newTask'),
              onPress: () => handleQuickAction('task'),
            },
            {
              icon: 'file-document-outline',
              label: t('newInvoice'),
              onPress: () => handleQuickAction('invoice'),
            },
          ]}
          onStateChange={({ open }) => setFabOpen(open)}
          onPress={() => {
            if (fabOpen) {
              // إغلاق القائمة
            }
          }}
        />
      </Portal>
    </View>
  );
}

const getStatusColor = (status: string, index: number) => {
  const colors = [
    '#3b82f6', '#f59e0b', '#6b7280', '#22c55e',
    '#ef4444', '#8b5cf6', '#f97316', '#06b6d4'
  ];
  return colors[index % colors.length];
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  welcomeCard: {
    margin: 16,
    marginBottom: 8,
  },
  welcomeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  welcomeText: {
    flex: 1,
  },
  greeting: {
    fontSize: 16,
    opacity: 0.9,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginVertical: 4,
  },
  welcomeSubtext: {
    fontSize: 14,
    opacity: 0.8,
  },
  statsContainer: {
    paddingHorizontal: 16,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  chartCard: {
    margin: 16,
    marginBottom: 8,
  },
  chart: {
    marginVertical: 8,
    borderRadius: 16,
  },
  bottomSpacing: {
    height: 100,
  },
});

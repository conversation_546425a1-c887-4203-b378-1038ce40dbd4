import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  ScrollView,
  RefreshControl,
  StyleSheet,
  Dimensions,
  Alert,
  TouchableOpacity,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  FAB,
  Portal,
  Modal,
  List,
  Divider,
  Badge,
  Avatar,
  IconButton,
  Chip,
  ProgressBar,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useNavigation } from '@react-navigation/native';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

// المكونات والخدمات
import { useAuth } from '../../contexts/AuthContext';
import { colors } from '../../theme/theme';

// بيانات وهمية للعرض التوضيحي
const mockStats = {
  totalCases: 127,
  activeCases: 89,
  upcomingHearings: 12,
  pendingTasks: 24,
  overdueInvoices: 3,
  monthlyRevenue: 15750,
  totalClients: 245,
  totalHours: 1840,
};

const mockUpcomingHearings = [
  {
    id: '1',
    title: 'قضية التجارة الإلكترونية',
    court: 'محكمة الكويت الكلية',
    date: new Date(2024, 11, 25, 10, 0),
    caseNumber: 'TC-2024-001',
    status: 'scheduled',
  },
  {
    id: '2',
    title: 'قضية العقار التجاري',
    court: 'محكمة الجهراء',
    date: new Date(2024, 11, 27, 14, 30),
    caseNumber: 'RE-2024-045',
    status: 'scheduled',
  },
];

const mockPendingTasks = [
  {
    id: '1',
    title: 'مراجعة عقد الشراكة',
    dueDate: new Date(),
    priority: 'urgent',
    assignee: 'أحمد المحامي',
  },
  {
    id: '2',
    title: 'إعداد مذكرة دفاع',
    dueDate: new Date(Date.now() + 86400000),
    priority: 'high',
    assignee: 'سارة المساعدة',
  },
];

const mockRecentCases = [
  {
    id: '1',
    caseNumber: 'CM-2024-089',
    title: 'قضية نزاع تجاري',
    client: 'شركة الخليج للتجارة',
    status: 'in_progress',
    lastUpdate: new Date(Date.now() - 3600000),
  },
  {
    id: '2',
    caseNumber: 'LB-2024-156',
    title: 'قضية عمالية',
    client: 'محمد أحمد الصالح',
    status: 'pending',
    lastUpdate: new Date(Date.now() - 7200000),
  },
];

const { width } = Dimensions.get('window');

export default function DashboardScreen() {
  const navigation = useNavigation();
  const { user } = useAuth();

  const [refreshing, setRefreshing] = useState(false);
  const [fabOpen, setFabOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // استخدام البيانات الوهمية
  const stats = mockStats;
  const upcomingHearings = mockUpcomingHearings;
  const pendingTasks = mockPendingTasks;
  const recentCases = mockRecentCases;

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // محاكاة تحديث البيانات
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      console.error('خطأ في تحديث البيانات:', error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  const handleQuickAction = (action: string) => {
    setFabOpen(false);
    Alert.alert('إجراء سريع', `تم اختيار: ${action}`);
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'صباح الخير';
    if (hour < 17) return 'مساء الخير';
    return 'مساء الخير';
  };

  const formatDate = (date: Date) => {
    return format(date, 'dd MMM yyyy', { locale: ar });
  };

  const formatTime = (date: Date) => {
    return format(date, 'HH:mm', { locale: ar });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return colors.secondary[500];
      case 'high': return colors.warning[500];
      case 'medium': return colors.info[500];
      case 'low': return colors.success[500];
      default: return colors.gray[500];
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in_progress': return colors.warning[500];
      case 'pending': return colors.gray[500];
      case 'completed': return colors.success[500];
      case 'scheduled': return colors.primary[500];
      default: return colors.gray[500];
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.gray[50] }]}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* ترحيب المستخدم */}
        <Card style={[styles.welcomeCard, { backgroundColor: colors.primary[600] }]}>
          <Card.Content>
            <View style={styles.welcomeHeader}>
              <View style={styles.welcomeText}>
                <Text style={[styles.greeting, { color: colors.white }]}>
                  {getGreeting()}
                </Text>
                <Text style={[styles.userName, { color: colors.white }]}>
                  {user?.name || 'أحمد المحامي'}
                </Text>
                <Text style={[styles.welcomeSubtext, { color: colors.white }]}>
                  مرحباً بك في منصة سندان
                </Text>
              </View>
              <Avatar.Text
                size={60}
                label={user?.name?.charAt(0) || 'أ'}
                style={{ backgroundColor: colors.white }}
                labelStyle={{ color: colors.primary[600] }}
              />
            </View>
          </Card.Content>
        </Card>

        {/* بطاقات الإحصائيات */}
        <View style={styles.statsContainer}>
          <View style={styles.statsRow}>
            <TouchableOpacity style={[styles.statCard, { backgroundColor: colors.white }]}>
              <View style={[styles.statIcon, { backgroundColor: colors.primary[100] }]}>
                <Icon name="folder" size={24} color={colors.primary[600]} />
              </View>
              <Text style={[styles.statValue, { color: colors.gray[900] }]}>{stats.totalCases}</Text>
              <Text style={[styles.statLabel, { color: colors.gray[600] }]}>إجمالي القضايا</Text>
            </TouchableOpacity>

            <TouchableOpacity style={[styles.statCard, { backgroundColor: colors.white }]}>
              <View style={[styles.statIcon, { backgroundColor: colors.warning[100] }]}>
                <Icon name="folder-open" size={24} color={colors.warning[600]} />
              </View>
              <Text style={[styles.statValue, { color: colors.gray[900] }]}>{stats.activeCases}</Text>
              <Text style={[styles.statLabel, { color: colors.gray[600] }]}>القضايا النشطة</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.statsRow}>
            <TouchableOpacity style={[styles.statCard, { backgroundColor: colors.white }]}>
              <View style={[styles.statIcon, { backgroundColor: colors.info[100] }]}>
                <Icon name="calendar" size={24} color={colors.info[600]} />
              </View>
              <Text style={[styles.statValue, { color: colors.gray[900] }]}>{stats.upcomingHearings}</Text>
              <Text style={[styles.statLabel, { color: colors.gray[600] }]}>جلسات قادمة</Text>
            </TouchableOpacity>

            <TouchableOpacity style={[styles.statCard, { backgroundColor: colors.white }]}>
              <View style={[styles.statIcon, { backgroundColor: colors.success[100] }]}>
                <Icon name="check-circle" size={24} color={colors.success[600]} />
              </View>
              <Text style={[styles.statValue, { color: colors.gray[900] }]}>{stats.pendingTasks}</Text>
              <Text style={[styles.statLabel, { color: colors.gray[600] }]}>مهام معلقة</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* الإيرادات الشهرية */}
        <Card style={[styles.card, { backgroundColor: colors.white }]}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <Text style={[styles.cardTitle, { color: colors.gray[900] }]}>الإيرادات الشهرية</Text>
              <Text style={[styles.cardValue, { color: colors.success[600] }]}>
                {stats.monthlyRevenue.toLocaleString('ar-KW')} د.ك
              </Text>
            </View>
            <View style={styles.progressContainer}>
              <ProgressBar progress={0.75} color={colors.success[500]} style={styles.progressBar} />
              <Text style={[styles.progressText, { color: colors.gray[600] }]}>75% من الهدف الشهري</Text>
            </View>
          </Card.Content>
        </Card>

        {/* الجلسات القادمة */}
        <Card style={[styles.card, { backgroundColor: colors.white }]}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <Text style={[styles.cardTitle, { color: colors.gray[900] }]}>الجلسات القادمة</Text>
              <Button mode="text" onPress={() => Alert.alert('عرض الكل', 'سيتم فتح صفحة الجلسات')}>
                عرض الكل
              </Button>
            </View>
            <View style={styles.listContainer}>
              {upcomingHearings.map((hearing) => (
                <TouchableOpacity key={hearing.id} style={styles.listItem}>
                  <View style={styles.listItemContent}>
                    <Text style={[styles.listItemTitle, { color: colors.gray[900] }]}>
                      {hearing.title}
                    </Text>
                    <Text style={[styles.listItemSubtitle, { color: colors.gray[600] }]}>
                      {hearing.court}
                    </Text>
                  </View>
                  <View style={styles.listItemMeta}>
                    <Text style={[styles.listItemTime, { color: colors.primary[600] }]}>
                      {formatDate(hearing.date)}
                    </Text>
                    <Text style={[styles.listItemTime, { color: colors.primary[600] }]}>
                      {formatTime(hearing.date)}
                    </Text>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </Card.Content>
        </Card>

        {/* المهام المعلقة */}
        <Card style={[styles.card, { backgroundColor: colors.white }]}>
          <Card.Content>
            <View style={styles.cardHeader}>
              <Text style={[styles.cardTitle, { color: colors.gray[900] }]}>المهام العاجلة</Text>
              <Button mode="text" onPress={() => Alert.alert('عرض الكل', 'سيتم فتح صفحة المهام')}>
                عرض الكل
              </Button>
            </View>
            <View style={styles.listContainer}>
              {pendingTasks.map((task) => (
                <TouchableOpacity key={task.id} style={styles.listItem}>
                  <View style={styles.taskPriority}>
                    <View style={[styles.priorityDot, { backgroundColor: getPriorityColor(task.priority) }]} />
                  </View>
                  <View style={styles.listItemContent}>
                    <Text style={[styles.listItemTitle, { color: colors.gray[900] }]}>
                      {task.title}
                    </Text>
                    <Text style={[styles.listItemSubtitle, { color: colors.gray[600] }]}>
                      مكلف: {task.assignee}
                    </Text>
                  </View>
                  <View style={styles.listItemMeta}>
                    <Chip
                      mode="flat"
                      style={[styles.priorityChip, { backgroundColor: `${getPriorityColor(task.priority)}20` }]}
                      textStyle={{ color: getPriorityColor(task.priority), fontSize: 12 }}
                    >
                      {task.priority === 'urgent' ? 'عاجل' : task.priority === 'high' ? 'عالي' : 'متوسط'}
                    </Chip>
                  </View>
                </TouchableOpacity>
              ))}
            </View>
          </Card.Content>
        </Card>

        {/* مساحة إضافية في الأسفل */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* زر الإجراءات السريعة */}
      <Portal>
        <FAB.Group
          open={fabOpen}
          visible
          icon={fabOpen ? 'close' : 'plus'}
          actions={[
            {
              icon: 'folder-plus',
              label: 'قضية جديدة',
              onPress: () => handleQuickAction('قضية جديدة'),
            },
            {
              icon: 'account-plus',
              label: 'عميل جديد',
              onPress: () => handleQuickAction('عميل جديد'),
            },
            {
              icon: 'calendar-plus',
              label: 'جلسة جديدة',
              onPress: () => handleQuickAction('جلسة جديدة'),
            },
            {
              icon: 'check-circle-outline',
              label: 'مهمة جديدة',
              onPress: () => handleQuickAction('مهمة جديدة'),
            },
            {
              icon: 'file-document-outline',
              label: 'فاتورة جديدة',
              onPress: () => handleQuickAction('فاتورة جديدة'),
            },
          ]}
          onStateChange={({ open }) => setFabOpen(open)}
          onPress={() => {
            if (fabOpen) {
              // إغلاق القائمة
            }
          }}
        />
      </Portal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  welcomeCard: {
    margin: 16,
    marginBottom: 8,
    borderRadius: 16,
    elevation: 4,
  },
  welcomeHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  welcomeText: {
    flex: 1,
  },
  greeting: {
    fontSize: 16,
    opacity: 0.9,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginVertical: 4,
  },
  welcomeSubtext: {
    fontSize: 14,
    opacity: 0.8,
  },
  statsContainer: {
    paddingHorizontal: 16,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
    gap: 8,
  },
  statCard: {
    flex: 1,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 2,
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
  },
  card: {
    margin: 16,
    marginBottom: 8,
    borderRadius: 12,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  cardValue: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  progressContainer: {
    marginTop: 8,
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  progressText: {
    fontSize: 12,
    textAlign: 'center',
  },
  listContainer: {
    gap: 8,
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: colors.gray[50],
    borderRadius: 8,
  },
  listItemContent: {
    flex: 1,
    marginLeft: 12,
  },
  listItemTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 4,
  },
  listItemSubtitle: {
    fontSize: 12,
  },
  listItemMeta: {
    alignItems: 'flex-end',
  },
  listItemTime: {
    fontSize: 12,
    fontWeight: '500',
  },
  taskPriority: {
    width: 4,
    height: 40,
    borderRadius: 2,
  },
  priorityDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  priorityChip: {
    height: 24,
  },
  bottomSpacing: {
    height: 100,
  },
});

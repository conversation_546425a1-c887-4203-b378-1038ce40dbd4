<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\LegalCase;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

/**
 * متحكم إدارة العملاء
 */
class ClientController extends Controller
{
    /**
     * عرض قائمة العملاء
     */
    public function index(Request $request)
    {
        $query = Client::with(['creator', 'cases'])
            ->withCount(['cases', 'invoices'])
            ->latest();

        // تطبيق الفلاتر
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        if ($request->filled('client_type')) {
            $query->ofType($request->client_type);
        }

        if ($request->filled('status')) {
            $query->withStatus($request->status);
        }

        if ($request->filled('nationality')) {
            $query->where('nationality', $request->nationality);
        }

        $clients = $query->paginate(15);

        // إحصائيات العملاء
        $stats = [
            'total' => Client::count(),
            'active' => Client::where('status', 'active')->count(),
            'companies' => Client::where('client_type', 'company')->count(),
            'individuals' => Client::where('client_type', 'individual')->count(),
        ];

        return view('clients.index', compact('clients', 'stats'));
    }

    /**
     * عرض نموذج إنشاء عميل جديد
     */
    public function create()
    {
        return view('clients.create');
    }

    /**
     * حفظ عميل جديد
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'nullable|email|unique:clients,email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'id_number' => 'nullable|string|max:20|unique:clients,id_number',
            'client_type' => 'required|in:individual,company,government',
            'company_name' => 'nullable|required_if:client_type,company|string|max:255',
            'commercial_registration' => 'nullable|string|max:50',
            'nationality' => 'nullable|string|max:50',
            'gender' => 'nullable|in:male,female',
            'birth_date' => 'nullable|date|before:today',
            'notes' => 'nullable|string',
            
            // تفضيلات التواصل
            'preferred_contact_method' => 'nullable|in:phone,email,whatsapp,sms',
            'preferred_language' => 'nullable|in:ar,en',
            'contact_time_preference' => 'nullable|string',
            
            // الملفات
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'id_documents.*' => 'nullable|file|mimes:pdf,jpeg,png,jpg|max:5120',
        ]);

        $validated['created_by'] = Auth::id();

        // إعداد تفضيلات التواصل
        $contactPreferences = [];
        if ($request->filled('preferred_contact_method')) {
            $contactPreferences['method'] = $request->preferred_contact_method;
        }
        if ($request->filled('preferred_language')) {
            $contactPreferences['language'] = $request->preferred_language;
        }
        if ($request->filled('contact_time_preference')) {
            $contactPreferences['time'] = $request->contact_time_preference;
        }
        $validated['contact_preferences'] = $contactPreferences;

        $client = Client::create($validated);

        // رفع الصورة الشخصية
        if ($request->hasFile('avatar')) {
            $client->addMediaFromRequest('avatar')
                ->toMediaCollection('avatar');
        }

        // رفع مستندات الهوية
        if ($request->hasFile('id_documents')) {
            foreach ($request->file('id_documents') as $file) {
                $client->addMedia($file)
                    ->toMediaCollection('id_documents');
            }
        }

        return redirect()->route('clients.show', $client)
            ->with('success', 'تم إنشاء العميل بنجاح');
    }

    /**
     * عرض تفاصيل العميل
     */
    public function show(Client $client)
    {
        $client->load([
            'creator',
            'cases' => function($query) {
                $query->with(['lawyer', 'caseType'])
                      ->orderBy('created_at', 'desc');
            },
            'invoices' => function($query) {
                $query->orderBy('created_at', 'desc');
            },
            'tasks' => function($query) {
                $query->with('assignedUser')
                      ->orderBy('due_date');
            },
            'hearings' => function($query) {
                $query->with('court')
                      ->orderBy('hearing_date', 'desc');
            }
        ]);

        // إحصائيات العميل
        $stats = [
            'total_cases' => $client->cases->count(),
            'active_cases' => $client->getActiveCasesCount(),
            'total_outstanding' => $client->getTotalOutstandingAmount(),
            'total_paid' => $client->getTotalPaidAmount(),
            'last_case' => $client->getLastCase(),
        ];

        return view('clients.show', compact('client', 'stats'));
    }

    /**
     * عرض نموذج تعديل العميل
     */
    public function edit(Client $client)
    {
        return view('clients.edit', compact('client'));
    }

    /**
     * تحديث بيانات العميل
     */
    public function update(Request $request, Client $client)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => [
                'nullable',
                'email',
                Rule::unique('clients')->ignore($client->id)
            ],
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'id_number' => [
                'nullable',
                'string',
                'max:20',
                Rule::unique('clients')->ignore($client->id)
            ],
            'client_type' => 'required|in:individual,company,government',
            'company_name' => 'nullable|required_if:client_type,company|string|max:255',
            'commercial_registration' => 'nullable|string|max:50',
            'nationality' => 'nullable|string|max:50',
            'gender' => 'nullable|in:male,female',
            'birth_date' => 'nullable|date|before:today',
            'status' => 'required|in:active,inactive,blacklisted',
            'notes' => 'nullable|string',
            
            // تفضيلات التواصل
            'preferred_contact_method' => 'nullable|in:phone,email,whatsapp,sms',
            'preferred_language' => 'nullable|in:ar,en',
            'contact_time_preference' => 'nullable|string',
            
            // الملفات
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'id_documents.*' => 'nullable|file|mimes:pdf,jpeg,png,jpg|max:5120',
        ]);

        // تحديث تفضيلات التواصل
        $contactPreferences = $client->contact_preferences ?? [];
        if ($request->filled('preferred_contact_method')) {
            $contactPreferences['method'] = $request->preferred_contact_method;
        }
        if ($request->filled('preferred_language')) {
            $contactPreferences['language'] = $request->preferred_language;
        }
        if ($request->filled('contact_time_preference')) {
            $contactPreferences['time'] = $request->contact_time_preference;
        }
        $validated['contact_preferences'] = $contactPreferences;

        $client->update($validated);

        // تحديث الصورة الشخصية
        if ($request->hasFile('avatar')) {
            $client->clearMediaCollection('avatar');
            $client->addMediaFromRequest('avatar')
                ->toMediaCollection('avatar');
        }

        // إضافة مستندات هوية جديدة
        if ($request->hasFile('id_documents')) {
            foreach ($request->file('id_documents') as $file) {
                $client->addMedia($file)
                    ->toMediaCollection('id_documents');
            }
        }

        return redirect()->route('clients.show', $client)
            ->with('success', 'تم تحديث بيانات العميل بنجاح');
    }

    /**
     * حذف العميل
     */
    public function destroy(Client $client)
    {
        // التحقق من الصلاحيات
        if (!Auth::user()->isAdmin()) {
            abort(403, 'غير مصرح لك بحذف العملاء');
        }

        // التحقق من وجود قضايا مرتبطة
        if ($client->cases()->count() > 0) {
            return back()->with('error', 'لا يمكن حذف العميل لوجود قضايا مرتبطة به');
        }

        $client->delete();

        return redirect()->route('clients.index')
            ->with('success', 'تم حذف العميل بنجاح');
    }

    /**
     * إضافة العميل إلى القائمة السوداء
     */
    public function blacklist(Request $request, Client $client)
    {
        $validated = $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        $client->blacklist($validated['reason']);

        return back()->with('success', 'تم إضافة العميل إلى القائمة السوداء');
    }

    /**
     * إزالة العميل من القائمة السوداء
     */
    public function removeFromBlacklist(Client $client)
    {
        $client->removeFromBlacklist();

        return back()->with('success', 'تم إزالة العميل من القائمة السوداء');
    }

    /**
     * تصدير قائمة العملاء
     */
    public function export(Request $request)
    {
        // سيتم تنفيذ هذه الوظيفة لاحقاً باستخدام Laravel Excel
        return back()->with('info', 'ميزة التصدير قيد التطوير');
    }

    /**
     * البحث السريع في العملاء (AJAX)
     */
    public function quickSearch(Request $request)
    {
        $search = $request->get('q');
        
        $clients = Client::where(function($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%")
                      ->orWhere('id_number', 'like', "%{$search}%")
                      ->orWhere('company_name', 'like', "%{$search}%");
            })
            ->limit(10)
            ->get(['id', 'name', 'email', 'phone', 'client_type', 'company_name']);

        return response()->json($clients);
    }

    /**
     * الحصول على إحصائيات العملاء (API)
     */
    public function getStats()
    {
        $stats = [
            'total' => Client::count(),
            'active' => Client::where('status', 'active')->count(),
            'inactive' => Client::where('status', 'inactive')->count(),
            'blacklisted' => Client::where('status', 'blacklisted')->count(),
            'by_type' => Client::selectRaw('client_type, count(*) as count')
                ->groupBy('client_type')
                ->pluck('count', 'client_type'),
            'by_nationality' => Client::selectRaw('nationality, count(*) as count')
                ->whereNotNull('nationality')
                ->groupBy('nationality')
                ->orderBy('count', 'desc')
                ->limit(10)
                ->pluck('count', 'nationality'),
            'recent' => Client::latest()->limit(5)->get(['id', 'name', 'client_type', 'created_at']),
        ];

        return response()->json($stats);
    }

    /**
     * حذف مستند من مجموعة المستندات
     */
    public function deleteDocument(Client $client, $mediaId)
    {
        $media = $client->getMedia()->find($mediaId);
        
        if ($media) {
            $media->delete();
            return back()->with('success', 'تم حذف المستند بنجاح');
        }

        return back()->with('error', 'المستند غير موجود');
    }

    /**
     * تحديث معلومة إضافية للعميل
     */
    public function updateAdditionalInfo(Request $request, Client $client)
    {
        $validated = $request->validate([
            'key' => 'required|string|max:100',
            'value' => 'required|string|max:500',
        ]);

        $client->addAdditionalInfo($validated['key'], $validated['value']);

        return back()->with('success', 'تم تحديث المعلومات الإضافية');
    }

    /**
     * تحديث تفضيل تواصل
     */
    public function updateContactPreference(Request $request, Client $client)
    {
        $validated = $request->validate([
            'key' => 'required|string|max:50',
            'value' => 'required|string|max:100',
        ]);

        $client->updateContactPreference($validated['key'], $validated['value']);

        return back()->with('success', 'تم تحديث تفضيلات التواصل');
    }

    /**
     * عرض ملف العميل للطباعة
     */
    public function printProfile(Client $client)
    {
        $client->load([
            'cases.lawyer',
            'cases.caseType',
            'invoices',
            'creator'
        ]);

        return view('clients.print', compact('client'));
    }

    /**
     * إنشاء قضية جديدة للعميل
     */
    public function createCase(Client $client)
    {
        return redirect()->route('cases.create', ['client_id' => $client->id]);
    }

    /**
     * إنشاء فاتورة جديدة للعميل
     */
    public function createInvoice(Client $client)
    {
        return redirect()->route('invoices.create', ['client_id' => $client->id]);
    }
}

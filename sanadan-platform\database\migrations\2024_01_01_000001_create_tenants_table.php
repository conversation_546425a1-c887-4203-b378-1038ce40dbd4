<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل الهجرة
     */
    public function up(): void
    {
        Schema::create('tenants', function (Blueprint $table) {
            $table->string('id')->primary();
            $table->string('name'); // اسم مكتب المحاماة
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            $table->string('license_number')->nullable(); // رقم ترخيص المحاماة
            $table->json('settings')->nullable(); // الإعدادات المخصصة
            $table->enum('subscription_plan', ['basic', 'professional', 'enterprise'])->default('basic');
            $table->timestamp('subscription_ends_at')->nullable();
            $table->enum('status', ['active', 'suspended', 'expired'])->default('active');
            $table->timestamps();
            
            $table->index(['status', 'subscription_ends_at']);
        });
    }

    /**
     * التراجع عن الهجرة
     */
    public function down(): void
    {
        Schema::dropIfExists('tenants');
    }
};

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - منصة سندان</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>

    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }

        /* Desktop Layout */
        @media (min-width: 769px) {
            .desktop-layout { display: block !important; }
            .mobile-layout { display: none !important; }
        }

        /* Mobile Layout */
        @media (max-width: 768px) {
            .desktop-layout { display: none !important; }
            .mobile-layout { display: block !important; }
        }

        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }

        .slide-in {
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        .notification-badge {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .counter-animation {
            animation: countUp 2s ease-out;
        }

        @keyframes countUp {
            from { transform: scale(0.5); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }
    </style>
</head>
<body class="bg-gray-50 font-arabic" x-data="dashboardApp()" x-init="init()">
    <!-- Desktop Layout -->
    <div class="desktop-layout min-h-screen bg-gray-50">
        <!-- Sidebar -->
        <div class="fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg sidebar-transition"
             x-show="sidebarOpen || window.innerWidth >= 768">

            <!-- Logo and Tenant Info -->
            <div class="flex items-center justify-center p-6 border-b border-gray-200">
                <div class="text-center">
                    <div class="w-12 h-12 mx-auto mb-2 bg-primary-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-balance-scale text-white text-xl"></i>
                    </div>
                    <h2 class="text-lg font-bold text-gray-900" x-text="user.officeName || user.name"></h2>
                    <p class="text-sm text-gray-500">نسخة احترافية</p>
                </div>
            </div>

            <!-- User Info -->
            <div class="p-4 border-b border-gray-200">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <img :src="user.avatar" :alt="user.name" class="w-10 h-10 rounded-full">
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate" x-text="user.name"></p>
                        <p class="text-xs text-gray-500 truncate" x-text="user.role === 'owner' ? 'مالك المكتب' : 'محامي'"></p>
                    </div>
                </div>
            </div>

            <!-- Navigation Menu -->
            <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
                <!-- Dashboard -->
                <a href="#" @click="switchPage('dashboard')"
                   class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
                   :class="currentPage === 'dashboard' ? 'bg-primary-100 text-primary-700 border-r-4 border-primary-600' : 'text-gray-700 hover:bg-gray-100'">
                    <i class="fas fa-tachometer-alt w-5 h-5 ml-3"></i>
                    <span>لوحة التحكم</span>
                </a>

                <!-- Cases -->
                <a href="#" @click="switchPage('cases')"
                   class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
                   :class="currentPage === 'cases' ? 'bg-primary-100 text-primary-700 border-r-4 border-primary-600' : 'text-gray-700 hover:bg-gray-100'">
                    <i class="fas fa-folder w-5 h-5 ml-3"></i>
                    <span>القضايا</span>
                    <span class="mr-auto bg-blue-500 text-white text-xs rounded-full px-2 py-1" x-text="stats.totalCases"></span>
                </a>

                <!-- Clients -->
                <a href="#" @click="switchPage('clients')"
                   class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
                   :class="currentPage === 'clients' ? 'bg-primary-100 text-primary-700 border-r-4 border-primary-600' : 'text-gray-700 hover:bg-gray-100'">
                    <i class="fas fa-users w-5 h-5 ml-3"></i>
                    <span>العملاء</span>
                    <span class="mr-auto bg-green-500 text-white text-xs rounded-full px-2 py-1" x-text="stats.totalClients"></span>
                </a>

                <!-- Calendar -->
                <a href="#" @click="switchPage('calendar')"
                   class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
                   :class="currentPage === 'calendar' ? 'bg-primary-100 text-primary-700 border-r-4 border-primary-600' : 'text-gray-700 hover:bg-gray-100'">
                    <i class="fas fa-calendar w-5 h-5 ml-3"></i>
                    <span>التقويم</span>
                </a>

                <!-- Hearings -->
                <a href="#" @click="switchPage('hearings')"
                   class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
                   :class="currentPage === 'hearings' ? 'bg-primary-100 text-primary-700 border-r-4 border-primary-600' : 'text-gray-700 hover:bg-gray-100'">
                    <i class="fas fa-gavel w-5 h-5 ml-3"></i>
                    <span>الجلسات</span>
                    <span class="mr-auto bg-red-500 text-white text-xs rounded-full px-2 py-1 notification-badge" x-text="stats.upcomingHearings"></span>
                </a>

                <!-- Tasks -->
                <a href="#" @click="switchPage('tasks')"
                   class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
                   :class="currentPage === 'tasks' ? 'bg-primary-100 text-primary-700 border-r-4 border-primary-600' : 'text-gray-700 hover:bg-gray-100'">
                    <i class="fas fa-tasks w-5 h-5 ml-3"></i>
                    <span>المهام</span>
                    <span class="mr-auto bg-yellow-500 text-white text-xs rounded-full px-2 py-1" x-text="stats.pendingTasks"></span>
                </a>

                <!-- Documents -->
                <a href="#" @click="switchPage('documents')"
                   class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
                   :class="currentPage === 'documents' ? 'bg-primary-100 text-primary-700 border-r-4 border-primary-600' : 'text-gray-700 hover:bg-gray-100'">
                    <i class="fas fa-file-alt w-5 h-5 ml-3"></i>
                    <span>المستندات</span>
                </a>

                <!-- Finance -->
                <a href="#" @click="switchPage('finance')"
                   class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
                   :class="currentPage === 'finance' ? 'bg-primary-100 text-primary-700 border-r-4 border-primary-600' : 'text-gray-700 hover:bg-gray-100'">
                    <i class="fas fa-file-invoice-dollar w-5 h-5 ml-3"></i>
                    <span>المالية</span>
                </a>

                <!-- Reports -->
                <a href="#" @click="switchPage('reports')"
                   class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
                   :class="currentPage === 'reports' ? 'bg-primary-100 text-primary-700 border-r-4 border-primary-600' : 'text-gray-700 hover:bg-gray-100'">
                    <i class="fas fa-chart-bar w-5 h-5 ml-3"></i>
                    <span>التقارير</span>
                </a>

                <!-- Settings -->
                <a href="#" @click="switchPage('settings')"
                   class="flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors duration-200"
                   :class="currentPage === 'settings' ? 'bg-primary-100 text-primary-700 border-r-4 border-primary-600' : 'text-gray-700 hover:bg-gray-100'">
                    <i class="fas fa-cog w-5 h-5 ml-3"></i>
                    <span>الإعدادات</span>
                </a>
            </nav>

            <!-- Bottom Section -->
            <div class="p-4 border-t border-gray-200">
                <!-- Storage Usage -->
                <div class="mb-4">
                    <div class="flex items-center justify-between text-xs text-gray-600 mb-1">
                        <span>التخزين المستخدم</span>
                        <span x-text="`${stats.storageUsed} / ${stats.storageLimit} GB`"></span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-primary-600 h-2 rounded-full transition-all duration-300"
                             :style="`width: ${(stats.storageUsed / stats.storageLimit) * 100}%`"></div>
                    </div>
                </div>

                <!-- Logout -->
                <button @click="logout()" class="flex items-center w-full px-4 py-2 text-sm font-medium text-red-600 rounded-lg hover:bg-red-50 transition-colors duration-200">
                    <i class="fas fa-sign-out-alt w-5 h-5 ml-3"></i>
                    <span>تسجيل الخروج</span>
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="mr-0 lg:mr-64 transition-all duration-300">
            <!-- Top Navigation -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between px-6 py-4">
                    <!-- Left Side -->
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <button @click="sidebarOpen = !sidebarOpen"
                                class="lg:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100">
                            <i class="fas fa-bars text-xl"></i>
                        </button>

                        <div>
                            <h1 class="text-2xl font-bold text-gray-900" x-text="pageTitle"></h1>
                            <p class="text-sm text-gray-600 mt-1" x-text="pageSubtitle"></p>
                        </div>
                    </div>

                    <!-- Right Side -->
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <!-- Search -->
                        <div class="relative hidden md:block" x-data="{ searchOpen: false, searchQuery: '' }">
                            <input type="text"
                                   x-model="searchQuery"
                                   @focus="searchOpen = true"
                                   @blur="setTimeout(() => searchOpen = false, 200)"
                                   placeholder="البحث في النظام..."
                                   class="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                <i class="fas fa-search text-gray-400"></i>
                            </div>

                            <!-- Search Results -->
                            <div x-show="searchOpen && searchQuery.length > 2"
                                 x-transition
                                 class="absolute top-full left-0 right-0 mt-1 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                                <div class="p-4">
                                    <div class="text-sm text-gray-500 mb-2">نتائج البحث</div>
                                    <div class="space-y-2">
                                        <div class="p-2 hover:bg-gray-50 rounded cursor-pointer" @click="showNotification('تم العثور', 'قضية التجارة الإلكترونية', 'info')">
                                            <div class="font-medium text-sm">قضية التجارة الإلكترونية</div>
                                            <div class="text-xs text-gray-500">قضية #TC-2024-001</div>
                                        </div>
                                        <div class="p-2 hover:bg-gray-50 rounded cursor-pointer" @click="showNotification('تم العثور', 'شركة الخليج للتجارة', 'info')">
                                            <div class="font-medium text-sm">شركة الخليج للتجارة</div>
                                            <div class="text-xs text-gray-500">عميل</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="relative" x-data="{ quickActionsOpen: false }">
                            <button @click="quickActionsOpen = !quickActionsOpen"
                                    class="p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-lg">
                                <i class="fas fa-plus text-xl"></i>
                            </button>

                            <div x-show="quickActionsOpen"
                                 @click.away="quickActionsOpen = false"
                                 x-transition
                                 class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                                <div class="py-2">
                                    <a href="#" @click="showNotification('نجح', 'تم إنشاء قضية جديدة', 'success')"
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-folder-plus w-4 h-4 ml-3 text-blue-500"></i>
                                        قضية جديدة
                                    </a>
                                    <a href="#" @click="showNotification('نجح', 'تم إضافة عميل جديد', 'success')"
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-user-plus w-4 h-4 ml-3 text-green-500"></i>
                                        عميل جديد
                                    </a>
                                    <a href="#" @click="showNotification('نجح', 'تم إنشاء مهمة جديدة', 'success')"
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-tasks w-4 h-4 ml-3 text-yellow-500"></i>
                                        مهمة جديدة
                                    </a>
                                    <a href="#" @click="showNotification('نجح', 'تم جدولة جلسة جديدة', 'success')"
                                       class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-gavel w-4 h-4 ml-3 text-purple-500"></i>
                                        جلسة جديدة
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Notifications -->
                        <div class="relative" x-data="{ notificationsOpen: false }">
                            <button @click="notificationsOpen = !notificationsOpen"
                                    class="relative p-2 text-gray-400 hover:text-gray-500 hover:bg-gray-100 rounded-lg">
                                <i class="fas fa-bell text-xl"></i>
                                <span class="absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center notification-badge" x-text="notifications.length"></span>
                            </button>

                            <div x-show="notificationsOpen"
                                 @click.away="notificationsOpen = false"
                                 x-transition
                                 class="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                                <div class="p-4 border-b border-gray-200">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-lg font-semibold text-gray-900">الإشعارات</h3>
                                        <button @click="clearNotifications()" class="text-sm text-primary-600 hover:text-primary-700">
                                            تحديد الكل كمقروء
                                        </button>
                                    </div>
                                </div>

                                <div class="max-h-96 overflow-y-auto">
                                    <template x-for="notification in notifications" :key="notification.id">
                                        <div class="p-4 border-b border-gray-100 hover:bg-gray-50">
                                            <div class="flex items-start space-x-3 space-x-reverse">
                                                <div class="flex-shrink-0">
                                                    <div class="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                                                        <i class="fas fa-bell text-primary-600 text-sm"></i>
                                                    </div>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900" x-text="notification.title"></p>
                                                    <p class="text-sm text-gray-500" x-text="notification.message"></p>
                                                    <p class="text-xs text-gray-400 mt-1" x-text="notification.time"></p>
                                                </div>
                                            </div>
                                        </div>
                                    </template>

                                    <div x-show="notifications.length === 0" class="p-8 text-center">
                                        <i class="fas fa-bell-slash text-gray-300 text-3xl mb-2"></i>
                                        <p class="text-gray-500">لا توجد إشعارات</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Current Time -->
                        <div class="hidden md:flex items-center text-sm text-gray-500">
                            <i class="fas fa-clock ml-2"></i>
                            <span id="current-time">--:--</span>
                        </div>

                        <!-- User Menu -->
                        <div class="relative" x-data="{ userMenuOpen: false }">
                            <button @click="userMenuOpen = !userMenuOpen"
                                    class="flex items-center space-x-2 space-x-reverse p-2 rounded-lg hover:bg-gray-100">
                                <img :src="user.avatar" :alt="user.name" class="w-8 h-8 rounded-full">
                                <div class="hidden md:block text-right">
                                    <div class="text-sm font-medium text-gray-900" x-text="user.name"></div>
                                    <div class="text-xs text-gray-500" x-text="user.role === 'owner' ? 'مالك المكتب' : 'محامي'"></div>
                                </div>
                                <i class="fas fa-chevron-down text-gray-400 text-sm"></i>
                            </button>

                            <div x-show="userMenuOpen"
                                 @click.away="userMenuOpen = false"
                                 x-transition
                                 class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                                <div class="py-2">
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-user w-4 h-4 ml-3"></i>
                                        الملف الشخصي
                                    </a>
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-cog w-4 h-4 ml-3"></i>
                                        الإعدادات
                                    </a>
                                    <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <i class="fas fa-shield-alt w-4 h-4 ml-3"></i>
                                        الأمان
                                    </a>
                                    <div class="border-t border-gray-100 my-1"></div>
                                    <button @click="logout()" class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                        <i class="fas fa-sign-out-alt w-4 h-4 ml-3"></i>
                                        تسجيل الخروج
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="p-6">
                <div id="page-content" class="fade-in">
                    <!-- Content will be loaded here -->
                </div>
            </main>
        </div>
    </div>

    <!-- Mobile Layout -->
    <div class="mobile-layout min-h-screen bg-gray-50">
        <!-- Mobile Header -->
        <header class="fixed top-0 left-0 right-0 z-40 bg-white shadow-sm border-b border-gray-200">
            <div class="flex items-center justify-between px-4 py-3">
                <div class="flex items-center space-x-3 space-x-reverse">
                    <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-balance-scale text-white text-sm"></i>
                    </div>
                    <div>
                        <h1 class="text-lg font-bold text-gray-900" x-text="user.officeName || user.name"></h1>
                        <p class="text-xs text-gray-500" x-text="pageTitle"></p>
                    </div>
                </div>

                <div class="flex items-center space-x-2 space-x-reverse">
                    <!-- Mobile Search -->
                    <button @click="$dispatch('open-search')" class="p-2 text-gray-400 hover:text-gray-600 rounded-lg">
                        <i class="fas fa-search text-lg"></i>
                    </button>

                    <!-- Mobile Notifications -->
                    <div class="relative" x-data="{ mobileNotificationsOpen: false }">
                        <button @click="mobileNotificationsOpen = !mobileNotificationsOpen"
                                class="relative p-2 text-gray-400 hover:text-gray-600 rounded-lg">
                            <i class="fas fa-bell text-lg"></i>
                            <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center" x-text="notifications.length > 9 ? '9+' : notifications.length"></span>
                        </button>

                        <div x-show="mobileNotificationsOpen"
                             @click.away="mobileNotificationsOpen = false"
                             x-transition
                             class="absolute top-full left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
                             style="transform: translateX(-85%);">
                            <div class="p-4 border-b border-gray-200">
                                <h3 class="text-lg font-semibold text-gray-900">الإشعارات</h3>
                            </div>
                            <div class="max-h-64 overflow-y-auto">
                                <template x-for="notification in notifications.slice(0, 3)" :key="notification.id">
                                    <div class="p-4 border-b border-gray-100">
                                        <div class="flex items-start space-x-3 space-x-reverse">
                                            <div class="flex-shrink-0">
                                                <div class="w-6 h-6 bg-primary-100 rounded-full flex items-center justify-center">
                                                    <i class="fas fa-bell text-primary-600 text-xs"></i>
                                                </div>
                                            </div>
                                            <div class="flex-1 min-w-0">
                                                <p class="text-sm font-medium text-gray-900" x-text="notification.title"></p>
                                                <p class="text-xs text-gray-500 mt-1" x-text="notification.time"></p>
                                            </div>
                                        </div>
                                    </div>
                                </template>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile User Menu -->
                    <div class="relative" x-data="{ mobileUserMenuOpen: false }">
                        <button @click="mobileUserMenuOpen = !mobileUserMenuOpen"
                                class="flex items-center p-1 rounded-lg">
                            <img :src="user.avatar" :alt="user.name" class="w-8 h-8 rounded-full border-2 border-gray-200">
                        </button>

                        <div x-show="mobileUserMenuOpen"
                             @click.away="mobileUserMenuOpen = false"
                             x-transition
                             class="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
                             style="transform: translateX(-85%);">
                            <div class="p-4 border-b border-gray-200">
                                <div class="text-sm font-medium text-gray-900" x-text="user.name"></div>
                                <div class="text-xs text-gray-500" x-text="user.role === 'owner' ? 'مالك المكتب' : 'محامي'"></div>
                            </div>
                            <div class="py-2">
                                <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-user w-4 h-4 ml-3"></i>
                                    الملف الشخصي
                                </a>
                                <a href="#" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                    <i class="fas fa-cog w-4 h-4 ml-3"></i>
                                    الإعدادات
                                </a>
                                <div class="border-t border-gray-100 my-1"></div>
                                <button @click="logout()" class="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                    <i class="fas fa-sign-out-alt w-4 h-4 ml-3"></i>
                                    تسجيل الخروج
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Mobile Content -->
        <main class="pb-20 pt-16">
            <div id="mobile-page-content" class="fade-in">
                <!-- Mobile content will be loaded here -->
            </div>
        </main>

        <!-- Mobile Bottom Navigation -->
        <nav class="fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 shadow-lg">
            <div class="flex items-center justify-around py-2">
                <!-- Dashboard -->
                <a href="#" @click="switchPage('dashboard')"
                   class="flex flex-col items-center py-2 px-3 rounded-lg transition-colors duration-200"
                   :class="currentPage === 'dashboard' ? 'text-primary-600 bg-primary-50' : 'text-gray-500 hover:text-gray-700'">
                    <div class="relative">
                        <i class="fas fa-home text-xl mb-1"></i>
                        <div x-show="currentPage === 'dashboard'" class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary-600 rounded-full"></div>
                    </div>
                    <span class="text-xs font-medium">الرئيسية</span>
                </a>

                <!-- Cases -->
                <div class="relative" x-data="{ casesMenuOpen: false }">
                    <button @click="casesMenuOpen = !casesMenuOpen"
                            class="flex flex-col items-center py-2 px-3 rounded-lg transition-colors duration-200"
                            :class="currentPage === 'cases' ? 'text-primary-600 bg-primary-50' : 'text-gray-500 hover:text-gray-700'">
                        <div class="relative">
                            <i class="fas fa-folder text-xl mb-1"></i>
                            <span class="absolute -top-2 -right-2 h-4 w-4 bg-blue-500 text-white text-xs rounded-full flex items-center justify-center" x-text="stats.totalCases"></span>
                            <div x-show="currentPage === 'cases'" class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary-600 rounded-full"></div>
                        </div>
                        <span class="text-xs font-medium">القضايا</span>
                    </button>

                    <!-- Cases Quick Menu -->
                    <div x-show="casesMenuOpen"
                         @click.away="casesMenuOpen = false"
                         x-transition
                         class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                        <div class="py-2">
                            <a href="#" @click="switchPage('cases'); casesMenuOpen = false"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-list w-4 h-4 ml-3 text-blue-500"></i>
                                جميع القضايا
                            </a>
                            <a href="#" @click="showNotification('نجح', 'تم إنشاء قضية جديدة', 'success'); casesMenuOpen = false"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-plus w-4 h-4 ml-3 text-green-500"></i>
                                قضية جديدة
                            </a>
                            <a href="#" @click="casesMenuOpen = false"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-folder-open w-4 h-4 ml-3 text-yellow-500"></i>
                                القضايا النشطة
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Clients -->
                <a href="#" @click="switchPage('clients')"
                   class="flex flex-col items-center py-2 px-3 rounded-lg transition-colors duration-200"
                   :class="currentPage === 'clients' ? 'text-primary-600 bg-primary-50' : 'text-gray-500 hover:text-gray-700'">
                    <div class="relative">
                        <i class="fas fa-users text-xl mb-1"></i>
                        <div x-show="currentPage === 'clients'" class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary-600 rounded-full"></div>
                    </div>
                    <span class="text-xs font-medium">العملاء</span>
                </a>

                <!-- Calendar -->
                <a href="#" @click="switchPage('calendar')"
                   class="flex flex-col items-center py-2 px-3 rounded-lg transition-colors duration-200"
                   :class="currentPage === 'calendar' ? 'text-primary-600 bg-primary-50' : 'text-gray-500 hover:text-gray-700'">
                    <div class="relative">
                        <i class="fas fa-calendar text-xl mb-1"></i>
                        <span class="absolute -top-2 -right-2 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center" x-text="stats.upcomingHearings"></span>
                        <div x-show="currentPage === 'calendar'" class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary-600 rounded-full"></div>
                    </div>
                    <span class="text-xs font-medium">التقويم</span>
                </a>

                <!-- More -->
                <div class="relative" x-data="{ moreMenuOpen: false }">
                    <button @click="moreMenuOpen = !moreMenuOpen"
                            class="flex flex-col items-center py-2 px-3 rounded-lg transition-colors duration-200 text-gray-500 hover:text-gray-700">
                        <div class="relative">
                            <i class="fas fa-ellipsis-h text-xl mb-1"></i>
                        </div>
                        <span class="text-xs font-medium">المزيد</span>
                    </button>

                    <!-- More Menu -->
                    <div x-show="moreMenuOpen"
                         @click.away="moreMenuOpen = false"
                         x-transition
                         class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                        <div class="py-2">
                            <a href="#" @click="switchPage('hearings'); moreMenuOpen = false"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-gavel w-4 h-4 ml-3 text-purple-500"></i>
                                الجلسات
                                <span class="mr-auto bg-red-500 text-white text-xs rounded-full px-2 py-1" x-text="stats.upcomingHearings"></span>
                            </a>
                            <a href="#" @click="switchPage('tasks'); moreMenuOpen = false"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-tasks w-4 h-4 ml-3 text-green-500"></i>
                                المهام
                                <span class="mr-auto bg-yellow-500 text-white text-xs rounded-full px-2 py-1" x-text="stats.pendingTasks"></span>
                            </a>
                            <a href="#" @click="switchPage('documents'); moreMenuOpen = false"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-file-alt w-4 h-4 ml-3 text-blue-500"></i>
                                المستندات
                            </a>
                            <a href="#" @click="switchPage('finance'); moreMenuOpen = false"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-file-invoice-dollar w-4 h-4 ml-3 text-yellow-600"></i>
                                المالية
                            </a>
                            <a href="#" @click="switchPage('reports'); moreMenuOpen = false"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-chart-bar w-4 h-4 ml-3 text-indigo-500"></i>
                                التقارير
                            </a>
                            <div class="border-t border-gray-100 my-1"></div>
                            <a href="#" @click="switchPage('settings'); moreMenuOpen = false"
                               class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                <i class="fas fa-cog w-4 h-4 ml-3 text-gray-500"></i>
                                الإعدادات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Mobile FAB (Floating Action Button) -->
        <div class="fixed bottom-20 left-4 z-30" x-data="{ fabOpen: false }">
            <button @click="fabOpen = !fabOpen"
                    class="w-14 h-14 bg-primary-600 hover:bg-primary-700 text-white rounded-full shadow-lg flex items-center justify-center transition-all duration-300"
                    :class="{ 'rotate-45': fabOpen }">
                <i class="fas fa-plus text-xl"></i>
            </button>

            <div x-show="fabOpen"
                 x-transition
                 class="absolute bottom-16 left-0 space-y-3">
                <a href="#" @click="showNotification('نجح', 'تم إنشاء قضية جديدة', 'success'); fabOpen = false"
                   class="flex items-center justify-center w-12 h-12 bg-blue-500 hover:bg-blue-600 text-white rounded-full shadow-lg transition-colors duration-200">
                    <i class="fas fa-folder-plus"></i>
                </a>
                <a href="#" @click="showNotification('نجح', 'تم إضافة عميل جديد', 'success'); fabOpen = false"
                   class="flex items-center justify-center w-12 h-12 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-lg transition-colors duration-200">
                    <i class="fas fa-user-plus"></i>
                </a>
                <a href="#" @click="showNotification('نجح', 'تم إنشاء مهمة جديدة', 'success'); fabOpen = false"
                   class="flex items-center justify-center w-12 h-12 bg-yellow-500 hover:bg-yellow-600 text-white rounded-full shadow-lg transition-colors duration-200">
                    <i class="fas fa-tasks"></i>
                </a>
                <a href="#" @click="showNotification('نجح', 'تم جدولة جلسة جديدة', 'success'); fabOpen = false"
                   class="flex items-center justify-center w-12 h-12 bg-purple-500 hover:bg-purple-600 text-white rounded-full shadow-lg transition-colors duration-200">
                    <i class="fas fa-gavel"></i>
                </a>
            </div>

            <div x-show="fabOpen"
                 @click="fabOpen = false"
                 x-transition
                 class="fixed inset-0 bg-black bg-opacity-25 -z-10"></div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div x-show="loading"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-4 space-x-reverse">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            <span class="text-gray-700">جاري التحميل...</span>
        </div>
    </div>

    <!-- Notifications -->
    <div id="notifications" class="fixed top-4 left-4 z-50 space-y-2"></div>

    <!-- Mobile Search Modal -->
    <div x-data="{ searchOpen: false, searchQuery: '' }"
         @open-search.window="searchOpen = true; $nextTick(() => $refs.searchInput.focus())"
         x-show="searchOpen"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-50 bg-black bg-opacity-50">

        <div class="fixed top-0 left-0 right-0 bg-white p-4 shadow-lg">
            <div class="flex items-center space-x-3 space-x-reverse">
                <button @click="searchOpen = false"
                        class="p-2 text-gray-400 hover:text-gray-600">
                    <i class="fas fa-arrow-right"></i>
                </button>

                <div class="flex-1 relative">
                    <input type="text"
                           x-model="searchQuery"
                           x-ref="searchInput"
                           placeholder="البحث في النظام..."
                           class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                    <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>

            <!-- Search Results -->
            <div x-show="searchQuery.length > 2" class="mt-4 max-h-64 overflow-y-auto">
                <div class="space-y-2">
                    <div class="p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100" @click="searchOpen = false; showNotification('تم العثور', 'قضية التجارة الإلكترونية', 'info')">
                        <div class="font-medium text-sm">قضية التجارة الإلكترونية</div>
                        <div class="text-xs text-gray-500">قضية #TC-2024-001</div>
                    </div>
                    <div class="p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100" @click="searchOpen = false; showNotification('تم العثور', 'شركة الخليج للتجارة', 'info')">
                        <div class="font-medium text-sm">شركة الخليج للتجارة</div>
                        <div class="text-xs text-gray-500">عميل</div>
                    </div>
                    <div class="p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100" @click="searchOpen = false; showNotification('تم العثور', 'مهمة مراجعة العقد', 'info')">
                        <div class="font-medium text-sm">مهمة مراجعة العقد</div>
                        <div class="text-xs text-gray-500">مهمة</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backdrop -->
        <div @click="searchOpen = false" class="absolute inset-0"></div>
    </div>

    <script>
        function dashboardApp() {
            return {
                currentPage: 'dashboard',
                sidebarOpen: false,
                loading: false,
                pageTitle: 'لوحة التحكم',
                pageSubtitle: 'نظرة عامة على أداء مكتبك',

                user: {},

                stats: {
                    totalCases: 127,
                    activeCases: 89,
                    totalClients: 245,
                    upcomingHearings: 12,
                    pendingTasks: 8,
                    storageUsed: 2.3,
                    storageLimit: 10,
                    monthlyRevenue: 15750
                },

                notifications: [
                    {
                        id: 1,
                        title: 'جلسة محكمة قادمة',
                        message: 'جلسة قضية التجارة الإلكترونية غداً الساعة 10:00 ص',
                        time: 'منذ 5 دقائق',
                        type: 'warning'
                    },
                    {
                        id: 2,
                        title: 'مهمة جديدة',
                        message: 'تم تكليفك بمراجعة عقد الشراكة',
                        time: 'منذ 15 دقيقة',
                        type: 'info'
                    },
                    {
                        id: 3,
                        title: 'دفعة جديدة',
                        message: 'تم استلام دفعة بقيمة 2,500 د.ك من شركة الخليج',
                        time: 'منذ ساعة',
                        type: 'success'
                    }
                ],

                init() {
                    // تحقق من تسجيل الدخول
                    const userData = localStorage.getItem('sanadan_user');
                    if (!userData) {
                        window.location.href = 'login.html';
                        return;
                    }

                    this.user = JSON.parse(userData);
                    this.updateTime();
                    setInterval(() => this.updateTime(), 1000);
                    this.loadPageContent();
                    this.animateStats();
                },

                updateTime() {
                    const now = new Date();
                    const timeString = now.toLocaleTimeString('ar-KW', {
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false
                    });
                    const timeElement = document.getElementById('current-time');
                    if (timeElement) {
                        timeElement.textContent = timeString;
                    }
                },

                animateStats() {
                    // تحريك الإحصائيات عند التحميل
                    setTimeout(() => {
                        this.stats.totalCases = this.animateNumber(0, 127, 2000);
                        this.stats.activeCases = this.animateNumber(0, 89, 2000);
                        this.stats.totalClients = this.animateNumber(0, 245, 2000);
                        this.stats.upcomingHearings = this.animateNumber(0, 12, 1500);
                        this.stats.pendingTasks = this.animateNumber(0, 8, 1500);
                    }, 500);
                },

                animateNumber(start, end, duration) {
                    const startTime = Date.now();
                    const animate = () => {
                        const elapsed = Date.now() - startTime;
                        const progress = Math.min(elapsed / duration, 1);
                        const current = Math.floor(start + (end - start) * progress);

                        if (progress < 1) {
                            requestAnimationFrame(animate);
                        }
                        return current;
                    };
                    animate();
                    return end;
                },

                switchPage(page) {
                    this.loading = true;
                    this.currentPage = page;
                    this.updatePageInfo(page);

                    setTimeout(() => {
                        this.loadPageContent();
                        this.loading = false;
                        this.showNotification('تم التحديث', `تم تحميل صفحة ${this.pageTitle}`, 'success');
                    }, 800);
                },

                updatePageInfo(page) {
                    const pageInfo = {
                        dashboard: { title: 'لوحة التحكم', subtitle: 'نظرة عامة على أداء مكتبك' },
                        cases: { title: 'القضايا', subtitle: 'إدارة جميع قضايا المكتب' },
                        clients: { title: 'العملاء', subtitle: 'إدارة عملاء المكتب' },
                        calendar: { title: 'التقويم', subtitle: 'جدولة المواعيد والجلسات' },
                        hearings: { title: 'الجلسات', subtitle: 'متابعة جلسات المحكمة' },
                        tasks: { title: 'المهام', subtitle: 'إدارة المهام والتكليفات' },
                        documents: { title: 'المستندات', subtitle: 'أرشيف الوثائق والملفات' },
                        finance: { title: 'المالية', subtitle: 'إدارة الفواتير والمدفوعات' },
                        reports: { title: 'التقارير', subtitle: 'تقارير الأداء والإحصائيات' },
                        settings: { title: 'الإعدادات', subtitle: 'إعدادات النظام والمكتب' }
                    };

                    const info = pageInfo[page] || { title: 'الصفحة', subtitle: '' };
                    this.pageTitle = info.title;
                    this.pageSubtitle = info.subtitle;
                },

                loadPageContent() {
                    const content = this.getPageContent(this.currentPage);
                    document.getElementById('page-content').innerHTML = content;
                    document.getElementById('mobile-page-content').innerHTML = content;
                },

                getPageContent(page) {
                    if (page === 'dashboard') {
                        return this.getDashboardContent();
                    } else if (page === 'cases') {
                        return this.getCasesContent();
                    } else {
                        return this.getDefaultContent(page);
                    }
                },

                getDashboardContent() {
                    return `
                        <div class="space-y-6">
                            <!-- Welcome Section -->
                            <div class="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl p-6 text-white">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h2 class="text-2xl font-bold mb-2">مرحباً بك، ${this.user.name.split(' ')[0]}!</h2>
                                        <p class="text-primary-100">${new Date().toLocaleDateString('ar-KW', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</p>
                                        <p class="text-primary-200 text-sm mt-1">${this.user.officeName || this.user.name}</p>
                                    </div>
                                    <div class="hidden md:block">
                                        <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                            <i class="fas fa-balance-scale text-3xl"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Stats Grid -->
                            <div class="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
                                <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200 counter-animation">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-sm font-medium text-gray-600">إجمالي القضايا</p>
                                            <p class="text-2xl font-bold text-gray-900">${this.stats.totalCases}</p>
                                            <p class="text-xs text-green-600 mt-1">
                                                <i class="fas fa-arrow-up"></i>
                                                +12% من الشهر الماضي
                                            </p>
                                        </div>
                                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-folder text-blue-600 text-xl"></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200 counter-animation">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-sm font-medium text-gray-600">القضايا النشطة</p>
                                            <p class="text-2xl font-bold text-gray-900">${this.stats.activeCases}</p>
                                            <p class="text-xs text-green-600 mt-1">
                                                <i class="fas fa-arrow-up"></i>
                                                +8% من الشهر الماضي
                                            </p>
                                        </div>
                                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-folder-open text-green-600 text-xl"></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200 counter-animation">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-sm font-medium text-gray-600">العملاء</p>
                                            <p class="text-2xl font-bold text-gray-900">${this.stats.totalClients}</p>
                                            <p class="text-xs text-blue-600 mt-1">
                                                <i class="fas fa-arrow-up"></i>
                                                +5% من الشهر الماضي
                                            </p>
                                        </div>
                                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-users text-yellow-600 text-xl"></i>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-200 counter-animation">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="text-sm font-medium text-gray-600">جلسات قادمة</p>
                                            <p class="text-2xl font-bold text-gray-900">${this.stats.upcomingHearings}</p>
                                            <p class="text-xs text-purple-600 mt-1">
                                                خلال 7 أيام
                                            </p>
                                        </div>
                                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                            <i class="fas fa-calendar text-purple-600 text-xl"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Recent Activity -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                                    <div class="p-6 border-b border-gray-200">
                                        <h3 class="text-lg font-semibold text-gray-900">القضايا الحديثة</h3>
                                    </div>
                                    <div class="p-6">
                                        <div class="space-y-4">
                                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer">
                                                <div class="flex items-center space-x-4 space-x-reverse">
                                                    <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                                                        <i class="fas fa-folder text-primary-600"></i>
                                                    </div>
                                                    <div>
                                                        <h4 class="font-medium text-gray-900">قضية التجارة الإلكترونية</h4>
                                                        <p class="text-sm text-gray-500">شركة الخليج للتجارة • #TC-2024-001</p>
                                                    </div>
                                                </div>
                                                <div class="text-left">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                                        نشطة
                                                    </span>
                                                    <p class="text-xs text-gray-500 mt-1">منذ ساعتين</p>
                                                </div>
                                            </div>

                                            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer">
                                                <div class="flex items-center space-x-4 space-x-reverse">
                                                    <div class="w-10 h-10 bg-primary-100 rounded-lg flex items-center justify-center">
                                                        <i class="fas fa-folder text-primary-600"></i>
                                                    </div>
                                                    <div>
                                                        <h4 class="font-medium text-gray-900">قضية العقارات</h4>
                                                        <p class="text-sm text-gray-500">أحمد محمد • #RE-2024-015</p>
                                                    </div>
                                                </div>
                                                <div class="text-left">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                                        معلقة
                                                    </span>
                                                    <p class="text-xs text-gray-500 mt-1">أمس</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="bg-white rounded-xl shadow-sm border border-gray-200">
                                    <div class="p-6 border-b border-gray-200">
                                        <h3 class="text-lg font-semibold text-gray-900">الجلسات القادمة</h3>
                                    </div>
                                    <div class="p-6">
                                        <div class="space-y-4">
                                            <div class="flex items-start space-x-3 space-x-reverse">
                                                <div class="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                                    <i class="fas fa-gavel text-blue-600"></i>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900">قضية التجارة الإلكترونية</p>
                                                    <p class="text-xs text-gray-500">محكمة الكويت التجارية</p>
                                                    <p class="text-xs text-blue-600 font-medium mt-1">
                                                        غداً - 10:00 صباحاً
                                                    </p>
                                                </div>
                                            </div>

                                            <div class="flex items-start space-x-3 space-x-reverse">
                                                <div class="flex-shrink-0 w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                                    <i class="fas fa-gavel text-blue-600"></i>
                                                </div>
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-900">قضية العقارات</p>
                                                    <p class="text-xs text-gray-500">محكمة الكويت المدنية</p>
                                                    <p class="text-xs text-blue-600 font-medium mt-1">
                                                        الأحد - 11:30 صباحاً
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                },

                getCasesContent() {
                    return `
                        <div class="space-y-6">
                            <!-- Header -->
                            <div class="flex items-center justify-between">
                                <div>
                                    <h2 class="text-2xl font-bold text-gray-900">إدارة القضايا</h2>
                                    <p class="text-gray-600">إجمالي ${this.stats.totalCases} قضية</p>
                                </div>
                                <button onclick="dashboardApp().showNotification('نجح', 'تم إنشاء قضية جديدة', 'success')"
                                        class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 space-x-reverse">
                                    <i class="fas fa-plus"></i>
                                    <span>قضية جديدة</span>
                                </button>
                            </div>

                            <!-- Filters -->
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                                <div class="flex flex-wrap gap-2">
                                    <button class="px-4 py-2 bg-primary-100 text-primary-700 rounded-lg text-sm font-medium">الكل</button>
                                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200">نشطة</button>
                                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200">معلقة</button>
                                    <button class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg text-sm font-medium hover:bg-gray-200">مكتملة</button>
                                </div>
                            </div>

                            <!-- Cases List -->
                            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                                <div class="p-6">
                                    <div class="space-y-4">
                                        ${Array.from({length: 8}, (_, i) => `
                                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                                <div class="flex items-center justify-between">
                                                    <div class="flex items-center space-x-4 space-x-reverse">
                                                        <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                                                            <i class="fas fa-folder text-primary-600"></i>
                                                        </div>
                                                        <div>
                                                            <h3 class="font-semibold text-gray-900">قضية ${['التجارة الإلكترونية', 'العقارات', 'الشركات', 'العمالة', 'الأسرة', 'الجنائية', 'المدنية', 'التجارية'][i]}</h3>
                                                            <p class="text-sm text-gray-500">${['شركة الخليج للتجارة', 'أحمد محمد', 'شركة الكويت', 'سارة أحمد', 'محمد علي', 'فاطمة خالد', 'عبدالله سالم', 'نورا حسن'][i]} • #${['TC', 'RE', 'CO', 'LA', 'FA', 'CR', 'CI', 'CM'][i]}-2024-${String(i + 1).padStart(3, '0')}</p>
                                                            <p class="text-xs text-gray-400 mt-1">آخر تحديث: ${['منذ ساعتين', 'أمس', 'منذ 3 أيام', 'منذ أسبوع', 'منذ أسبوعين', 'منذ شهر', 'منذ شهرين', 'منذ 3 أشهر'][i]}</p>
                                                        </div>
                                                    </div>
                                                    <div class="text-left">
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${['bg-green-100 text-green-800', 'bg-yellow-100 text-yellow-800', 'bg-blue-100 text-blue-800'][i % 3]}">
                                                            ${['نشطة', 'معلقة', 'مراجعة'][i % 3]}
                                                        </span>
                                                        <div class="mt-2 flex space-x-2 space-x-reverse">
                                                            <button class="text-gray-400 hover:text-gray-600">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            <button class="text-gray-400 hover:text-gray-600">
                                                                <i class="fas fa-edit"></i>
                                                            </button>
                                                            <button class="text-gray-400 hover:text-red-600">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        `).join('')}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                },

                getDefaultContent(page) {
                    return `
                        <div class="text-center py-20">
                            <div class="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-${this.getPageIcon(page)} text-gray-400 text-3xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-700 mb-2">صفحة ${this.pageTitle}</h3>
                            <p class="text-gray-500 mb-6">${this.pageSubtitle}</p>
                            <div class="space-y-3">
                                <div class="bg-white rounded-lg p-4 shadow-sm border border-gray-200 max-w-md mx-auto">
                                    <p class="text-sm text-gray-600">هذه الصفحة قيد التطوير وستكون متاحة قريباً</p>
                                </div>
                                <button onclick="dashboardApp().showNotification('قريباً', 'هذه الميزة ستكون متاحة في التحديث القادم', 'info')"
                                        class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg">
                                    تجربة الميزة
                                </button>
                            </div>
                        </div>
                    `;
                },

                getPageIcon(page) {
                    const icons = {
                        dashboard: 'tachometer-alt',
                        cases: 'folder',
                        clients: 'users',
                        calendar: 'calendar',
                        hearings: 'gavel',
                        tasks: 'tasks',
                        documents: 'file-alt',
                        finance: 'file-invoice-dollar',
                        reports: 'chart-bar',
                        settings: 'cog'
                    };
                    return icons[page] || 'question-circle';
                },

                showNotification(title, message, type = 'info') {
                    const notification = document.createElement('div');
                    notification.className = `bg-white border-r-4 border-${type === 'success' ? 'green' : type === 'error' ? 'red' : type === 'warning' ? 'yellow' : 'blue'}-500 rounded-lg shadow-lg p-4 mb-2 slide-in`;
                    notification.innerHTML = `
                        <div class="flex items-center">
                            <i class="fas fa-${type === 'success' ? 'check-circle text-green-500' : type === 'error' ? 'exclamation-circle text-red-500' : type === 'warning' ? 'exclamation-triangle text-yellow-500' : 'info-circle text-blue-500'} ml-3"></i>
                            <div>
                                <p class="font-semibold text-gray-800">${title}</p>
                                <p class="text-sm text-gray-600">${message}</p>
                            </div>
                            <button onclick="this.parentElement.parentElement.remove()" class="mr-auto text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    `;

                    document.getElementById('notifications').appendChild(notification);

                    setTimeout(() => {
                        if (notification.parentElement) {
                            notification.remove();
                        }
                    }, 5000);
                },

                clearNotifications() {
                    this.notifications = [];
                    this.showNotification('تم', 'تم تحديد جميع الإشعارات كمقروءة', 'success');
                },

                logout() {
                    this.loading = true;
                    this.showNotification('تسجيل الخروج', 'جاري تسجيل الخروج...', 'info');

                    setTimeout(() => {
                        localStorage.removeItem('sanadan_user');
                        window.location.href = 'login.html';
                    }, 2000);
                }
            }
        }
    </script>
</body>
</html>

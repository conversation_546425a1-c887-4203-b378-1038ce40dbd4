import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { Text, Card, useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { colors } from '../theme/theme';

interface StatsCardProps {
  title: string;
  value: number | string;
  icon: string;
  color?: string;
  subtitle?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  onPress?: () => void;
  loading?: boolean;
}

export default function StatsCard({
  title,
  value,
  icon,
  color = colors.primary[600],
  subtitle,
  trend,
  onPress,
  loading = false,
}: StatsCardProps) {
  const theme = useTheme();

  const formatValue = (val: number | string): string => {
    if (typeof val === 'number') {
      if (val >= 1000000) {
        return `${(val / 1000000).toFixed(1)}م`;
      } else if (val >= 1000) {
        return `${(val / 1000).toFixed(1)}ك`;
      }
      return val.toLocaleString('ar-KW');
    }
    return val;
  };

  const CardContent = () => (
    <Card.Content style={styles.content}>
      <View style={styles.header}>
        <View style={[styles.iconContainer, { backgroundColor: `${color}20` }]}>
          <Icon name={icon} size={24} color={color} />
        </View>
        {trend && (
          <View style={styles.trendContainer}>
            <Icon
              name={trend.isPositive ? 'trending-up' : 'trending-down'}
              size={16}
              color={trend.isPositive ? colors.success[600] : colors.secondary[600]}
            />
            <Text
              style={[
                styles.trendText,
                {
                  color: trend.isPositive ? colors.success[600] : colors.secondary[600],
                },
              ]}
            >
              {Math.abs(trend.value)}%
            </Text>
          </View>
        )}
      </View>

      <View style={styles.body}>
        {loading ? (
          <View style={styles.loadingContainer}>
            <View style={[styles.loadingBar, { backgroundColor: theme.colors.outline }]} />
            <View style={[styles.loadingBar, styles.loadingBarShort, { backgroundColor: theme.colors.outline }]} />
          </View>
        ) : (
          <>
            <Text style={[styles.value, { color: theme.colors.onSurface }]}>
              {formatValue(value)}
            </Text>
            <Text style={[styles.title, { color: theme.colors.onSurfaceVariant }]}>
              {title}
            </Text>
            {subtitle && (
              <Text style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
                {subtitle}
              </Text>
            )}
          </>
        )}
      </View>
    </Card.Content>
  );

  if (onPress) {
    return (
      <TouchableOpacity
        style={styles.container}
        onPress={onPress}
        activeOpacity={0.7}
      >
        <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
          <CardContent />
        </Card>
      </TouchableOpacity>
    );
  }

  return (
    <View style={styles.container}>
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        <CardContent />
      </Card>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    marginHorizontal: 4,
  },
  card: {
    elevation: 2,
    borderRadius: 12,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
  },
  trendContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  trendText: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  body: {
    alignItems: 'flex-start',
  },
  value: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  title: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 12,
    opacity: 0.7,
  },
  loadingContainer: {
    width: '100%',
  },
  loadingBar: {
    height: 12,
    borderRadius: 6,
    marginBottom: 8,
  },
  loadingBarShort: {
    width: '60%',
    height: 8,
  },
});

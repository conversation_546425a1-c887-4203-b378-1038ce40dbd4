import { Platform } from 'react-native';

// إعدادات البيئة
export const ENV = __DEV__ ? 'development' : 'production';

// إعدادات API
export const API_CONFIG = {
  BASE_URL: __DEV__ 
    ? 'http://localhost:8000/api/v1' 
    : 'https://api.sanadan.com/v1',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};

// إعدادات التطبيق
export const APP_CONFIG = {
  NAME: 'منصة سندان',
  VERSION: '1.0.0',
  BUILD_NUMBER: '1',
  BUNDLE_ID: 'com.sanadan.legalplatform',
  DEEP_LINK_SCHEME: 'sanadan',
  WEBSITE_URL: 'https://sanadan.com',
  SUPPORT_EMAIL: '<EMAIL>',
  SUPPORT_PHONE: '+965-2222-3333',
};

// إعدادات المصادقة
export const AUTH_CONFIG = {
  TOKEN_STORAGE_KEY: 'authToken',
  REFRESH_TOKEN_KEY: 'refreshToken',
  BIOMETRIC_KEY: 'biometricEnabled',
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 ساعة
  REFRESH_THRESHOLD: 5 * 60 * 1000, // 5 دقائق
  MAX_LOGIN_ATTEMPTS: 5,
  LOCKOUT_DURATION: 15 * 60 * 1000, // 15 دقيقة
};

// إعدادات التخزين
export const STORAGE_CONFIG = {
  CACHE_SIZE: 50 * 1024 * 1024, // 50 MB
  IMAGE_CACHE_SIZE: 100 * 1024 * 1024, // 100 MB
  OFFLINE_STORAGE_SIZE: 10 * 1024 * 1024, // 10 MB
  AUTO_CLEANUP_INTERVAL: 7 * 24 * 60 * 60 * 1000, // أسبوع
};

// إعدادات الإشعارات
export const NOTIFICATION_CONFIG = {
  ENABLED: true,
  SOUND_ENABLED: true,
  VIBRATION_ENABLED: true,
  BADGE_ENABLED: true,
  CATEGORIES: {
    HEARING_REMINDER: 'hearing_reminder',
    TASK_DEADLINE: 'task_deadline',
    PAYMENT_DUE: 'payment_due',
    DOCUMENT_SHARED: 'document_shared',
    CASE_UPDATE: 'case_update',
    SYSTEM_UPDATE: 'system_update',
  },
  REMINDER_INTERVALS: [
    { label: '15 دقيقة', value: 15 },
    { label: '30 دقيقة', value: 30 },
    { label: 'ساعة واحدة', value: 60 },
    { label: 'ساعتان', value: 120 },
    { label: 'يوم واحد', value: 1440 },
  ],
};

// إعدادات الملفات
export const FILE_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10 MB
  MAX_IMAGE_SIZE: 5 * 1024 * 1024, // 5 MB
  ALLOWED_DOCUMENT_TYPES: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'image/jpeg',
    'image/png',
    'image/gif',
  ],
  ALLOWED_IMAGE_TYPES: [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
  ],
  COMPRESSION_QUALITY: 0.8,
  THUMBNAIL_SIZE: { width: 200, height: 200 },
};

// إعدادات الخرائط والموقع
export const LOCATION_CONFIG = {
  DEFAULT_REGION: {
    latitude: 29.3759,
    longitude: 47.9774,
    latitudeDelta: 0.5,
    longitudeDelta: 0.5,
  },
  ACCURACY: 'high' as const,
  TIMEOUT: 15000,
  MAXIMUM_AGE: 10000,
  DISTANCE_FILTER: 10,
};

// إعدادات الدفع
export const PAYMENT_CONFIG = {
  SUPPORTED_GATEWAYS: ['knet', 'stripe', 'paypal'],
  CURRENCIES: {
    DEFAULT: 'KWD',
    SUPPORTED: ['KWD', 'USD', 'EUR', 'SAR', 'AED'],
  },
  KNET: {
    MERCHANT_ID: __DEV__ ? 'test_merchant' : 'prod_merchant',
    TERMINAL_ID: __DEV__ ? 'test_terminal' : 'prod_terminal',
    TEST_MODE: __DEV__,
  },
  STRIPE: {
    PUBLISHABLE_KEY: __DEV__ 
      ? 'pk_test_...' 
      : 'pk_live_...',
  },
};

// إعدادات التقارير
export const REPORTS_CONFIG = {
  DEFAULT_DATE_RANGE: 30, // أيام
  MAX_EXPORT_RECORDS: 10000,
  SUPPORTED_FORMATS: ['pdf', 'excel', 'csv'],
  CHART_COLORS: [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B',
    '#8B5CF6', '#06B6D4', '#F97316', '#84CC16',
  ],
};

// إعدادات البحث
export const SEARCH_CONFIG = {
  MIN_QUERY_LENGTH: 2,
  MAX_RESULTS: 50,
  DEBOUNCE_DELAY: 300,
  HIGHLIGHT_COLOR: '#FFEB3B',
  RECENT_SEARCHES_LIMIT: 10,
  SEARCH_HISTORY_EXPIRY: 30 * 24 * 60 * 60 * 1000, // 30 يوم
};

// إعدادات التزامن
export const SYNC_CONFIG = {
  AUTO_SYNC_INTERVAL: 5 * 60 * 1000, // 5 دقائق
  BACKGROUND_SYNC_INTERVAL: 15 * 60 * 1000, // 15 دقيقة
  RETRY_ATTEMPTS: 3,
  BATCH_SIZE: 50,
  CONFLICT_RESOLUTION: 'server_wins' as const,
};

// إعدادات الأمان
export const SECURITY_CONFIG = {
  ENCRYPTION_ENABLED: true,
  BIOMETRIC_TIMEOUT: 5 * 60 * 1000, // 5 دقائق
  AUTO_LOCK_TIMEOUT: 10 * 60 * 1000, // 10 دقائق
  SCREENSHOT_PROTECTION: !__DEV__,
  ROOT_DETECTION: !__DEV__,
  SSL_PINNING: !__DEV__,
  OBFUSCATION: !__DEV__,
};

// إعدادات التحليلات
export const ANALYTICS_CONFIG = {
  ENABLED: !__DEV__,
  CRASH_REPORTING: !__DEV__,
  PERFORMANCE_MONITORING: !__DEV__,
  USER_TRACKING: false, // احترام الخصوصية
  SESSION_TIMEOUT: 30 * 60 * 1000, // 30 دقيقة
};

// إعدادات التخصيص
export const CUSTOMIZATION_CONFIG = {
  THEMES: ['light', 'dark', 'auto'],
  LANGUAGES: ['ar', 'en'],
  FONTS: {
    ARABIC: 'Cairo',
    ENGLISH: 'Inter',
    LEGAL: 'Amiri',
  },
  RTL_LANGUAGES: ['ar'],
};

// إعدادات الشبكة
export const NETWORK_CONFIG = {
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
  OFFLINE_QUEUE_SIZE: 100,
  CACHE_DURATION: 5 * 60 * 1000, // 5 دقائق
  BACKGROUND_FETCH_INTERVAL: 15 * 60 * 1000, // 15 دقيقة
};

// إعدادات التطوير
export const DEV_CONFIG = {
  ENABLE_FLIPPER: __DEV__,
  ENABLE_REACTOTRON: __DEV__,
  SHOW_PERFORMANCE_MONITOR: __DEV__,
  ENABLE_YELLOW_BOX: __DEV__,
  LOG_LEVEL: __DEV__ ? 'debug' : 'error',
};

// إعدادات خاصة بالمنصة
export const PLATFORM_CONFIG = {
  IS_IOS: Platform.OS === 'ios',
  IS_ANDROID: Platform.OS === 'android',
  IS_WEB: Platform.OS === 'web',
  MIN_IOS_VERSION: '12.0',
  MIN_ANDROID_VERSION: 21,
  TARGET_SDK_VERSION: 33,
};

// إعدادات الميزات
export const FEATURES_CONFIG = {
  AI_ASSISTANT: true,
  DOCUMENT_GENERATION: true,
  ADVANCED_REPORTS: true,
  BIOMETRIC_AUTH: true,
  OFFLINE_MODE: true,
  DARK_MODE: true,
  MULTI_LANGUAGE: true,
  PUSH_NOTIFICATIONS: true,
  BACKGROUND_SYNC: true,
  FILE_ENCRYPTION: true,
  AUDIT_TRAIL: true,
  BACKUP_RESTORE: true,
};

// إعدادات الاشتراكات
export const SUBSCRIPTION_CONFIG = {
  PLANS: {
    BASIC: {
      id: 'basic',
      name: 'الباقة الأساسية',
      price: 50,
      currency: 'KWD',
      interval: 'month',
      features: [
        'حتى 100 قضية',
        '2 مستخدم',
        '5 جيجا تخزين',
        'دعم بريد إلكتروني',
      ],
    },
    PROFESSIONAL: {
      id: 'professional',
      name: 'الباقة المتقدمة',
      price: 100,
      currency: 'KWD',
      interval: 'month',
      features: [
        'قضايا غير محدودة',
        '10 مستخدم',
        '50 جيجا تخزين',
        'دعم هاتفي ومرئي',
        'تقارير متقدمة',
        'مساعد ذكي',
      ],
    },
    ENTERPRISE: {
      id: 'enterprise',
      name: 'الباقة المؤسسية',
      price: 200,
      currency: 'KWD',
      interval: 'month',
      features: [
        'مميزات غير محدودة',
        'مستخدمين غير محدود',
        '500 جيجا تخزين',
        'دعم 24/7',
        'تخصيص كامل',
        'API متقدم',
        'تدريب مخصص',
      ],
    },
  },
  TRIAL_PERIOD: 30, // أيام
  GRACE_PERIOD: 7, // أيام
};

// تصدير جميع الإعدادات
export default {
  ENV,
  API_CONFIG,
  APP_CONFIG,
  AUTH_CONFIG,
  STORAGE_CONFIG,
  NOTIFICATION_CONFIG,
  FILE_CONFIG,
  LOCATION_CONFIG,
  PAYMENT_CONFIG,
  REPORTS_CONFIG,
  SEARCH_CONFIG,
  SYNC_CONFIG,
  SECURITY_CONFIG,
  ANALYTICS_CONFIG,
  CUSTOMIZATION_CONFIG,
  NETWORK_CONFIG,
  DEV_CONFIG,
  PLATFORM_CONFIG,
  FEATURES_CONFIG,
  SUBSCRIPTION_CONFIG,
};

import './bootstrap';
import '../css/app.css';

import { createApp, h } from 'vue';
import { createInertiaApp } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { ZiggyVue } from '../../vendor/tightenco/ziggy/dist/vue.m';
import { createPinia } from 'pinia';
import Toast from 'vue-toastification';
import 'vue-toastification/dist/index.css';

// استيراد المكونات العامة
import AppLayout from '@/Layouts/AppLayout.vue';
import GuestLayout from '@/Layouts/GuestLayout.vue';
import AuthenticatedLayout from '@/Layouts/AuthenticatedLayout.vue';

// استيراد المكونات المشتركة
import Modal from '@/Components/Modal.vue';
import Button from '@/Components/Button.vue';
import Input from '@/Components/Input.vue';
import Select from '@/Components/Select.vue';
import Textarea from '@/Components/Textarea.vue';
import Checkbox from '@/Components/Checkbox.vue';
import Radio from '@/Components/Radio.vue';
import Badge from '@/Components/Badge.vue';
import Card from '@/Components/Card.vue';
import Table from '@/Components/Table.vue';
import Pagination from '@/Components/Pagination.vue';
import LoadingSpinner from '@/Components/LoadingSpinner.vue';
import Alert from '@/Components/Alert.vue';
import Dropdown from '@/Components/Dropdown.vue';
import Tabs from '@/Components/Tabs.vue';
import Calendar from '@/Components/Calendar.vue';
import Chart from '@/Components/Chart.vue';
import FileUpload from '@/Components/FileUpload.vue';
import DatePicker from '@/Components/DatePicker.vue';
import TimePicker from '@/Components/TimePicker.vue';
import RichTextEditor from '@/Components/RichTextEditor.vue';
import DataTable from '@/Components/DataTable.vue';
import SearchBox from '@/Components/SearchBox.vue';
import FilterPanel from '@/Components/FilterPanel.vue';
import StatsCard from '@/Components/StatsCard.vue';
import ProgressBar from '@/Components/ProgressBar.vue';
import Avatar from '@/Components/Avatar.vue';
import Breadcrumb from '@/Components/Breadcrumb.vue';
import Sidebar from '@/Components/Sidebar.vue';
import Navbar from '@/Components/Navbar.vue';

// استيراد المساعدات
import { formatDate, formatCurrency, formatNumber } from '@/Utils/formatters';
import { validateEmail, validatePhone, validateRequired } from '@/Utils/validators';
import { debounce, throttle } from '@/Utils/helpers';
import { useAuth } from '@/Composables/useAuth';
import { useNotifications } from '@/Composables/useNotifications';
import { usePermissions } from '@/Composables/usePermissions';
import { useLocale } from '@/Composables/useLocale';

const appName = import.meta.env.VITE_APP_NAME || 'منصة سندان';

// إعدادات Toast
const toastOptions = {
    position: 'top-right',
    timeout: 5000,
    closeOnClick: true,
    pauseOnFocusLoss: true,
    pauseOnHover: true,
    draggable: true,
    draggablePercent: 0.6,
    showCloseButtonOnHover: false,
    hideProgressBar: false,
    closeButton: 'button',
    icon: true,
    rtl: document.dir === 'rtl',
    toastClassName: 'custom-toast',
    bodyClassName: 'custom-toast-body',
    containerClassName: 'custom-toast-container',
};

createInertiaApp({
    title: (title) => title ? `${title} - ${appName}` : appName,
    resolve: (name) => resolvePageComponent(`./Pages/${name}.vue`, import.meta.glob('./Pages/**/*.vue')),
    setup({ el, App, props, plugin }) {
        const app = createApp({ render: () => h(App, props) });
        
        // إعداد Pinia للحالة العامة
        const pinia = createPinia();
        app.use(pinia);
        
        // إعداد Inertia
        app.use(plugin);
        
        // إعداد Ziggy للمسارات
        app.use(ZiggyVue);
        
        // إعداد Toast للإشعارات
        app.use(Toast, toastOptions);
        
        // تسجيل المكونات العامة
        app.component('AppLayout', AppLayout);
        app.component('GuestLayout', GuestLayout);
        app.component('AuthenticatedLayout', AuthenticatedLayout);
        app.component('Modal', Modal);
        app.component('Button', Button);
        app.component('Input', Input);
        app.component('Select', Select);
        app.component('Textarea', Textarea);
        app.component('Checkbox', Checkbox);
        app.component('Radio', Radio);
        app.component('Badge', Badge);
        app.component('Card', Card);
        app.component('Table', Table);
        app.component('Pagination', Pagination);
        app.component('LoadingSpinner', LoadingSpinner);
        app.component('Alert', Alert);
        app.component('Dropdown', Dropdown);
        app.component('Tabs', Tabs);
        app.component('Calendar', Calendar);
        app.component('Chart', Chart);
        app.component('FileUpload', FileUpload);
        app.component('DatePicker', DatePicker);
        app.component('TimePicker', TimePicker);
        app.component('RichTextEditor', RichTextEditor);
        app.component('DataTable', DataTable);
        app.component('SearchBox', SearchBox);
        app.component('FilterPanel', FilterPanel);
        app.component('StatsCard', StatsCard);
        app.component('ProgressBar', ProgressBar);
        app.component('Avatar', Avatar);
        app.component('Breadcrumb', Breadcrumb);
        app.component('Sidebar', Sidebar);
        app.component('Navbar', Navbar);
        
        // إضافة المساعدات العامة
        app.config.globalProperties.$formatDate = formatDate;
        app.config.globalProperties.$formatCurrency = formatCurrency;
        app.config.globalProperties.$formatNumber = formatNumber;
        app.config.globalProperties.$validateEmail = validateEmail;
        app.config.globalProperties.$validatePhone = validatePhone;
        app.config.globalProperties.$validateRequired = validateRequired;
        app.config.globalProperties.$debounce = debounce;
        app.config.globalProperties.$throttle = throttle;
        
        // إضافة المتغيرات العامة
        app.provide('appName', appName);
        app.provide('locale', document.documentElement.lang || 'ar');
        app.provide('direction', document.dir || 'rtl');
        
        // إعداد معالج الأخطاء العام
        app.config.errorHandler = (error, instance, info) => {
            console.error('Vue Error:', error);
            console.error('Component:', instance);
            console.error('Info:', info);
            
            // إرسال الخطأ إلى خدمة التتبع (مثل Sentry)
            if (import.meta.env.PROD) {
                // Sentry.captureException(error);
            }
        };
        
        // إعداد معالج التحذيرات
        app.config.warnHandler = (msg, instance, trace) => {
            if (import.meta.env.DEV) {
                console.warn('Vue Warning:', msg);
                console.warn('Component:', instance);
                console.warn('Trace:', trace);
            }
        };
        
        // إعداد الخصائص العامة للتطبيق
        app.config.globalProperties.$user = props.initialPage.props.auth?.user || null;
        app.config.globalProperties.$permissions = props.initialPage.props.auth?.permissions || [];
        app.config.globalProperties.$tenant = props.initialPage.props.tenant || null;
        
        // تحديد اتجاه النص حسب اللغة
        const setDirection = () => {
            const locale = document.documentElement.lang || 'ar';
            const direction = locale === 'ar' ? 'rtl' : 'ltr';
            document.dir = direction;
            document.documentElement.setAttribute('dir', direction);
        };
        
        setDirection();
        
        // مراقبة تغيير اللغة
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'lang') {
                    setDirection();
                }
            });
        });
        
        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['lang']
        });
        
        // إعداد اختصارات لوحة المفاتيح
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K للبحث السريع
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                // فتح نافذة البحث السريع
                const searchEvent = new CustomEvent('open-quick-search');
                document.dispatchEvent(searchEvent);
            }
            
            // Ctrl/Cmd + N لإنشاء جديد
            if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
                e.preventDefault();
                // فتح نافذة الإنشاء السريع
                const createEvent = new CustomEvent('open-quick-create');
                document.dispatchEvent(createEvent);
            }
            
            // ESC لإغلاق النوافذ المنبثقة
            if (e.key === 'Escape') {
                const closeEvent = new CustomEvent('close-modals');
                document.dispatchEvent(closeEvent);
            }
        });
        
        // إعداد Service Worker للعمل دون اتصال
        if ('serviceWorker' in navigator && import.meta.env.PROD) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then((registration) => {
                        console.log('SW registered: ', registration);
                    })
                    .catch((registrationError) => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
        
        // إعداد تتبع الأداء
        if (import.meta.env.PROD) {
            // تتبع وقت تحميل الصفحة
            window.addEventListener('load', () => {
                const loadTime = performance.now();
                console.log(`Page loaded in ${loadTime.toFixed(2)}ms`);
                
                // إرسال البيانات إلى خدمة التحليلات
                // analytics.track('page_load_time', { duration: loadTime });
            });
        }
        
        return app.mount(el);
    },
    progress: {
        color: '#3B82F6',
        showSpinner: true,
    },
});

// إعداد Axios للطلبات
window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
window.axios.defaults.headers.common['Accept'] = 'application/json';
window.axios.defaults.headers.common['Content-Type'] = 'application/json';

// إضافة interceptor للتعامل مع الأخطاء
window.axios.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response?.status === 401) {
            // إعادة توجيه لصفحة تسجيل الدخول
            window.location.href = '/login';
        } else if (error.response?.status === 403) {
            // عرض رسالة عدم وجود صلاحية
            console.error('Access denied');
        } else if (error.response?.status >= 500) {
            // عرض رسالة خطأ في الخادم
            console.error('Server error');
        }
        
        return Promise.reject(error);
    }
);

// إعداد Echo للإشعارات المباشرة (إذا كان متاحاً)
if (window.Echo) {
    // الاستماع للإشعارات الخاصة بالمستخدم
    if (window.Laravel?.user?.id) {
        window.Echo.private(`App.Models.User.${window.Laravel.user.id}`)
            .notification((notification) => {
                // عرض الإشعار
                console.log('New notification:', notification);
            });
    }
    
    // الاستماع للتحديثات العامة
    window.Echo.channel('general-updates')
        .listen('SystemUpdate', (e) => {
            console.log('System update:', e);
        });
}

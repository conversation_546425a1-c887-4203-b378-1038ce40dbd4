import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { User } from '../types';
import { authService } from '../services/authService';
import { notificationService } from '../services/notificationService';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isFirstTime: boolean;
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, password: string) => Promise<void>;
  verifyOTP: (email: string, otp: string, type: 'register' | 'reset') => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<void>;
  refreshToken: () => Promise<void>;
  checkBiometricAvailability: () => Promise<boolean>;
  enableBiometric: () => Promise<void>;
  disableBiometric: () => Promise<void>;
  authenticateWithBiometric: () => Promise<void>;
}

interface RegisterData {
  name: string;
  email: string;
  password: string;
  phone?: string;
  lawFirmName: string;
  licenseNumber: string;
  subscriptionPlan: 'basic' | 'professional' | 'enterprise';
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isFirstTime, setIsFirstTime] = useState(false);

  // فحص حالة المصادقة عند بدء التطبيق
  useEffect(() => {
    checkAuthState();
  }, []);

  const checkAuthState = async () => {
    try {
      setIsLoading(true);

      // فحص إذا كانت المرة الأولى لفتح التطبيق
      const hasSeenOnboarding = await AsyncStorage.getItem('hasSeenOnboarding');
      if (!hasSeenOnboarding) {
        setIsFirstTime(true);
        setIsLoading(false);
        return;
      }

      // محاولة الحصول على التوكن المحفوظ
      const token = await SecureStore.getItemAsync('authToken');
      if (!token) {
        setIsLoading(false);
        return;
      }

      // التحقق من صحة التوكن والحصول على بيانات المستخدم
      const userData = await authService.getCurrentUser(token);
      if (userData) {
        setUser(userData);
        // إعداد الإشعارات للمستخدم المسجل
        await notificationService.registerForPushNotifications(userData.id);
      } else {
        // التوكن غير صالح، حذفه
        await SecureStore.deleteItemAsync('authToken');
      }
    } catch (error) {
      console.error('خطأ في فحص حالة المصادقة:', error);
      // في حالة الخطأ، حذف التوكن المحفوظ
      await SecureStore.deleteItemAsync('authToken');
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string, rememberMe = false) => {
    try {
      const response = await authService.login(email, password);
      
      // حفظ التوكن
      await SecureStore.setItemAsync('authToken', response.token);
      
      // حفظ بيانات المستخدم
      setUser(response.user);

      // حفظ تفضيل "تذكرني"
      if (rememberMe) {
        await AsyncStorage.setItem('rememberMe', 'true');
        await AsyncStorage.setItem('lastEmail', email);
      }

      // إعداد الإشعارات
      await notificationService.registerForPushNotifications(response.user.id);

      // تسجيل آخر تسجيل دخول
      await AsyncStorage.setItem('lastLoginAt', new Date().toISOString());

    } catch (error) {
      throw error;
    }
  };

  const register = async (userData: RegisterData) => {
    try {
      const response = await authService.register(userData);
      
      // في حالة التسجيل الناجح، قد نحتاج للتحقق من البريد الإلكتروني أولاً
      if (response.requiresVerification) {
        // إرسال رمز التحقق
        await authService.sendVerificationCode(userData.email, 'register');
        return;
      }

      // إذا لم يكن التحقق مطلوباً، تسجيل الدخول مباشرة
      await SecureStore.setItemAsync('authToken', response.token);
      setUser(response.user);
      
      // إعداد الإشعارات
      await notificationService.registerForPushNotifications(response.user.id);

    } catch (error) {
      throw error;
    }
  };

  const logout = async () => {
    try {
      // إلغاء تسجيل الإشعارات
      if (user) {
        await notificationService.unregisterFromPushNotifications(user.id);
      }

      // استدعاء خدمة تسجيل الخروج
      await authService.logout();

      // حذف التوكن المحفوظ
      await SecureStore.deleteItemAsync('authToken');
      
      // حذف البيانات المحلية
      await AsyncStorage.multiRemove([
        'rememberMe',
        'lastEmail',
        'biometricEnabled',
        'userPreferences'
      ]);

      // إعادة تعيين حالة المستخدم
      setUser(null);

    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
      // حتى لو فشل الطلب، نقوم بتنظيف البيانات المحلية
      await SecureStore.deleteItemAsync('authToken');
      setUser(null);
    }
  };

  const forgotPassword = async (email: string) => {
    try {
      await authService.forgotPassword(email);
    } catch (error) {
      throw error;
    }
  };

  const resetPassword = async (token: string, password: string) => {
    try {
      await authService.resetPassword(token, password);
    } catch (error) {
      throw error;
    }
  };

  const verifyOTP = async (email: string, otp: string, type: 'register' | 'reset') => {
    try {
      const response = await authService.verifyOTP(email, otp, type);
      
      if (response.token) {
        await SecureStore.setItemAsync('authToken', response.token);
        setUser(response.user);
        
        // إعداد الإشعارات
        await notificationService.registerForPushNotifications(response.user.id);
      }

    } catch (error) {
      throw error;
    }
  };

  const updateProfile = async (userData: Partial<User>) => {
    try {
      const updatedUser = await authService.updateProfile(userData);
      setUser(updatedUser);
    } catch (error) {
      throw error;
    }
  };

  const refreshToken = async () => {
    try {
      const token = await SecureStore.getItemAsync('authToken');
      if (!token) {
        throw new Error('لا يوجد توكن محفوظ');
      }

      const response = await authService.refreshToken(token);
      await SecureStore.setItemAsync('authToken', response.token);
      setUser(response.user);

    } catch (error) {
      // في حالة فشل تحديث التوكن، تسجيل خروج المستخدم
      await logout();
      throw error;
    }
  };

  const checkBiometricAvailability = async (): Promise<boolean> => {
    try {
      return await authService.checkBiometricAvailability();
    } catch (error) {
      return false;
    }
  };

  const enableBiometric = async () => {
    try {
      const isAvailable = await checkBiometricAvailability();
      if (!isAvailable) {
        throw new Error('المصادقة البيومترية غير متاحة على هذا الجهاز');
      }

      // حفظ كلمة المرور مشفرة للمصادقة البيومترية
      const email = user?.email;
      if (!email) {
        throw new Error('لا يمكن تفعيل المصادقة البيومترية بدون بريد إلكتروني');
      }

      await authService.enableBiometric(email);
      await AsyncStorage.setItem('biometricEnabled', 'true');

    } catch (error) {
      throw error;
    }
  };

  const disableBiometric = async () => {
    try {
      await authService.disableBiometric();
      await AsyncStorage.removeItem('biometricEnabled');
    } catch (error) {
      throw error;
    }
  };

  const authenticateWithBiometric = async () => {
    try {
      const isEnabled = await AsyncStorage.getItem('biometricEnabled');
      if (!isEnabled) {
        throw new Error('المصادقة البيومترية غير مفعلة');
      }

      const response = await authService.authenticateWithBiometric();
      
      await SecureStore.setItemAsync('authToken', response.token);
      setUser(response.user);
      
      // إعداد الإشعارات
      await notificationService.registerForPushNotifications(response.user.id);

    } catch (error) {
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isFirstTime,
    login,
    register,
    logout,
    forgotPassword,
    resetPassword,
    verifyOTP,
    updateProfile,
    refreshToken,
    checkBiometricAvailability,
    enableBiometric,
    disableBiometric,
    authenticateWithBiometric,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth يجب أن يستخدم داخل AuthProvider');
  }
  return context;
}

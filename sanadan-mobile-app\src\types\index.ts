// أنواع البيانات الأساسية للتطبيق

export interface User {
  id: string;
  name: string;
  email: string;
  phone?: string;
  avatar?: string;
  role: UserRole;
  lawFirmId: string;
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export type UserRole = 'admin' | 'lawyer' | 'employee' | 'client';

export interface LawFirm {
  id: string;
  name: string;
  logo?: string;
  address: string;
  phone: string;
  email: string;
  licenseNumber: string;
  subscriptionPlan: SubscriptionPlan;
  settings: LawFirmSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface LawFirmSettings {
  currency: string;
  timezone: string;
  language: string;
  theme: 'light' | 'dark' | 'auto';
  notifications: NotificationSettings;
  features: FeatureSettings;
}

export interface NotificationSettings {
  email: boolean;
  push: boolean;
  sms: boolean;
  hearingReminders: boolean;
  taskDeadlines: boolean;
  paymentDue: boolean;
}

export interface FeatureSettings {
  aiAssistant: boolean;
  documentGeneration: boolean;
  advancedReports: boolean;
  apiAccess: boolean;
  customBranding: boolean;
}

export type SubscriptionPlan = 'basic' | 'professional' | 'enterprise';

export interface Client {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  idNumber?: string;
  clientType: ClientType;
  companyName?: string;
  commercialRegistration?: string;
  nationality?: string;
  gender?: 'male' | 'female';
  birthDate?: Date;
  notes?: string;
  status: ClientStatus;
  lawFirmId: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export type ClientType = 'individual' | 'company' | 'government';
export type ClientStatus = 'active' | 'inactive' | 'blacklisted';

export interface LegalCase {
  id: string;
  caseNumber: string;
  title: string;
  description?: string;
  caseType: CaseType;
  status: CaseStatus;
  priority: Priority;
  clientId: string;
  lawyerId: string;
  courtId?: string;
  filingDate?: Date;
  estimatedDuration?: number;
  estimatedValue?: number;
  feesAmount?: number;
  feesType: FeesType;
  notes?: string;
  lawFirmId: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface CaseType {
  id: string;
  name: string;
  nameEn: string;
  description?: string;
  color: string;
  icon: string;
  isActive: boolean;
  lawFirmId: string;
}

export type CaseStatus = 
  | 'open' 
  | 'in_progress' 
  | 'pending' 
  | 'on_hold' 
  | 'closed' 
  | 'dismissed' 
  | 'settled' 
  | 'appealed';

export type Priority = 'low' | 'medium' | 'high' | 'urgent';
export type FeesType = 'fixed' | 'hourly' | 'percentage';

export interface Court {
  id: string;
  name: string;
  type: CourtType;
  address: string;
  phone?: string;
  email?: string;
  workingHours?: string;
  location?: {
    latitude: number;
    longitude: number;
  };
  isActive: boolean;
  lawFirmId: string;
}

export type CourtType = 'civil' | 'criminal' | 'commercial' | 'administrative' | 'family' | 'labor';

export interface Hearing {
  id: string;
  caseId: string;
  courtId: string;
  chamberNumber?: string;
  judgeName?: string;
  hearingDate: Date;
  hearingTime: string;
  duration?: number;
  type: HearingType;
  status: HearingStatus;
  agenda?: string;
  notes?: string;
  outcome?: string;
  nextHearingDate?: Date;
  attendees?: string[];
  documents?: string[];
  lawFirmId: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export type HearingType = 'initial' | 'follow_up' | 'final' | 'appeal' | 'execution';
export type HearingStatus = 'scheduled' | 'completed' | 'postponed' | 'cancelled';

export interface Task {
  id: string;
  title: string;
  description?: string;
  assignedTo: string;
  caseId?: string;
  clientId?: string;
  dueDate?: Date;
  priority: Priority;
  status: TaskStatus;
  type: TaskType;
  estimatedHours?: number;
  actualHours?: number;
  tags?: string[];
  attachments?: string[];
  comments?: TaskComment[];
  lawFirmId: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export type TaskStatus = 'pending' | 'in_progress' | 'completed' | 'cancelled' | 'overdue';
export type TaskType = 'call' | 'meeting' | 'document' | 'research' | 'court' | 'follow_up' | 'other';

export interface TaskComment {
  id: string;
  content: string;
  authorId: string;
  authorName: string;
  createdAt: Date;
}

export interface Document {
  id: string;
  name: string;
  fileName: string;
  filePath: string;
  fileType: string;
  fileSize: number;
  mimeType: string;
  category: DocumentCategory;
  caseId?: string;
  clientId?: string;
  description?: string;
  tags?: string[];
  isConfidential: boolean;
  version: number;
  checksum?: string;
  uploadedBy: string;
  lawFirmId: string;
  createdAt: Date;
  updatedAt: Date;
}

export type DocumentCategory = 
  | 'contract' 
  | 'evidence' 
  | 'correspondence' 
  | 'court_document' 
  | 'legal_opinion' 
  | 'power_of_attorney' 
  | 'identification' 
  | 'other';

export interface Invoice {
  id: string;
  invoiceNumber: string;
  clientId: string;
  caseId?: string;
  lineItems: InvoiceLineItem[];
  subtotal: number;
  taxPercentage: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  currency: string;
  status: InvoiceStatus;
  issueDate: Date;
  dueDate: Date;
  paidDate?: Date;
  notes?: string;
  paymentTerms?: string;
  isRecurring: boolean;
  recurringPeriod?: RecurringPeriod;
  lawFirmId: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface InvoiceLineItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  taxable: boolean;
}

export type InvoiceStatus = 'draft' | 'sent' | 'paid' | 'partial' | 'overdue' | 'cancelled';
export type RecurringPeriod = 'monthly' | 'quarterly' | 'yearly';

export interface Payment {
  id: string;
  invoiceId: string;
  amount: number;
  paymentMethod: PaymentMethod;
  paymentDate: Date;
  referenceNumber?: string;
  transactionId?: string;
  status: PaymentStatus;
  notes?: string;
  gatewayResponse?: any;
  fees: number;
  currency: string;
  receivedBy: string;
  lawFirmId: string;
  createdAt: Date;
  updatedAt: Date;
}

export type PaymentMethod = 
  | 'cash' 
  | 'bank_transfer' 
  | 'check' 
  | 'credit_card' 
  | 'knet' 
  | 'stripe' 
  | 'paypal';

export type PaymentStatus = 'pending' | 'completed' | 'failed' | 'refunded';

export interface Expense {
  id: string;
  caseId?: string;
  description: string;
  amount: number;
  currency: string;
  category: ExpenseCategory;
  expenseDate: Date;
  receipt?: string;
  isReimbursable: boolean;
  isApproved: boolean;
  approvedBy?: string;
  approvedAt?: Date;
  notes?: string;
  lawFirmId: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export type ExpenseCategory = 
  | 'court_fees' 
  | 'travel' 
  | 'accommodation' 
  | 'meals' 
  | 'documents' 
  | 'expert_fees' 
  | 'translation' 
  | 'other';

export interface Timesheet {
  id: string;
  userId: string;
  caseId?: string;
  taskId?: string;
  description: string;
  hours: number;
  date: Date;
  hourlyRate?: number;
  billableAmount?: number;
  isBillable: boolean;
  isApproved: boolean;
  approvedBy?: string;
  approvedAt?: Date;
  lawFirmId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: NotificationType;
  priority: Priority;
  userId: string;
  isRead: boolean;
  readAt?: Date;
  data?: any;
  actionUrl?: string;
  expiresAt?: Date;
  createdAt: Date;
}

export type NotificationType = 
  | 'hearing_reminder' 
  | 'task_deadline' 
  | 'payment_due' 
  | 'document_shared' 
  | 'case_update' 
  | 'system_update' 
  | 'security_alert';

export interface DashboardStats {
  totalCases: number;
  activeCases: number;
  upcomingHearings: number;
  pendingTasks: number;
  overdueInvoices: number;
  monthlyRevenue: number;
  totalClients: number;
  totalHours: number;
  casesByStatus: Record<CaseStatus, number>;
  revenueByMonth: Array<{ month: string; revenue: number }>;
  tasksByPriority: Record<Priority, number>;
  hearingsByWeek: Array<{ week: string; count: number }>;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  errors?: Record<string, string[]>;
  meta?: {
    total: number;
    page: number;
    perPage: number;
    totalPages: number;
  };
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  meta: {
    total: number;
    page: number;
    perPage: number;
    totalPages: number;
  };
}

export interface SearchFilters {
  query?: string;
  status?: string;
  priority?: string;
  dateFrom?: Date;
  dateTo?: Date;
  assignedTo?: string;
  caseType?: string;
  clientId?: string;
  page?: number;
  perPage?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

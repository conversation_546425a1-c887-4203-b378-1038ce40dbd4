<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\LegalCase;
use App\Models\Hearing;
use App\Models\Task;
use App\Models\Invoice;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

/**
 * متحكم لوحة التحكم الرئيسية
 * يعرض الإحصائيات والبيانات الأساسية للمستخدمين
 */
class DashboardController extends Controller
{
    /**
     * عرض لوحة التحكم الرئيسية
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $period = $request->get('period', 'month'); // week, month, quarter, year
        
        // تحديد الفترة الزمنية
        $dateRange = $this->getDateRange($period);
        
        // الحصول على الإحصائيات حسب نوع المستخدم
        if ($user->isAdmin()) {
            $stats = $this->getAdminStats($dateRange);
        } elseif ($user->isLawyer()) {
            $stats = $this->getLawyerStats($user, $dateRange);
        } else {
            $stats = $this->getEmployeeStats($user, $dateRange);
        }
        
        // البيانات المشتركة
        $commonData = [
            'upcoming_hearings' => $this->getUpcomingHearings($user),
            'urgent_tasks' => $this->getUrgentTasks($user),
            'recent_activities' => $this->getRecentActivities($user),
            'calendar_events' => $this->getCalendarEvents($user),
        ];
        
        return view('dashboard.index', array_merge($stats, $commonData, [
            'user' => $user,
            'period' => $period,
        ]));
    }

    /**
     * الحصول على إحصائيات المدير
     */
    private function getAdminStats(array $dateRange): array
    {
        return [
            // إحصائيات عامة
            'total_cases' => LegalCase::count(),
            'active_cases' => LegalCase::active()->count(),
            'total_clients' => Client::count(),
            'total_lawyers' => User::where('user_type', 'lawyer')->count(),
            
            // إحصائيات الفترة المحددة
            'new_cases' => LegalCase::whereBetween('created_at', $dateRange)->count(),
            'new_clients' => Client::whereBetween('created_at', $dateRange)->count(),
            'completed_tasks' => Task::where('status', 'completed')
                ->whereBetween('completed_at', $dateRange)->count(),
            
            // الإحصائيات المالية
            'total_revenue' => Payment::whereBetween('payment_date', $dateRange)->sum('amount'),
            'pending_invoices' => Invoice::where('status', 'pending')->sum('total_amount'),
            'overdue_invoices' => Invoice::where('status', 'overdue')->sum('total_amount'),
            
            // إحصائيات الأداء
            'cases_by_status' => $this->getCasesByStatus(),
            'revenue_trend' => $this->getRevenueTrend($dateRange),
            'lawyer_performance' => $this->getLawyerPerformance(),
            'case_types_distribution' => $this->getCaseTypesDistribution(),
        ];
    }

    /**
     * الحصول على إحصائيات المحامي
     */
    private function getLawyerStats(User $lawyer, array $dateRange): array
    {
        return [
            // قضايا المحامي
            'my_cases' => $lawyer->assignedCases()->count(),
            'active_cases' => $lawyer->assignedCases()->active()->count(),
            'new_cases' => $lawyer->assignedCases()
                ->whereBetween('created_at', $dateRange)->count(),
            
            // مهام المحامي
            'pending_tasks' => $lawyer->assignedTasks()
                ->where('status', 'pending')->count(),
            'completed_tasks' => $lawyer->assignedTasks()
                ->where('status', 'completed')
                ->whereBetween('completed_at', $dateRange)->count(),
            
            // الجلسات
            'upcoming_hearings_count' => Hearing::whereHas('legalCase', function($query) use ($lawyer) {
                $query->where('lawyer_id', $lawyer->id);
            })->where('hearing_date', '>', now())->count(),
            
            // الإحصائيات المالية
            'my_revenue' => $this->getLawyerRevenue($lawyer, $dateRange),
            'billable_hours' => $this->getLawyerBillableHours($lawyer, $dateRange),
            
            // توزيع القضايا
            'cases_by_priority' => $this->getLawyerCasesByPriority($lawyer),
            'cases_by_status' => $this->getLawyerCasesByStatus($lawyer),
        ];
    }

    /**
     * الحصول على إحصائيات الموظف
     */
    private function getEmployeeStats(User $employee, array $dateRange): array
    {
        return [
            // مهام الموظف
            'assigned_tasks' => $employee->assignedTasks()->count(),
            'pending_tasks' => $employee->assignedTasks()
                ->where('status', 'pending')->count(),
            'completed_tasks' => $employee->assignedTasks()
                ->where('status', 'completed')
                ->whereBetween('completed_at', $dateRange)->count(),
            'overdue_tasks' => $employee->assignedTasks()
                ->where('due_date', '<', now())
                ->where('status', '!=', 'completed')->count(),
            
            // ساعات العمل
            'total_hours' => $employee->timesheets()
                ->whereBetween('date', $dateRange)->sum('hours'),
            'avg_daily_hours' => $employee->timesheets()
                ->whereBetween('date', $dateRange)->avg('hours'),
            
            // الأداء
            'task_completion_rate' => $this->getTaskCompletionRate($employee, $dateRange),
            'tasks_by_status' => $this->getEmployeeTasksByStatus($employee),
        ];
    }

    /**
     * الحصول على الجلسات القادمة
     */
    private function getUpcomingHearings(User $user, int $limit = 5)
    {
        $query = Hearing::with(['legalCase.client', 'court'])
            ->where('hearing_date', '>', now())
            ->where('status', 'scheduled')
            ->orderBy('hearing_date');

        if (!$user->isAdmin()) {
            $query->whereHas('legalCase', function($q) use ($user) {
                if ($user->isLawyer()) {
                    $q->where('lawyer_id', $user->id);
                } else {
                    // للموظفين: الجلسات للقضايا التي لديهم مهام فيها
                    $q->whereHas('tasks', function($taskQuery) use ($user) {
                        $taskQuery->where('assigned_to', $user->id);
                    });
                }
            });
        }

        return $query->limit($limit)->get();
    }

    /**
     * الحصول على المهام العاجلة
     */
    private function getUrgentTasks(User $user, int $limit = 5)
    {
        $query = Task::with(['legalCase', 'assignedUser'])
            ->where('status', '!=', 'completed')
            ->where(function($q) {
                $q->where('priority', 'urgent')
                  ->orWhere('due_date', '<=', now()->addDays(2));
            })
            ->orderBy('due_date');

        if (!$user->isAdmin()) {
            $query->where('assigned_to', $user->id);
        }

        return $query->limit($limit)->get();
    }

    /**
     * الحصول على الأنشطة الأخيرة
     */
    private function getRecentActivities(User $user, int $limit = 10)
    {
        // استخدام Spatie Activity Log
        return activity()
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * الحصول على أحداث التقويم
     */
    private function getCalendarEvents(User $user)
    {
        $events = collect();

        // إضافة الجلسات
        $hearings = $this->getUpcomingHearings($user, 20);
        foreach ($hearings as $hearing) {
            $events->push([
                'id' => 'hearing_' . $hearing->id,
                'title' => 'جلسة: ' . $hearing->legalCase->title,
                'start' => $hearing->hearing_date->toISOString(),
                'type' => 'hearing',
                'color' => '#dc2626',
                'url' => route('hearings.show', $hearing),
            ]);
        }

        // إضافة المهام
        $tasks = Task::where('assigned_to', $user->id)
            ->where('status', '!=', 'completed')
            ->whereNotNull('due_date')
            ->where('due_date', '>=', now())
            ->where('due_date', '<=', now()->addMonth())
            ->get();

        foreach ($tasks as $task) {
            $events->push([
                'id' => 'task_' . $task->id,
                'title' => 'مهمة: ' . $task->title,
                'start' => $task->due_date->toISOString(),
                'type' => 'task',
                'color' => '#059669',
                'url' => route('tasks.show', $task),
            ]);
        }

        return $events;
    }

    /**
     * تحديد نطاق التاريخ حسب الفترة
     */
    private function getDateRange(string $period): array
    {
        $now = Carbon::now();
        
        return match($period) {
            'week' => [$now->startOfWeek(), $now->endOfWeek()],
            'month' => [$now->startOfMonth(), $now->endOfMonth()],
            'quarter' => [$now->startOfQuarter(), $now->endOfQuarter()],
            'year' => [$now->startOfYear(), $now->endOfYear()],
            default => [$now->startOfMonth(), $now->endOfMonth()],
        };
    }

    /**
     * الحصول على توزيع القضايا حسب الحالة
     */
    private function getCasesByStatus(): array
    {
        return LegalCase::select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();
    }

    /**
     * الحصول على اتجاه الإيرادات
     */
    private function getRevenueTrend(array $dateRange): array
    {
        return Payment::selectRaw('DATE(payment_date) as date, SUM(amount) as total')
            ->whereBetween('payment_date', $dateRange)
            ->groupBy('date')
            ->orderBy('date')
            ->get()
            ->pluck('total', 'date')
            ->toArray();
    }

    /**
     * الحصول على أداء المحامين
     */
    private function getLawyerPerformance(): array
    {
        return User::where('user_type', 'lawyer')
            ->withCount(['assignedCases', 'assignedTasks'])
            ->with(['assignedCases' => function($query) {
                $query->select('lawyer_id', DB::raw('count(*) as active_cases'))
                    ->active()
                    ->groupBy('lawyer_id');
            }])
            ->get()
            ->map(function($lawyer) {
                return [
                    'name' => $lawyer->name,
                    'total_cases' => $lawyer->assigned_cases_count,
                    'active_cases' => $lawyer->assignedCases->count(),
                    'total_tasks' => $lawyer->assigned_tasks_count,
                ];
            })
            ->toArray();
    }

    /**
     * الحصول على توزيع أنواع القضايا
     */
    private function getCaseTypesDistribution(): array
    {
        return LegalCase::with('caseType')
            ->get()
            ->groupBy('caseType.name')
            ->map(function($cases) {
                return $cases->count();
            })
            ->toArray();
    }

    /**
     * الحصول على إيرادات المحامي
     */
    private function getLawyerRevenue(User $lawyer, array $dateRange): float
    {
        return Payment::whereHas('invoice.legalCase', function($query) use ($lawyer) {
            $query->where('lawyer_id', $lawyer->id);
        })
        ->whereBetween('payment_date', $dateRange)
        ->sum('amount');
    }

    /**
     * الحصول على ساعات العمل القابلة للفوترة للمحامي
     */
    private function getLawyerBillableHours(User $lawyer, array $dateRange): float
    {
        return $lawyer->timesheets()
            ->whereBetween('date', $dateRange)
            ->sum('hours');
    }

    /**
     * الحصول على قضايا المحامي حسب الأولوية
     */
    private function getLawyerCasesByPriority(User $lawyer): array
    {
        return $lawyer->assignedCases()
            ->select('priority', DB::raw('count(*) as count'))
            ->groupBy('priority')
            ->pluck('count', 'priority')
            ->toArray();
    }

    /**
     * الحصول على قضايا المحامي حسب الحالة
     */
    private function getLawyerCasesByStatus(User $lawyer): array
    {
        return $lawyer->assignedCases()
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();
    }

    /**
     * الحصول على معدل إنجاز المهام للموظف
     */
    private function getTaskCompletionRate(User $employee, array $dateRange): float
    {
        $totalTasks = $employee->assignedTasks()
            ->whereBetween('created_at', $dateRange)
            ->count();

        if ($totalTasks === 0) {
            return 0;
        }

        $completedTasks = $employee->assignedTasks()
            ->where('status', 'completed')
            ->whereBetween('created_at', $dateRange)
            ->count();

        return round(($completedTasks / $totalTasks) * 100, 2);
    }

    /**
     * الحصول على مهام الموظف حسب الحالة
     */
    private function getEmployeeTasksByStatus(User $employee): array
    {
        return $employee->assignedTasks()
            ->select('status', DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();
    }

    /**
     * API endpoint للحصول على الإحصائيات
     */
    public function getStats(Request $request)
    {
        $user = Auth::user();
        $period = $request->get('period', 'month');
        $dateRange = $this->getDateRange($period);
        
        if ($user->isAdmin()) {
            $stats = $this->getAdminStats($dateRange);
        } elseif ($user->isLawyer()) {
            $stats = $this->getLawyerStats($user, $dateRange);
        } else {
            $stats = $this->getEmployeeStats($user, $dateRange);
        }
        
        return response()->json($stats);
    }
}

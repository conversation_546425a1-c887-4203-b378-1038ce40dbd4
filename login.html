<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - منصة سندان</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        
        .login-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        .slide-in {
            animation: slideIn 0.6s ease-out;
        }
        
        @keyframes slideIn {
            from {
                transform: translateY(30px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .desktop-layout {
                display: none !important;
            }
            .mobile-layout {
                display: block !important;
            }
        }
        
        @media (min-width: 769px) {
            .desktop-layout {
                display: block !important;
            }
            .mobile-layout {
                display: none !important;
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-arabic" x-data="loginApp()" x-init="init()">
    <!-- Desktop Layout -->
    <div class="desktop-layout min-h-screen flex">
        <!-- Left Side - Login Form -->
        <div class="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-20 xl:px-24">
            <div class="mx-auto w-full max-w-sm lg:w-96">
                <!-- Logo and Title -->
                <div class="text-center mb-8 fade-in">
                    <div class="mx-auto h-16 w-16 bg-primary-600 rounded-2xl flex items-center justify-center mb-4">
                        <i class="fas fa-balance-scale text-white text-2xl"></i>
                    </div>
                    <h2 class="text-3xl font-bold text-gray-900">منصة سندان</h2>
                    <p class="mt-2 text-sm text-gray-600">نظام إدارة مكاتب المحاماة</p>
                </div>

                <!-- Login Form -->
                <div class="slide-in">
                    <div class="mb-6">
                        <h3 class="text-2xl font-bold text-gray-900">تسجيل الدخول</h3>
                        <p class="mt-2 text-sm text-gray-600">مرحباً بك مرة أخرى! يرجى تسجيل الدخول إلى حسابك</p>
                    </div>

                    <!-- Demo Credentials -->
                    <div class="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                        <div class="flex">
                            <i class="fas fa-info-circle text-blue-400 ml-3"></i>
                            <div class="text-sm text-blue-700">
                                <p class="font-semibold">بيانات تجريبية:</p>
                                <p>البريد: <EMAIL></p>
                                <p>كلمة المرور: 123456</p>
                            </div>
                        </div>
                    </div>

                    <!-- Error Messages -->
                    <div x-show="errorMessage" x-transition class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div class="flex">
                            <i class="fas fa-exclamation-circle text-red-400 ml-3"></i>
                            <div class="text-sm text-red-700" x-text="errorMessage"></div>
                        </div>
                    </div>

                    <form @submit.prevent="login()" class="space-y-6">
                        <!-- Email -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                البريد الإلكتروني
                            </label>
                            <div class="relative">
                                <input id="email" 
                                       x-model="credentials.email"
                                       type="email" 
                                       required 
                                       class="appearance-none block w-full px-3 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm"
                                       placeholder="أدخل بريدك الإلكتروني">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-envelope text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Password -->
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                كلمة المرور
                            </label>
                            <div class="relative" x-data="{ showPassword: false }">
                                <input id="password" 
                                       x-model="credentials.password"
                                       :type="showPassword ? 'text' : 'password'" 
                                       required
                                       class="appearance-none block w-full px-3 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm"
                                       placeholder="أدخل كلمة المرور">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                    <button type="button" @click="showPassword = !showPassword" class="text-gray-400 hover:text-gray-600">
                                        <i class="fas" :class="showPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Remember Me & Forgot Password -->
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <input id="remember" 
                                       x-model="credentials.remember"
                                       type="checkbox" 
                                       class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                                <label for="remember" class="mr-2 block text-sm text-gray-900">
                                    تذكرني
                                </label>
                            </div>

                            <div class="text-sm">
                                <a href="#" class="font-medium text-primary-600 hover:text-primary-500">
                                    نسيت كلمة المرور؟
                                </a>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div>
                            <button type="submit" 
                                    :disabled="loading"
                                    class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
                                <span x-show="!loading">تسجيل الدخول</span>
                                <span x-show="loading" class="flex items-center">
                                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    جاري تسجيل الدخول...
                                </span>
                            </button>
                        </div>

                        <!-- Register Link -->
                        <div class="text-center">
                            <p class="text-sm text-gray-600">
                                ليس لديك حساب؟
                                <a href="register.html" class="font-medium text-primary-600 hover:text-primary-500">
                                    إنشاء حساب جديد
                                </a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Right Side - Image/Branding -->
        <div class="hidden lg:block relative w-0 flex-1">
            <div class="absolute inset-0 login-bg flex items-center justify-center">
                <div class="text-center text-white p-8">
                    <div class="glass-effect rounded-2xl p-8 max-w-md">
                        <i class="fas fa-balance-scale text-6xl mb-6"></i>
                        <h3 class="text-2xl font-bold mb-4">منصة سندان</h3>
                        <p class="text-lg mb-6">نظام إدارة مكاتب المحاماة الأكثر تطوراً في الكويت</p>
                        <div class="space-y-3 text-sm">
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check-circle ml-2"></i>
                                <span>إدارة شاملة للقضايا والعملاء</span>
                            </div>
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check-circle ml-2"></i>
                                <span>تقويم ذكي للجلسات والمواعيد</span>
                            </div>
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check-circle ml-2"></i>
                                <span>تقارير مالية وإحصائيات متقدمة</span>
                            </div>
                            <div class="flex items-center justify-center">
                                <i class="fas fa-check-circle ml-2"></i>
                                <span>أمان عالي وحماية للبيانات</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Layout -->
    <div class="mobile-layout min-h-screen flex flex-col justify-center py-12 px-4 sm:px-6">
        <div class="sm:mx-auto sm:w-full sm:max-w-md">
            <!-- Mobile Logo and Title -->
            <div class="text-center mb-8 fade-in">
                <div class="mx-auto h-12 w-12 bg-primary-600 rounded-xl flex items-center justify-center mb-4">
                    <i class="fas fa-balance-scale text-white text-xl"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-900">منصة سندان</h2>
                <p class="mt-2 text-sm text-gray-600">نظام إدارة مكاتب المحاماة</p>
            </div>

            <!-- Mobile Login Form -->
            <div class="bg-white py-8 px-4 shadow-lg rounded-lg slide-in">
                <div class="mb-6">
                    <h3 class="text-xl font-bold text-gray-900 text-center">تسجيل الدخول</h3>
                    <p class="mt-2 text-sm text-gray-600 text-center">مرحباً بك مرة أخرى!</p>
                </div>

                <!-- Demo Credentials -->
                <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div class="text-xs text-blue-700">
                        <p class="font-semibold">بيانات تجريبية:</p>
                        <p><EMAIL> / 123456</p>
                    </div>
                </div>

                <!-- Mobile form content -->
                <form @submit.prevent="login()" class="space-y-6">
                    <!-- Email -->
                    <div>
                        <label for="mobile-email" class="block text-sm font-medium text-gray-700 mb-2">
                            البريد الإلكتروني
                        </label>
                        <input id="mobile-email" 
                               x-model="credentials.email"
                               type="email" 
                               required 
                               class="appearance-none block w-full px-3 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-base"
                               placeholder="أدخل بريدك الإلكتروني">
                    </div>

                    <!-- Password -->
                    <div>
                        <label for="mobile-password" class="block text-sm font-medium text-gray-700 mb-2">
                            كلمة المرور
                        </label>
                        <input id="mobile-password" 
                               x-model="credentials.password"
                               type="password" 
                               required
                               class="appearance-none block w-full px-3 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-base"
                               placeholder="أدخل كلمة المرور">
                    </div>

                    <!-- Submit Button -->
                    <div>
                        <button type="submit" 
                                :disabled="loading"
                                class="w-full flex justify-center py-3 px-4 border border-transparent text-base font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
                            <span x-show="!loading">تسجيل الدخول</span>
                            <span x-show="loading" class="flex items-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                جاري تسجيل الدخول...
                            </span>
                        </button>
                    </div>

                    <!-- Register Link -->
                    <div class="text-center">
                        <p class="text-sm text-gray-600">
                            ليس لديك حساب؟
                            <a href="register.html" class="font-medium text-primary-600 hover:text-primary-500">
                                إنشاء حساب جديد
                            </a>
                        </p>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function loginApp() {
            return {
                loading: false,
                errorMessage: '',
                credentials: {
                    email: '<EMAIL>',
                    password: '123456',
                    remember: false
                },
                
                init() {
                    // تحقق من وجود جلسة مسبقة
                    if (localStorage.getItem('sanadan_user')) {
                        window.location.href = 'dashboard.html';
                    }
                },
                
                async login() {
                    this.loading = true;
                    this.errorMessage = '';
                    
                    // محاكاة طلب تسجيل الدخول
                    try {
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        
                        // التحقق من البيانات
                        if (this.credentials.email === '<EMAIL>' && this.credentials.password === '123456') {
                            // حفظ بيانات المستخدم
                            const user = {
                                id: 1,
                                name: 'أحمد المحامي',
                                email: this.credentials.email,
                                role: 'admin',
                                avatar: 'https://ui-avatars.com/api/?name=أحمد+المحامي&background=3b82f6&color=ffffff',
                                loginTime: new Date().toISOString()
                            };
                            
                            localStorage.setItem('sanadan_user', JSON.stringify(user));
                            
                            // إعادة التوجيه للوحة التحكم
                            window.location.href = 'dashboard.html';
                        } else {
                            this.errorMessage = 'البريد الإلكتروني أو كلمة المرور غير صحيحة';
                        }
                    } catch (error) {
                        this.errorMessage = 'حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.';
                    } finally {
                        this.loading = false;
                    }
                }
            }
        }
    </script>
</body>
</html>

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل الهجرة
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_number')->unique(); // رقم الفاتورة
            $table->foreignId('client_id')->constrained('clients');
            $table->foreignId('legal_case_id')->nullable()->constrained('legal_cases');
            $table->enum('type', ['service', 'consultation', 'court_fees', 'expenses', 'retainer'])->default('service');
            $table->decimal('subtotal', 10, 3)->default(0); // المبلغ قبل الضريبة
            $table->decimal('tax_percentage', 5, 2)->default(0); // نسبة الضريبة
            $table->decimal('tax_amount', 10, 3)->default(0); // مبلغ الضريبة
            $table->decimal('discount_amount', 10, 3)->default(0); // مبلغ الخصم
            $table->decimal('total_amount', 10, 3); // المبلغ الإجمالي
            $table->enum('status', ['draft', 'sent', 'paid', 'partial', 'overdue', 'cancelled'])->default('draft');
            $table->date('issue_date'); // تاريخ الإصدار
            $table->date('due_date'); // تاريخ الاستحقاق
            $table->date('paid_date')->nullable(); // تاريخ الدفع
            $table->text('notes')->nullable();
            $table->json('line_items'); // بنود الفاتورة
            $table->string('currency', 3)->default('KWD'); // العملة
            $table->boolean('is_recurring')->default(false); // فاتورة متكررة
            $table->enum('recurring_period', ['monthly', 'quarterly', 'yearly'])->nullable();
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['client_id', 'status']);
            $table->index(['status', 'due_date']);
            $table->index('invoice_number');
            $table->index('issue_date');
        });
    }

    /**
     * التراجع عن الهجرة
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};

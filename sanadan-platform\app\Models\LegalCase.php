<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Laravel\Scout\Searchable;

/**
 * نموذج القضية القانونية
 * 
 * @property int $id
 * @property string $case_number رقم القضية
 * @property string $title عنوان القضية
 * @property string $description وصف القضية
 * @property int $case_type_id نوع القضية
 * @property string $status حالة القضية
 * @property string $priority أولوية القضية (low, medium, high, urgent)
 * @property int $client_id العميل
 * @property int $lawyer_id المحامي المسؤول
 * @property int $court_id المحكمة
 * @property \Carbon\Carbon $filing_date تاريخ رفع القضية
 * @property \Carbon\Carbon $next_hearing_date تاريخ الجلسة القادمة
 * @property \Carbon\Carbon $estimated_completion_date التاريخ المتوقع للانتهاء
 * @property float $estimated_value القيمة المتوقعة للقضية
 * @property float $fees_amount أتعاب المحاماة
 * @property string $fees_type نوع الأتعاب (fixed, hourly, percentage)
 * @property array $case_parties أطراف القضية
 * @property array $legal_references المراجع القانونية
 * @property string $notes ملاحظات
 * @property int $created_by المستخدم الذي أنشأ القضية
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 * @property \Carbon\Carbon $deleted_at
 */
class LegalCase extends Model implements HasMedia
{
    use HasFactory, SoftDeletes, LogsActivity, InteractsWithMedia, Searchable;

    /**
     * اسم الجدول
     */
    protected $table = 'legal_cases';

    /**
     * الحقول القابلة للتعبئة
     */
    protected $fillable = [
        'case_number',
        'title',
        'description',
        'case_type_id',
        'status',
        'priority',
        'client_id',
        'lawyer_id',
        'court_id',
        'filing_date',
        'next_hearing_date',
        'estimated_completion_date',
        'estimated_value',
        'fees_amount',
        'fees_type',
        'case_parties',
        'legal_references',
        'notes',
        'created_by',
    ];

    /**
     * تحويل الحقول إلى أنواع البيانات المناسبة
     */
    protected $casts = [
        'filing_date' => 'date',
        'next_hearing_date' => 'datetime',
        'estimated_completion_date' => 'date',
        'estimated_value' => 'decimal:2',
        'fees_amount' => 'decimal:2',
        'case_parties' => 'array',
        'legal_references' => 'array',
    ];

    /**
     * القيم الافتراضية للحقول
     */
    protected $attributes = [
        'status' => 'open',
        'priority' => 'medium',
        'fees_type' => 'fixed',
        'case_parties' => '[]',
        'legal_references' => '[]',
    ];

    /**
     * إعدادات تسجيل النشاط
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['case_number', 'title', 'status', 'priority', 'lawyer_id'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * تكوين مجموعات الوسائط
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('documents')
            ->acceptsMimeTypes([
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'image/jpeg',
                'image/png'
            ]);

        $this->addMediaCollection('evidence')
            ->acceptsMimeTypes([
                'application/pdf',
                'image/jpeg',
                'image/png',
                'video/mp4',
                'audio/mpeg'
            ]);
    }

    /**
     * إعدادات البحث
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'case_number' => $this->case_number,
            'title' => $this->title,
            'description' => $this->description,
            'status' => $this->status,
            'client_name' => $this->client->name ?? '',
            'lawyer_name' => $this->lawyer->name ?? '',
            'case_type' => $this->caseType->name ?? '',
        ];
    }

    /**
     * العلاقة مع العميل
     */
    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * العلاقة مع المحامي المسؤول
     */
    public function lawyer()
    {
        return $this->belongsTo(User::class, 'lawyer_id');
    }

    /**
     * العلاقة مع نوع القضية
     */
    public function caseType()
    {
        return $this->belongsTo(CaseType::class);
    }

    /**
     * العلاقة مع المحكمة
     */
    public function court()
    {
        return $this->belongsTo(Court::class);
    }

    /**
     * العلاقة مع المستخدم الذي أنشأ القضية
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * العلاقة مع الجلسات
     */
    public function hearings()
    {
        return $this->hasMany(Hearing::class);
    }

    /**
     * العلاقة مع المهام
     */
    public function tasks()
    {
        return $this->hasMany(Task::class);
    }

    /**
     * العلاقة مع المستندات
     */
    public function documents()
    {
        return $this->hasMany(Document::class);
    }

    /**
     * العلاقة مع الفواتير
     */
    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * العلاقة مع ساعات العمل
     */
    public function timesheets()
    {
        return $this->hasMany(Timesheet::class);
    }

    /**
     * العلاقة مع المصروفات
     */
    public function expenses()
    {
        return $this->hasMany(Expense::class);
    }

    /**
     * إنشاء رقم قضية تلقائي
     */
    public static function generateCaseNumber(): string
    {
        $year = now()->year;
        $lastCase = static::whereYear('created_at', $year)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastCase ? (int) substr($lastCase->case_number, -4) + 1 : 1;
        
        return sprintf('%d-%04d', $year, $sequence);
    }

    /**
     * الحصول على الجلسة القادمة
     */
    public function getNextHearing()
    {
        return $this->hearings()
            ->where('hearing_date', '>', now())
            ->orderBy('hearing_date')
            ->first();
    }

    /**
     * الحصول على آخر جلسة
     */
    public function getLastHearing()
    {
        return $this->hearings()
            ->orderBy('hearing_date', 'desc')
            ->first();
    }

    /**
     * الحصول على إجمالي ساعات العمل
     */
    public function getTotalHours(): float
    {
        return $this->timesheets()->sum('hours');
    }

    /**
     * الحصول على إجمالي المصروفات
     */
    public function getTotalExpenses(): float
    {
        return $this->expenses()->sum('amount');
    }

    /**
     * الحصول على إجمالي الفواتير
     */
    public function getTotalInvoiced(): float
    {
        return $this->invoices()->sum('total_amount');
    }

    /**
     * الحصول على المبلغ المدفوع
     */
    public function getTotalPaid(): float
    {
        return $this->invoices()
            ->with('payments')
            ->get()
            ->sum(function ($invoice) {
                return $invoice->payments->sum('amount');
            });
    }

    /**
     * الحصول على المبلغ المستحق
     */
    public function getOutstandingAmount(): float
    {
        return $this->getTotalInvoiced() - $this->getTotalPaid();
    }

    /**
     * التحقق من كون القضية نشطة
     */
    public function isActive(): bool
    {
        return !in_array($this->status, ['closed', 'dismissed', 'settled']);
    }

    /**
     * التحقق من كون القضية عاجلة
     */
    public function isUrgent(): bool
    {
        return $this->priority === 'urgent';
    }

    /**
     * التحقق من تأخر القضية
     */
    public function isOverdue(): bool
    {
        return $this->estimated_completion_date && 
               $this->estimated_completion_date->isPast() && 
               $this->isActive();
    }

    /**
     * إغلاق القضية
     */
    public function close(string $outcome = null, string $notes = null): void
    {
        $this->update([
            'status' => 'closed',
            'notes' => $notes ? $this->notes . "\n\n" . $notes : $this->notes,
        ]);

        if ($outcome) {
            $this->addCaseParty('outcome', $outcome);
        }
    }

    /**
     * إعادة فتح القضية
     */
    public function reopen(string $reason = null): void
    {
        $this->update(['status' => 'open']);

        if ($reason) {
            $this->notes = $this->notes . "\n\n" . "تم إعادة فتح القضية: " . $reason;
            $this->save();
        }
    }

    /**
     * إضافة طرف للقضية
     */
    public function addCaseParty(string $type, string $name, array $details = []): void
    {
        $parties = $this->case_parties;
        $parties[] = array_merge([
            'type' => $type,
            'name' => $name,
            'added_at' => now()->toISOString(),
        ], $details);
        
        $this->update(['case_parties' => $parties]);
    }

    /**
     * إضافة مرجع قانوني
     */
    public function addLegalReference(string $type, string $reference, string $description = null): void
    {
        $references = $this->legal_references;
        $references[] = [
            'type' => $type,
            'reference' => $reference,
            'description' => $description,
            'added_at' => now()->toISOString(),
        ];
        
        $this->update(['legal_references' => $references]);
    }

    /**
     * تحديث الجلسة القادمة
     */
    public function updateNextHearing(): void
    {
        $nextHearing = $this->getNextHearing();
        $this->update([
            'next_hearing_date' => $nextHearing ? $nextHearing->hearing_date : null
        ]);
    }

    /**
     * البحث في القضايا
     */
    public function scopeSearch($query, string $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('case_number', 'like', "%{$search}%")
              ->orWhere('title', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%");
        });
    }

    /**
     * فلترة حسب الحالة
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * فلترة حسب الأولوية
     */
    public function scopeWithPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * فلترة حسب المحامي
     */
    public function scopeForLawyer($query, int $lawyerId)
    {
        return $query->where('lawyer_id', $lawyerId);
    }

    /**
     * فلترة حسب العميل
     */
    public function scopeForClient($query, int $clientId)
    {
        return $query->where('client_id', $clientId);
    }

    /**
     * فلترة القضايا النشطة
     */
    public function scopeActive($query)
    {
        return $query->whereNotIn('status', ['closed', 'dismissed', 'settled']);
    }

    /**
     * فلترة القضايا المتأخرة
     */
    public function scopeOverdue($query)
    {
        return $query->where('estimated_completion_date', '<', now())
                    ->whereNotIn('status', ['closed', 'dismissed', 'settled']);
    }
}

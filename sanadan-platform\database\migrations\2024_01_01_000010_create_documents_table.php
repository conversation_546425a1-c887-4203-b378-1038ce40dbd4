<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل الهجرة
     */
    public function up(): void
    {
        Schema::create('documents', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم المستند
            $table->text('description')->nullable(); // وصف المستند
            $table->string('file_path'); // مسار الملف
            $table->string('file_name'); // اسم الملف الأصلي
            $table->string('file_type'); // نوع الملف
            $table->bigInteger('file_size'); // حجم الملف بالبايت
            $table->string('mime_type'); // نوع MIME
            $table->enum('category', [
                'contract', 'evidence', 'correspondence', 'court_document', 
                'legal_opinion', 'power_of_attorney', 'identification', 'other'
            ])->default('other');
            $table->foreignId('legal_case_id')->nullable()->constrained('legal_cases')->onDelete('cascade');
            $table->foreignId('client_id')->nullable()->constrained('clients');
            $table->boolean('is_confidential')->default(false); // سري
            $table->boolean('is_signed')->default(false); // موقع
            $table->datetime('signed_at')->nullable(); // تاريخ التوقيع
            $table->string('signed_by')->nullable(); // الموقع
            $table->json('metadata')->nullable(); // بيانات إضافية
            $table->integer('version')->default(1); // إصدار المستند
            $table->foreignId('parent_document_id')->nullable()->constrained('documents'); // المستند الأصلي
            $table->foreignId('uploaded_by')->constrained('users');
            $table->timestamps();
            $table->softDeletes();
            
            $table->index(['legal_case_id', 'category']);
            $table->index(['client_id', 'category']);
            $table->index('file_type');
            $table->index('is_confidential');
        });
    }

    /**
     * التراجع عن الهجرة
     */
    public function down(): void
    {
        Schema::dropIfExists('documents');
    }
};

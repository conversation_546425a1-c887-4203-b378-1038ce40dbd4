# دليل التطوير - منصة سندان

## 🚀 إعداد بيئة التطوير

### المتطلبات الأساسية

#### 1. Node.js و npm
```bash
# تثبيت Node.js 18+
# تحميل من: https://nodejs.org/

# التحقق من الإصدار
node --version  # يجب أن يكون 18+
npm --version   # يجب أن يكون 9+
```

#### 2. Expo CLI
```bash
# تثبيت Expo CLI عالمياً
npm install -g @expo/cli

# التحقق من التثبيت
expo --version
```

#### 3. محررات الكود المقترحة
- **Visual Studio Code** مع الإضافات التالية:
  - React Native Tools
  - ES7+ React/Redux/React-Native snippets
  - TypeScript Importer
  - Prettier - Code formatter
  - ESLint
  - Arabic Language Pack

#### 4. أدوات التطوير للمنصات

**للتطوير على Android:**
```bash
# تثبيت Android Studio
# تحميل من: https://developer.android.com/studio

# إعداد متغيرات البيئة
export ANDROID_HOME=$HOME/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/tools/bin
export PATH=$PATH:$ANDROID_HOME/platform-tools
```

**للتطوير على iOS (macOS فقط):**
```bash
# تثبيت Xcode من App Store
# تثبيت Command Line Tools
xcode-select --install

# تثبيت CocoaPods
sudo gem install cocoapods
```

### إعداد المشروع

#### 1. استنساخ المشروع
```bash
git clone https://github.com/sanadan-platform/mobile-app.git
cd sanadan-mobile-app
```

#### 2. تثبيت التبعيات
```bash
# تثبيت تبعيات Node.js
npm install

# للتطوير على iOS (macOS فقط)
cd ios && pod install && cd ..
```

#### 3. إعداد متغيرات البيئة
```bash
# نسخ ملف البيئة النموذجي
cp .env.example .env

# تحرير الملف وإضافة القيم المطلوبة
nano .env
```

#### 4. تشغيل التطبيق
```bash
# تشغيل خادم التطوير
npm start

# تشغيل على Android
npm run android

# تشغيل على iOS
npm run ios

# تشغيل على المتصفح
npm run web
```

## 🏗️ هيكل المشروع

```
sanadan-mobile-app/
├── src/                          # الكود المصدري
│   ├── components/               # المكونات القابلة لإعادة الاستخدام
│   │   ├── common/              # مكونات عامة
│   │   ├── forms/               # مكونات النماذج
│   │   ├── charts/              # مكونات الرسوم البيانية
│   │   └── ui/                  # مكونات واجهة المستخدم
│   ├── screens/                 # شاشات التطبيق
│   │   ├── auth/                # شاشات المصادقة
│   │   ├── dashboard/           # لوحة التحكم
│   │   ├── cases/               # إدارة القضايا
│   │   ├── clients/             # إدارة العملاء
│   │   ├── hearings/            # إدارة الجلسات
│   │   ├── tasks/               # إدارة المهام
│   │   ├── documents/           # إدارة المستندات
│   │   ├── invoices/            # إدارة الفواتير
│   │   ├── payments/            # إدارة المدفوعات
│   │   ├── reports/             # التقارير
│   │   ├── calendar/            # التقويم
│   │   ├── notifications/       # الإشعارات
│   │   ├── profile/             # الملف الشخصي
│   │   └── settings/            # الإعدادات
│   ├── navigation/              # إعدادات التنقل
│   │   ├── AppNavigator.tsx     # التنقل الرئيسي
│   │   ├── AuthNavigator.tsx    # تنقل المصادقة
│   │   └── TabNavigator.tsx     # تنقل التبويبات
│   ├── services/                # خدمات API والبيانات
│   │   ├── apiService.ts        # خدمة API الأساسية
│   │   ├── authService.ts       # خدمة المصادقة
│   │   ├── casesService.ts      # خدمة القضايا
│   │   ├── clientsService.ts    # خدمة العملاء
│   │   ├── notificationService.ts # خدمة الإشعارات
│   │   └── storageService.ts    # خدمة التخزين
│   ├── contexts/                # سياقات React
│   │   ├── AuthContext.tsx      # سياق المصادقة
│   │   ├── ThemeContext.tsx     # سياق التصميم
│   │   ├── LanguageContext.tsx  # سياق اللغة
│   │   └── NotificationContext.tsx # سياق الإشعارات
│   ├── hooks/                   # خطافات مخصصة
│   │   ├── useAuth.ts           # خطاف المصادقة
│   │   ├── useApi.ts            # خطاف API
│   │   ├── useStorage.ts        # خطاف التخزين
│   │   └── usePermissions.ts    # خطاف الصلاحيات
│   ├── utils/                   # دوال مساعدة
│   │   ├── helpers.ts           # دوال مساعدة عامة
│   │   ├── validators.ts        # دوال التحقق
│   │   ├── formatters.ts        # دوال التنسيق
│   │   └── constants.ts         # الثوابت
│   ├── types/                   # تعريفات TypeScript
│   │   ├── index.ts             # الأنواع الأساسية
│   │   ├── api.ts               # أنواع API
│   │   └── navigation.ts        # أنواع التنقل
│   ├── theme/                   # إعدادات التصميم
│   │   ├── theme.ts             # الثيم الأساسي
│   │   ├── colors.ts            # الألوان
│   │   ├── typography.ts        # الخطوط
│   │   └── spacing.ts           # المسافات
│   ├── config/                  # إعدادات التطبيق
│   │   ├── index.ts             # الإعدادات الرئيسية
│   │   ├── api.ts               # إعدادات API
│   │   └── features.ts          # إعدادات الميزات
│   └── assets/                  # الأصول
│       ├── images/              # الصور
│       ├── icons/               # الأيقونات
│       └── fonts/               # الخطوط
├── assets/                      # أصول Expo
│   ├── icon.png                 # أيقونة التطبيق
│   ├── splash.png               # شاشة البداية
│   └── adaptive-icon.png        # أيقونة تكيفية (Android)
├── docs/                        # الوثائق
│   ├── api/                     # وثائق API
│   ├── components/              # وثائق المكونات
│   └── guides/                  # أدلة التطوير
└── tests/                       # الاختبارات
    ├── __mocks__/               # محاكيات الاختبار
    ├── components/              # اختبارات المكونات
    ├── screens/                 # اختبارات الشاشات
    ├── services/                # اختبارات الخدمات
    └── utils/                   # اختبارات الدوال المساعدة
```

## 🎨 معايير الكود

### TypeScript
```typescript
// استخدام أنواع صريحة
interface User {
  id: string;
  name: string;
  email: string;
}

// استخدام Generic Types
function fetchData<T>(url: string): Promise<T> {
  return apiService.get<T>(url);
}

// استخدام Union Types
type Status = 'loading' | 'success' | 'error';
```

### React Components
```typescript
// مكون وظيفي مع TypeScript
interface Props {
  title: string;
  onPress: () => void;
  disabled?: boolean;
}

const CustomButton: React.FC<Props> = ({ 
  title, 
  onPress, 
  disabled = false 
}) => {
  return (
    <TouchableOpacity 
      onPress={onPress} 
      disabled={disabled}
      style={styles.button}
    >
      <Text style={styles.title}>{title}</Text>
    </TouchableOpacity>
  );
};
```

### Hooks
```typescript
// خطاف مخصص
const useApi = <T>(url: string) => {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchData();
  }, [url]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await apiService.get<T>(url);
      setData(response.data);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return { data, loading, error, refetch: fetchData };
};
```

### Styling
```typescript
// استخدام StyleSheet
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
    padding: spacing.md,
  },
  title: {
    fontSize: fontSizes.xl,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
    marginBottom: spacing.sm,
  },
});
```

## 🧪 الاختبارات

### إعداد الاختبارات
```bash
# تشغيل جميع الاختبارات
npm test

# تشغيل اختبارات محددة
npm test -- --testPathPattern=components

# تشغيل الاختبارات مع التغطية
npm run test:coverage

# تشغيل الاختبارات في وضع المراقبة
npm run test:watch
```

### كتابة الاختبارات
```typescript
// اختبار مكون
import { render, fireEvent } from '@testing-library/react-native';
import CustomButton from '../CustomButton';

describe('CustomButton', () => {
  it('should render correctly', () => {
    const { getByText } = render(
      <CustomButton title="اضغط هنا" onPress={() => {}} />
    );
    
    expect(getByText('اضغط هنا')).toBeTruthy();
  });

  it('should call onPress when pressed', () => {
    const mockOnPress = jest.fn();
    const { getByText } = render(
      <CustomButton title="اضغط هنا" onPress={mockOnPress} />
    );
    
    fireEvent.press(getByText('اضغط هنا'));
    expect(mockOnPress).toHaveBeenCalled();
  });
});
```

## 🔧 أدوات التطوير

### ESLint Configuration
```json
{
  "extends": [
    "@react-native-community",
    "@typescript-eslint/recommended",
    "prettier"
  ],
  "rules": {
    "react-native/no-unused-styles": "error",
    "react-native/split-platform-components": "error",
    "react-native/no-inline-styles": "warn",
    "@typescript-eslint/no-unused-vars": "error"
  }
}
```

### Prettier Configuration
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2,
  "useTabs": false
}
```

### Git Hooks
```json
{
  "husky": {
    "hooks": {
      "pre-commit": "lint-staged",
      "pre-push": "npm test"
    }
  },
  "lint-staged": {
    "*.{ts,tsx}": [
      "eslint --fix",
      "prettier --write",
      "git add"
    ]
  }
}
```

## 📱 البناء والنشر

### البناء للتطوير
```bash
# بناء للتطوير
expo build:android --type apk
expo build:ios --type simulator
```

### البناء للإنتاج
```bash
# بناء Android للإنتاج
eas build --platform android --profile production

# بناء iOS للإنتاج
eas build --platform ios --profile production
```

### النشر
```bash
# نشر على Expo
expo publish

# رفع على Google Play
eas submit --platform android

# رفع على App Store
eas submit --platform ios
```

## 🐛 تصحيح الأخطاء

### أدوات التصحيح
```bash
# تشغيل مع Flipper
npm run android -- --variant=debug

# عرض سجلات Android
adb logcat

# عرض سجلات iOS
xcrun simctl spawn booted log stream --predicate 'process == "Expo"'
```

### Remote Debugging
```javascript
// تفعيل Remote Debugging
if (__DEV__) {
  import('./ReactotronConfig').then(() => console.log('Reactotron Configured'));
}
```

## 📚 الموارد المفيدة

### الوثائق الرسمية
- [React Native Documentation](https://reactnative.dev/docs/getting-started)
- [Expo Documentation](https://docs.expo.dev/)
- [React Navigation](https://reactnavigation.org/docs/getting-started)
- [React Native Paper](https://reactnativepaper.com/)

### أدلة التطوير
- [React Native Performance](https://reactnative.dev/docs/performance)
- [TypeScript with React Native](https://reactnative.dev/docs/typescript)
- [Testing React Native Apps](https://reactnative.dev/docs/testing-overview)

### المجتمع والدعم
- [React Native Community](https://github.com/react-native-community)
- [Expo Forums](https://forums.expo.dev/)
- [Stack Overflow](https://stackoverflow.com/questions/tagged/react-native)

---

## 🤝 المساهمة

### خطوات المساهمة
1. Fork المشروع
2. إنشاء فرع للميزة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

### معايير المراجعة
- ✅ الكود يتبع معايير المشروع
- ✅ جميع الاختبارات تمر بنجاح
- ✅ التوثيق محدث
- ✅ لا توجد تحذيرات ESLint
- ✅ الكود مُنسق بـ Prettier

---

<div align="center">
  <p>صُنع بـ ❤️ لخدمة المجتمع القانوني العربي</p>
</div>

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل الهجرة
     */
    public function up(): void
    {
        Schema::create('hearings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('legal_case_id')->constrained('legal_cases')->onDelete('cascade');
            $table->foreignId('court_id')->constrained('courts');
            $table->string('chamber_name')->nullable(); // اسم الدائرة
            $table->string('judge_name')->nullable(); // اسم القاضي
            $table->datetime('hearing_date'); // تاريخ ووقت الجلسة
            $table->enum('status', ['scheduled', 'completed', 'postponed', 'cancelled'])->default('scheduled');
            $table->enum('type', ['initial', 'follow_up', 'final', 'appeal', 'execution'])->default('follow_up');
            $table->text('agenda')->nullable(); // جدول أعمال الجلسة
            $table->text('outcome')->nullable(); // نتيجة الجلسة
            $table->text('notes')->nullable(); // ملاحظات
            $table->datetime('next_hearing_date')->nullable(); // تاريخ الجلسة التالية
            $table->json('attendees')->nullable(); // الحاضرون
            $table->boolean('reminder_sent')->default(false); // تم إرسال التذكير
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
            
            $table->index(['hearing_date', 'status']);
            $table->index(['legal_case_id', 'hearing_date']);
            $table->index('court_id');
        });
    }

    /**
     * التراجع عن الهجرة
     */
    public function down(): void
    {
        Schema::dropIfExists('hearings');
    }
};

@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* استيراد خطوط Google Fonts للعربية والإنجليزية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

/* إعدادات أساسية للصفحة */
@layer base {
    html {
        font-family: 'Cairo', 'Inter', system-ui, sans-serif;
        scroll-behavior: smooth;
    }

    body {
        @apply bg-gray-50 text-gray-900 antialiased;
        font-feature-settings: 'cv03', 'cv04', 'cv11';
    }

    /* إعدادات RTL */
    [dir="rtl"] {
        font-family: 'Cairo', 'Amiri', system-ui, sans-serif;
    }

    [dir="ltr"] {
        font-family: 'Inter', 'Cairo', system-ui, sans-serif;
    }

    /* تحسين عرض النصوص العربية */
    .arabic-text {
        font-family: 'Cairo', 'Amiri', system-ui, sans-serif;
        line-height: 1.8;
        letter-spacing: 0.025em;
    }

    .english-text {
        font-family: 'Inter', 'Roboto', system-ui, sans-serif;
        line-height: 1.6;
    }

    /* تحسين عرض الأرقام */
    .numbers {
        font-variant-numeric: tabular-nums;
        font-feature-settings: 'tnum';
    }

    /* إعدادات العناوين */
    h1, h2, h3, h4, h5, h6 {
        @apply font-semibold text-gray-900;
        line-height: 1.4;
    }

    h1 { @apply text-3xl lg:text-4xl; }
    h2 { @apply text-2xl lg:text-3xl; }
    h3 { @apply text-xl lg:text-2xl; }
    h4 { @apply text-lg lg:text-xl; }
    h5 { @apply text-base lg:text-lg; }
    h6 { @apply text-sm lg:text-base; }

    /* تحسين عرض الروابط */
    a {
        @apply text-primary-600 hover:text-primary-700 transition-colors duration-200;
    }

    /* تحسين عرض النماذج */
    input, textarea, select {
        @apply transition-all duration-200;
    }

    input:focus, textarea:focus, select:focus {
        @apply ring-2 ring-primary-500 ring-opacity-50 border-primary-500;
    }

    /* تحسين عرض الجداول */
    table {
        @apply w-full border-collapse;
    }

    th {
        @apply bg-gray-50 px-6 py-3 text-start text-xs font-medium text-gray-500 uppercase tracking-wider;
    }

    td {
        @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
    }

    /* تحسين عرض الكود */
    code {
        @apply bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-sm font-mono;
    }

    pre {
        @apply bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto;
    }

    pre code {
        @apply bg-transparent text-inherit p-0;
    }
}

/* مكونات مخصصة */
@layer components {
    /* أزرار مخصصة */
    .btn-lg {
        @apply px-6 py-3 text-base;
    }

    .btn-sm {
        @apply px-3 py-1.5 text-xs;
    }

    .btn-xs {
        @apply px-2 py-1 text-xs;
    }

    /* بطاقات مخصصة */
    .card-hover {
        @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
    }

    .card-bordered {
        @apply border border-gray-200;
    }

    /* نماذج مخصصة */
    .form-group {
        @apply mb-4;
    }

    .form-label {
        @apply block text-sm font-medium text-gray-700 mb-2;
    }

    .form-control {
        @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm 
               focus:outline-none focus:ring-primary-500 focus:border-primary-500 
               sm:text-sm transition-colors duration-200;
    }

    .form-control-lg {
        @apply px-4 py-3 text-base;
    }

    .form-control-sm {
        @apply px-2 py-1 text-sm;
    }

    .form-error {
        @apply border-red-300 focus:border-red-500 focus:ring-red-500;
    }

    .form-success {
        @apply border-green-300 focus:border-green-500 focus:ring-green-500;
    }

    .error-message {
        @apply mt-1 text-sm text-red-600;
    }

    .help-text {
        @apply mt-1 text-sm text-gray-500;
    }

    /* شريط التنقل */
    .navbar {
        @apply bg-white shadow-sm border-b border-gray-200;
    }

    .navbar-brand {
        @apply text-xl font-bold text-primary-600;
    }

    .navbar-nav {
        @apply flex space-x-4;
    }

    .nav-link {
        @apply px-3 py-2 text-sm font-medium text-gray-700 hover:text-primary-600 
               transition-colors duration-200;
    }

    .nav-link.active {
        @apply text-primary-600 bg-primary-50 rounded-md;
    }

    /* الشريط الجانبي */
    .sidebar {
        @apply bg-white shadow-sm border-e border-gray-200 h-full;
    }

    .sidebar-nav {
        @apply space-y-1 p-4;
    }

    .sidebar-link {
        @apply flex items-center px-3 py-2 text-sm font-medium text-gray-700 
               hover:text-primary-600 hover:bg-primary-50 rounded-md 
               transition-all duration-200;
    }

    .sidebar-link.active {
        @apply text-primary-600 bg-primary-50;
    }

    .sidebar-icon {
        @apply w-5 h-5 me-3 text-gray-400;
    }

    .sidebar-link.active .sidebar-icon {
        @apply text-primary-600;
    }

    /* التنبيهات */
    .alert {
        @apply p-4 rounded-md border;
    }

    .alert-success {
        @apply bg-green-50 border-green-200 text-green-800;
    }

    .alert-error {
        @apply bg-red-50 border-red-200 text-red-800;
    }

    .alert-warning {
        @apply bg-yellow-50 border-yellow-200 text-yellow-800;
    }

    .alert-info {
        @apply bg-blue-50 border-blue-200 text-blue-800;
    }

    /* الشارات */
    .badge-lg {
        @apply px-3 py-1 text-sm;
    }

    .badge-sm {
        @apply px-1.5 py-0.5 text-xs;
    }

    /* حالات القضايا */
    .case-status-open {
        @apply bg-blue-100 text-blue-800;
    }

    .case-status-progress {
        @apply bg-yellow-100 text-yellow-800;
    }

    .case-status-pending {
        @apply bg-gray-100 text-gray-800;
    }

    .case-status-closed {
        @apply bg-green-100 text-green-800;
    }

    .case-status-urgent {
        @apply bg-red-100 text-red-800;
    }

    /* أولويات المهام */
    .priority-low {
        @apply bg-green-100 text-green-800;
    }

    .priority-medium {
        @apply bg-yellow-100 text-yellow-800;
    }

    .priority-high {
        @apply bg-orange-100 text-orange-800;
    }

    .priority-urgent {
        @apply bg-red-100 text-red-800;
    }

    /* تحميل البيانات */
    .loading-spinner {
        @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600;
    }

    .loading-dots {
        @apply flex space-x-1;
    }

    .loading-dot {
        @apply h-2 w-2 bg-primary-600 rounded-full animate-bounce;
    }

    .loading-dot:nth-child(2) {
        animation-delay: 0.1s;
    }

    .loading-dot:nth-child(3) {
        animation-delay: 0.2s;
    }

    /* الجداول المتجاوبة */
    .table-responsive {
        @apply overflow-x-auto shadow ring-1 ring-black ring-opacity-5 rounded-lg;
    }

    .table-striped tbody tr:nth-child(even) {
        @apply bg-gray-50;
    }

    .table-hover tbody tr:hover {
        @apply bg-gray-100;
    }

    /* التقويم */
    .calendar-day {
        @apply p-2 text-center border border-gray-200 hover:bg-gray-50 cursor-pointer;
    }

    .calendar-day.today {
        @apply bg-primary-100 text-primary-800 font-semibold;
    }

    .calendar-day.selected {
        @apply bg-primary-600 text-white;
    }

    .calendar-day.has-events {
        @apply bg-yellow-100 text-yellow-800;
    }

    /* الرسوم البيانية */
    .chart-container {
        @apply relative h-64 w-full;
    }

    /* الطباعة */
    @media print {
        .no-print {
            @apply hidden;
        }

        .print-only {
            @apply block;
        }

        body {
            @apply bg-white text-black;
        }

        .card {
            @apply shadow-none border border-gray-300;
        }
    }
}

/* أدوات مساعدة */
@layer utilities {
    /* إخفاء شريط التمرير */
    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }

    /* تحسين الأداء */
    .gpu-accelerated {
        transform: translateZ(0);
        will-change: transform;
    }

    /* تحسين النصوص */
    .text-balance {
        text-wrap: balance;
    }

    .text-pretty {
        text-wrap: pretty;
    }

    /* تحسين الصور */
    .img-fluid {
        @apply max-w-full h-auto;
    }

    .img-cover {
        @apply object-cover w-full h-full;
    }

    .img-contain {
        @apply object-contain w-full h-full;
    }

    /* تحسين الخطوط */
    .font-arabic {
        font-family: 'Cairo', 'Amiri', system-ui, sans-serif;
    }

    .font-english {
        font-family: 'Inter', 'Roboto', system-ui, sans-serif;
    }

    /* تحسين التباعد */
    .space-y-px > * + * {
        margin-top: 1px;
    }

    .space-x-px > * + * {
        margin-inline-start: 1px;
    }

    /* تحسين الحدود */
    .border-dashed {
        border-style: dashed;
    }

    .border-dotted {
        border-style: dotted;
    }

    /* تحسين الظلال */
    .shadow-inner-lg {
        box-shadow: inset 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    /* تحسين التدرجات */
    .gradient-primary {
        background: linear-gradient(135deg, theme('colors.primary.600'), theme('colors.primary.800'));
    }

    .gradient-success {
        background: linear-gradient(135deg, theme('colors.success.600'), theme('colors.success.800'));
    }

    .gradient-warning {
        background: linear-gradient(135deg, theme('colors.warning.600'), theme('colors.warning.800'));
    }

    .gradient-danger {
        background: linear-gradient(135deg, theme('colors.secondary.600'), theme('colors.secondary.800'));
    }
}

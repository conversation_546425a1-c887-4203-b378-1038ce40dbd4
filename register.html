<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب - منصة سندان</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        
        .register-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .slide-in {
            animation: slideIn 0.6s ease-out;
        }
        
        @keyframes slideIn {
            from {
                transform: translateY(30px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }
        
        .fade-in {
            animation: fadeIn 0.8s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        /* Mobile Responsive */
        @media (max-width: 768px) {
            .desktop-layout {
                display: none !important;
            }
            .mobile-layout {
                display: block !important;
            }
        }
        
        @media (min-width: 769px) {
            .desktop-layout {
                display: block !important;
            }
            .mobile-layout {
                display: none !important;
            }
        }
    </style>
</head>
<body class="bg-gray-50 font-arabic" x-data="registerApp()" x-init="init()">
    <!-- Desktop Layout -->
    <div class="desktop-layout min-h-screen flex">
        <!-- Left Side - Register Form -->
        <div class="flex-1 flex flex-col justify-center py-12 px-4 sm:px-6 lg:px-20 xl:px-24">
            <div class="mx-auto w-full max-w-sm lg:w-96">
                <!-- Logo and Title -->
                <div class="text-center mb-8 fade-in">
                    <div class="mx-auto h-16 w-16 bg-primary-600 rounded-2xl flex items-center justify-center mb-4">
                        <i class="fas fa-balance-scale text-white text-2xl"></i>
                    </div>
                    <h2 class="text-3xl font-bold text-gray-900">منصة سندان</h2>
                    <p class="mt-2 text-sm text-gray-600">نظام إدارة مكاتب المحاماة</p>
                </div>

                <!-- Register Form -->
                <div class="slide-in">
                    <div class="mb-6">
                        <h3 class="text-2xl font-bold text-gray-900">إنشاء حساب جديد</h3>
                        <p class="mt-2 text-sm text-gray-600">انضم إلى منصة سندان وابدأ إدارة مكتبك بكفاءة</p>
                    </div>

                    <!-- Success Message -->
                    <div x-show="successMessage" x-transition class="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div class="flex">
                            <i class="fas fa-check-circle text-green-400 ml-3"></i>
                            <div class="text-sm text-green-700" x-text="successMessage"></div>
                        </div>
                    </div>

                    <!-- Error Messages -->
                    <div x-show="errorMessage" x-transition class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                        <div class="flex">
                            <i class="fas fa-exclamation-circle text-red-400 ml-3"></i>
                            <div class="text-sm text-red-700" x-text="errorMessage"></div>
                        </div>
                    </div>

                    <form @submit.prevent="register()" class="space-y-6">
                        <!-- Office Name -->
                        <div>
                            <label for="officeName" class="block text-sm font-medium text-gray-700 mb-2">
                                اسم المكتب
                            </label>
                            <input id="officeName" 
                                   x-model="formData.officeName"
                                   type="text" 
                                   required 
                                   class="appearance-none block w-full px-3 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm"
                                   placeholder="مكتب المحاماة الرائد">
                        </div>

                        <!-- Full Name -->
                        <div>
                            <label for="fullName" class="block text-sm font-medium text-gray-700 mb-2">
                                الاسم الكامل
                            </label>
                            <input id="fullName" 
                                   x-model="formData.fullName"
                                   type="text" 
                                   required 
                                   class="appearance-none block w-full px-3 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm"
                                   placeholder="أحمد محمد المحامي">
                        </div>

                        <!-- Email -->
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                البريد الإلكتروني
                            </label>
                            <input id="email" 
                                   x-model="formData.email"
                                   type="email" 
                                   required 
                                   class="appearance-none block w-full px-3 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm"
                                   placeholder="<EMAIL>">
                        </div>

                        <!-- Phone -->
                        <div>
                            <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                                رقم الهاتف
                            </label>
                            <input id="phone" 
                                   x-model="formData.phone"
                                   type="tel" 
                                   required 
                                   class="appearance-none block w-full px-3 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm"
                                   placeholder="+965 9999 9999">
                        </div>

                        <!-- Password -->
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                                كلمة المرور
                            </label>
                            <div class="relative" x-data="{ showPassword: false }">
                                <input id="password" 
                                       x-model="formData.password"
                                       :type="showPassword ? 'text' : 'password'" 
                                       required
                                       class="appearance-none block w-full px-3 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm"
                                       placeholder="كلمة مرور قوية">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center">
                                    <button type="button" @click="showPassword = !showPassword" class="text-gray-400 hover:text-gray-600">
                                        <i class="fas" :class="showPassword ? 'fa-eye-slash' : 'fa-eye'"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Confirm Password -->
                        <div>
                            <label for="confirmPassword" class="block text-sm font-medium text-gray-700 mb-2">
                                تأكيد كلمة المرور
                            </label>
                            <input id="confirmPassword" 
                                   x-model="formData.confirmPassword"
                                   type="password" 
                                   required
                                   class="appearance-none block w-full px-3 py-3 border border-gray-300 rounded-lg placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 text-sm"
                                   placeholder="أعد كتابة كلمة المرور">
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="flex items-center">
                            <input id="terms" 
                                   x-model="formData.acceptTerms"
                                   type="checkbox" 
                                   required
                                   class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                            <label for="terms" class="mr-2 block text-sm text-gray-900">
                                أوافق على <a href="#" class="text-primary-600 hover:text-primary-500">الشروط والأحكام</a> و <a href="#" class="text-primary-600 hover:text-primary-500">سياسة الخصوصية</a>
                            </label>
                        </div>

                        <!-- Submit Button -->
                        <div>
                            <button type="submit" 
                                    :disabled="loading"
                                    class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200">
                                <span x-show="!loading">إنشاء الحساب</span>
                                <span x-show="loading" class="flex items-center">
                                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                    جاري إنشاء الحساب...
                                </span>
                            </button>
                        </div>

                        <!-- Login Link -->
                        <div class="text-center">
                            <p class="text-sm text-gray-600">
                                لديك حساب بالفعل؟
                                <a href="login.html" class="font-medium text-primary-600 hover:text-primary-500">
                                    تسجيل الدخول
                                </a>
                            </p>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Right Side - Features -->
        <div class="hidden lg:block relative w-0 flex-1">
            <div class="absolute inset-0 register-bg flex items-center justify-center">
                <div class="text-center text-white p-8">
                    <div class="bg-white bg-opacity-20 backdrop-blur-lg rounded-2xl p-8 max-w-md border border-white border-opacity-20">
                        <i class="fas fa-rocket text-6xl mb-6"></i>
                        <h3 class="text-2xl font-bold mb-4">ابدأ رحلتك معنا</h3>
                        <p class="text-lg mb-6">انضم إلى آلاف المحامين الذين يثقون في منصة سندان</p>
                        <div class="space-y-4 text-sm">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle ml-3 text-green-300"></i>
                                <span>نسخة تجريبية مجانية لمدة 14 يوم</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-check-circle ml-3 text-green-300"></i>
                                <span>إعداد سريع في أقل من 5 دقائق</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-check-circle ml-3 text-green-300"></i>
                                <span>دعم فني متاح 24/7</span>
                            </div>
                            <div class="flex items-center">
                                <i class="fas fa-check-circle ml-3 text-green-300"></i>
                                <span>تدريب مجاني لفريقك</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Layout -->
    <div class="mobile-layout min-h-screen py-12 px-4">
        <div class="max-w-md mx-auto">
            <!-- Mobile Logo and Title -->
            <div class="text-center mb-8 fade-in">
                <div class="mx-auto h-12 w-12 bg-primary-600 rounded-xl flex items-center justify-center mb-4">
                    <i class="fas fa-balance-scale text-white text-xl"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-900">منصة سندان</h2>
                <p class="mt-2 text-sm text-gray-600">إنشاء حساب جديد</p>
            </div>

            <!-- Mobile Register Form -->
            <div class="bg-white py-8 px-4 shadow-lg rounded-lg slide-in">
                <form @submit.prevent="register()" class="space-y-4">
                    <input x-model="formData.officeName" type="text" required placeholder="اسم المكتب" 
                           class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    
                    <input x-model="formData.fullName" type="text" required placeholder="الاسم الكامل" 
                           class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    
                    <input x-model="formData.email" type="email" required placeholder="البريد الإلكتروني" 
                           class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    
                    <input x-model="formData.phone" type="tel" required placeholder="رقم الهاتف" 
                           class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    
                    <input x-model="formData.password" type="password" required placeholder="كلمة المرور" 
                           class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">
                    
                    <input x-model="formData.confirmPassword" type="password" required placeholder="تأكيد كلمة المرور" 
                           class="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-primary-500 focus:border-primary-500">

                    <div class="flex items-center">
                        <input x-model="formData.acceptTerms" type="checkbox" required class="h-4 w-4 text-primary-600 rounded">
                        <label class="mr-2 text-sm text-gray-900">أوافق على الشروط والأحكام</label>
                    </div>

                    <button type="submit" :disabled="loading"
                            class="w-full py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-medium disabled:opacity-50">
                        <span x-show="!loading">إنشاء الحساب</span>
                        <span x-show="loading">جاري الإنشاء...</span>
                    </button>

                    <div class="text-center">
                        <a href="login.html" class="text-sm text-primary-600">لديك حساب؟ تسجيل الدخول</a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function registerApp() {
            return {
                loading: false,
                errorMessage: '',
                successMessage: '',
                formData: {
                    officeName: '',
                    fullName: '',
                    email: '',
                    phone: '',
                    password: '',
                    confirmPassword: '',
                    acceptTerms: false
                },
                
                init() {
                    // تحقق من وجود جلسة مسبقة
                    if (localStorage.getItem('sanadan_user')) {
                        window.location.href = 'dashboard.html';
                    }
                },
                
                async register() {
                    this.loading = true;
                    this.errorMessage = '';
                    this.successMessage = '';
                    
                    // التحقق من صحة البيانات
                    if (this.formData.password !== this.formData.confirmPassword) {
                        this.errorMessage = 'كلمة المرور وتأكيد كلمة المرور غير متطابقتين';
                        this.loading = false;
                        return;
                    }
                    
                    if (this.formData.password.length < 6) {
                        this.errorMessage = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                        this.loading = false;
                        return;
                    }
                    
                    if (!this.formData.acceptTerms) {
                        this.errorMessage = 'يجب الموافقة على الشروط والأحكام';
                        this.loading = false;
                        return;
                    }
                    
                    try {
                        // محاكاة طلب التسجيل
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        
                        // إنشاء حساب المستخدم
                        const user = {
                            id: Date.now(),
                            name: this.formData.fullName,
                            email: this.formData.email,
                            phone: this.formData.phone,
                            officeName: this.formData.officeName,
                            role: 'owner',
                            avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(this.formData.fullName)}&background=3b82f6&color=ffffff`,
                            registrationTime: new Date().toISOString(),
                            trialEndsAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString() // 14 يوم
                        };
                        
                        localStorage.setItem('sanadan_user', JSON.stringify(user));
                        
                        this.successMessage = 'تم إنشاء الحساب بنجاح! جاري إعادة التوجيه...';
                        
                        // إعادة التوجيه بعد 2 ثانية
                        setTimeout(() => {
                            window.location.href = 'dashboard.html';
                        }, 2000);
                        
                    } catch (error) {
                        this.errorMessage = 'حدث خطأ أثناء إنشاء الحساب. يرجى المحاولة مرة أخرى.';
                    } finally {
                        this.loading = false;
                    }
                }
            }
        }
    </script>
</body>
</html>

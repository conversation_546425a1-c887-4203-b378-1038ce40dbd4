<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل الهجرة
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained('invoices')->onDelete('cascade');
            $table->string('payment_number')->unique(); // رقم الدفعة
            $table->decimal('amount', 10, 3); // مبلغ الدفعة
            $table->enum('payment_method', [
                'cash', 'bank_transfer', 'check', 'credit_card', 
                'knet', 'stripe', 'paypal'
            ]); // طريقة الدفع
            $table->datetime('payment_date'); // تاريخ الدفع
            $table->string('reference_number')->nullable(); // رقم المرجع
            $table->string('transaction_id')->nullable(); // معرف المعاملة
            $table->enum('status', ['pending', 'completed', 'failed', 'refunded'])->default('completed');
            $table->text('notes')->nullable();
            $table->json('gateway_response')->nullable(); // استجابة بوابة الدفع
            $table->decimal('fees', 8, 3)->default(0); // رسوم المعاملة
            $table->string('currency', 3)->default('KWD');
            $table->foreignId('received_by')->constrained('users'); // المستلم
            $table->timestamps();
            
            $table->index(['invoice_id', 'status']);
            $table->index('payment_date');
            $table->index('payment_method');
            $table->index('reference_number');
        });
    }

    /**
     * التراجع عن الهجرة
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};

{"name": "sanadan-mobile-app", "version": "1.0.0", "description": "منصة سندان لأعمال المحاماة - تطبيق جوال", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios"}, "dependencies": {"expo": "~50.0.0", "react": "18.2.0", "react-native": "0.73.0", "@expo/vector-icons": "^14.0.0", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "react-native-screens": "~3.29.0", "react-native-safe-area-context": "4.8.2", "react-native-gesture-handler": "~2.14.0", "react-native-reanimated": "~3.6.2", "expo-status-bar": "~1.11.1", "expo-font": "~11.10.2", "expo-splash-screen": "~0.26.4", "expo-constants": "~15.4.5", "expo-device": "~5.9.3", "expo-notifications": "~0.27.6", "expo-secure-store": "~12.8.1", "expo-document-picker": "~11.10.1", "expo-image-picker": "~14.7.1", "expo-camera": "~14.1.3", "expo-file-system": "~16.0.6", "expo-sharing": "~11.10.0", "expo-print": "~12.8.1", "expo-calendar": "~12.8.1", "expo-contacts": "~12.8.1", "expo-location": "~16.5.5", "expo-barcode-scanner": "~12.9.3", "react-native-paper": "^5.11.6", "react-native-vector-icons": "^10.0.3", "react-native-chart-kit": "^6.12.0", "react-native-svg": "14.1.0", "react-native-calendars": "^1.1302.0", "react-native-modal": "^13.0.1", "react-native-toast-message": "^2.2.0", "react-native-skeleton-placeholder": "^5.2.4", "react-native-super-grid": "^5.0.0", "react-native-swipe-list-view": "^3.2.9", "react-native-actionsheet": "^2.4.2", "react-native-image-viewing": "^0.2.2", "react-native-pdf": "^6.7.3", "react-native-signature-canvas": "^4.7.2", "react-native-qrcode-svg": "^6.3.0", "react-native-share": "^10.0.2", "react-native-fs": "^2.20.0", "react-native-zip-archive": "^6.1.0", "react-native-biometrics": "^3.0.1", "react-native-keychain": "^8.2.0", "react-native-onesignal": "^5.0.4", "react-native-background-job": "^0.2.9", "react-native-offline": "^6.0.2", "react-native-netinfo": "^11.2.1", "react-native-async-storage": "^1.21.0", "react-native-mmkv": "^2.11.0", "react-native-flipper": "^0.212.0", "react-query": "^3.39.3", "axios": "^1.6.2", "formik": "^2.4.5", "yup": "^1.4.0", "date-fns": "^3.0.6", "lodash": "^4.17.21", "react-native-uuid": "^2.0.1", "react-native-crypto-js": "^1.0.0", "react-native-device-info": "^10.12.0", "react-native-permissions": "^4.1.1", "react-native-orientation-locker": "^1.6.0", "react-native-splash-screen": "^3.3.0", "react-native-code-push": "^8.2.1", "react-native-config": "^1.5.1", "react-native-linear-gradient": "^2.8.3", "react-native-blur": "^4.4.0", "react-native-haptic-feedback": "^2.2.0", "react-native-sound": "^0.11.2", "react-native-video": "^5.2.1", "react-native-webview": "^13.6.4", "react-native-maps": "1.10.0", "react-native-google-places-autocomplete": "^2.5.6", "react-native-image-crop-picker": "^0.40.3", "react-native-image-resizer": "^3.0.8", "react-native-fast-image": "^8.6.3", "react-native-animatable": "^1.4.0", "react-native-progress": "^5.0.1", "react-native-step-indicator": "^1.0.3", "react-native-swiper": "^1.6.0", "react-native-tab-view": "^3.5.2", "react-native-collapsible": "^1.6.1", "react-native-accordion": "^1.0.4", "react-native-searchable-dropdown": "^1.2.3", "react-native-dropdown-picker": "^5.4.6", "react-native-date-picker": "^4.3.3", "react-native-time-picker": "^1.0.1", "react-native-country-picker-modal": "^2.0.0", "react-native-phone-number-input": "^2.1.0", "react-native-otp-inputs": "^7.4.0", "react-native-pin-view": "^2.1.4", "react-native-fingerprint-scanner": "^6.0.0", "react-native-face-id": "^2.0.0"}, "devDependencies": {"@babel/core": "^7.23.6", "@types/react": "~18.2.45", "@types/react-native": "~0.73.0", "typescript": "^5.3.3", "eslint": "^8.56.0", "prettier": "^3.1.1", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-native": "^4.1.0", "metro-config": "^0.80.3", "react-native-flipper": "^0.212.0"}, "keywords": ["react-native", "expo", "legal", "law", "kuwait", "saas", "mobile"], "author": "Sanadan Platform Team", "license": "MIT", "private": true}
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cases', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained()->onDelete('cascade');
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('assigned_lawyer')->constrained('users');
            $table->foreignId('created_by')->constrained('users');
            
            // معلومات أساسية
            $table->string('case_number')->unique(); // رقم القضية
            $table->string('court_case_number')->nullable(); // رقم القضية في المحكمة
            $table->string('title'); // عنوان القضية
            $table->text('description'); // وصف القضية
            
            // تصنيف القضية
            $table->enum('type', [
                'civil', 'commercial', 'criminal', 'administrative', 
                'labor', 'family', 'real_estate', 'intellectual_property',
                'tax', 'insurance', 'banking', 'construction', 'other'
            ]);
            $table->enum('category', [
                'litigation', 'consultation', 'contract_drafting', 
                'legal_review', 'arbitration', 'mediation', 'other'
            ]);
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            
            // حالة القضية
            $table->enum('status', [
                'draft', 'active', 'pending', 'on_hold', 
                'completed', 'cancelled', 'archived'
            ])->default('draft');
            $table->enum('stage', [
                'initial_consultation', 'case_preparation', 'filing',
                'discovery', 'negotiation', 'trial', 'appeal',
                'execution', 'closed'
            ])->default('initial_consultation');
            
            // التواريخ المهمة
            $table->date('case_date'); // تاريخ القضية
            $table->date('filing_date')->nullable(); // تاريخ رفع القضية
            $table->date('first_hearing_date')->nullable(); // تاريخ أول جلسة
            $table->date('expected_completion_date')->nullable(); // التاريخ المتوقع للانتهاء
            $table->date('actual_completion_date')->nullable(); // التاريخ الفعلي للانتهاء
            $table->date('statute_of_limitations')->nullable(); // تاريخ انتهاء التقادم
            
            // المحكمة والقاضي
            $table->string('court_name')->nullable(); // اسم المحكمة
            $table->string('court_type')->nullable(); // نوع المحكمة
            $table->string('judge_name')->nullable(); // اسم القاضي
            $table->string('court_room')->nullable(); // قاعة المحكمة
            
            // الأطراف
            $table->text('plaintiff_details')->nullable(); // تفاصيل المدعي
            $table->text('defendant_details')->nullable(); // تفاصيل المدعى عليه
            $table->text('witnesses')->nullable(); // الشهود
            $table->text('opposing_counsel')->nullable(); // محامي الطرف الآخر
            
            // المالية
            $table->decimal('case_value', 15, 3)->nullable(); // قيمة القضية
            $table->decimal('legal_fees', 10, 3)->default(0); // الأتعاب القانونية
            $table->decimal('court_fees', 10, 3)->default(0); // رسوم المحكمة
            $table->decimal('other_expenses', 10, 3)->default(0); // مصاريف أخرى
            $table->decimal('total_cost', 10, 3)->default(0); // إجمالي التكلفة
            $table->decimal('amount_paid', 10, 3)->default(0); // المبلغ المدفوع
            $table->decimal('amount_due', 10, 3)->default(0); // المبلغ المستحق
            
            // النتيجة
            $table->enum('outcome', [
                'pending', 'won', 'lost', 'settled', 
                'dismissed', 'withdrawn', 'other'
            ])->nullable();
            $table->text('outcome_details')->nullable(); // تفاصيل النتيجة
            $table->text('judgment_summary')->nullable(); // ملخص الحكم
            
            // إعدادات إضافية
            $table->boolean('is_confidential')->default(false); // سرية القضية
            $table->boolean('is_pro_bono')->default(false); // قضية مجانية
            $table->text('notes')->nullable(); // ملاحظات
            $table->json('custom_fields')->nullable(); // حقول مخصصة
            $table->json('tags')->nullable(); // علامات
            
            $table->timestamps();
            $table->softDeletes();
            
            // فهارس
            $table->index(['tenant_id', 'status']);
            $table->index(['tenant_id', 'type']);
            $table->index(['tenant_id', 'assigned_lawyer']);
            $table->index(['tenant_id', 'client_id']);
            $table->index('case_number');
            $table->index('court_case_number');
            $table->index('filing_date');
            $table->index('first_hearing_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cases');
    }
};

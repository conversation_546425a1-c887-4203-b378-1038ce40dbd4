{"name": "sanadan-platform", "version": "1.0.0", "description": "منصة سندان لأعمال المحاماة - نظام إدارة مكاتب المحاماة في الكويت", "keywords": ["laravel", "vue", "legal", "law", "kuwait", "saas", "tailwind"], "author": "Sanadan Platform Team", "license": "MIT", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint resources/js --ext .vue,.js,.ts --fix", "format": "prettier --write resources/js/**/*.{vue,js,ts}", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "@inertiajs/vue3": "^1.0.14", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@vueuse/core": "^10.5.0", "axios": "^1.6.0", "chart.js": "^4.4.0", "date-fns": "^2.30.0", "date-fns-jalali": "^2.30.0-0", "flatpickr": "^4.6.13", "lodash": "^4.17.21", "pinia": "^2.1.7", "vue": "^3.3.8", "vue-chartjs": "^5.2.0", "vue-router": "^4.2.5", "vue-toastification": "^2.0.0-rc.5", "vuedraggable": "^4.1.0"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/node": "^20.9.0", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "@vitejs/plugin-vue": "^4.5.0", "autoprefixer": "^10.4.16", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-vue": "^9.18.1", "laravel-vite-plugin": "^0.8.1", "postcss": "^8.4.31", "prettier": "^3.1.0", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0", "vue-tsc": "^1.8.22"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}
import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';
import typography from '@tailwindcss/typography';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
        './resources/js/**/*.vue',
        './resources/js/**/*.js',
        './resources/js/**/*.ts',
    ],

    theme: {
        extend: {
            fontFamily: {
                sans: ['Cairo', 'Figtree', ...defaultTheme.fontFamily.sans],
                arabic: ['Cairo', 'Amiri', 'Noto Sans Arabic'],
                english: ['Inter', 'Roboto', ...defaultTheme.fontFamily.sans],
            },
            colors: {
                // ألوان المنصة الأساسية
                primary: {
                    50: '#eff6ff',
                    100: '#dbeafe',
                    200: '#bfdbfe',
                    300: '#93c5fd',
                    400: '#60a5fa',
                    500: '#3b82f6',
                    600: '#2563eb',
                    700: '#1d4ed8',
                    800: '#1e40af',
                    900: '#1e3a8a',
                    950: '#172554',
                },
                secondary: {
                    50: '#fef2f2',
                    100: '#fee2e2',
                    200: '#fecaca',
                    300: '#fca5a5',
                    400: '#f87171',
                    500: '#ef4444',
                    600: '#dc2626',
                    700: '#b91c1c',
                    800: '#991b1b',
                    900: '#7f1d1d',
                    950: '#450a0a',
                },
                success: {
                    50: '#f0fdf4',
                    100: '#dcfce7',
                    200: '#bbf7d0',
                    300: '#86efac',
                    400: '#4ade80',
                    500: '#22c55e',
                    600: '#16a34a',
                    700: '#15803d',
                    800: '#166534',
                    900: '#14532d',
                    950: '#052e16',
                },
                warning: {
                    50: '#fffbeb',
                    100: '#fef3c7',
                    200: '#fde68a',
                    300: '#fcd34d',
                    400: '#fbbf24',
                    500: '#f59e0b',
                    600: '#d97706',
                    700: '#b45309',
                    800: '#92400e',
                    900: '#78350f',
                    950: '#451a03',
                },
                info: {
                    50: '#f0f9ff',
                    100: '#e0f2fe',
                    200: '#bae6fd',
                    300: '#7dd3fc',
                    400: '#38bdf8',
                    500: '#0ea5e9',
                    600: '#0284c7',
                    700: '#0369a1',
                    800: '#075985',
                    900: '#0c4a6e',
                    950: '#082f49',
                },
                gold: {
                    50: '#fefce8',
                    100: '#fef9c3',
                    200: '#fef08a',
                    300: '#fde047',
                    400: '#facc15',
                    500: '#eab308',
                    600: '#ca8a04',
                    700: '#a16207',
                    800: '#854d0e',
                    900: '#713f12',
                    950: '#422006',
                },
                // ألوان خاصة بالحالات
                'case-open': '#3b82f6',
                'case-progress': '#f59e0b',
                'case-pending': '#6b7280',
                'case-closed': '#22c55e',
                'case-urgent': '#ef4444',
                // ألوان خاصة بالأولويات
                'priority-low': '#22c55e',
                'priority-medium': '#f59e0b',
                'priority-high': '#f97316',
                'priority-urgent': '#ef4444',
            },
            spacing: {
                '18': '4.5rem',
                '88': '22rem',
                '128': '32rem',
            },
            borderRadius: {
                '4xl': '2rem',
            },
            boxShadow: {
                'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
                'card': '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
                'dropdown': '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
            },
            animation: {
                'fade-in': 'fadeIn 0.5s ease-in-out',
                'slide-in': 'slideIn 0.3s ease-out',
                'bounce-soft': 'bounceSoft 1s infinite',
                'pulse-soft': 'pulseSoft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
            },
            keyframes: {
                fadeIn: {
                    '0%': { opacity: '0' },
                    '100%': { opacity: '1' },
                },
                slideIn: {
                    '0%': { transform: 'translateY(-10px)', opacity: '0' },
                    '100%': { transform: 'translateY(0)', opacity: '1' },
                },
                bounceSoft: {
                    '0%, 100%': {
                        transform: 'translateY(-5%)',
                        animationTimingFunction: 'cubic-bezier(0.8, 0, 1, 1)',
                    },
                    '50%': {
                        transform: 'translateY(0)',
                        animationTimingFunction: 'cubic-bezier(0, 0, 0.2, 1)',
                    },
                },
                pulseSoft: {
                    '0%, 100%': { opacity: '1' },
                    '50%': { opacity: '0.8' },
                },
            },
            typography: (theme) => ({
                DEFAULT: {
                    css: {
                        color: theme('colors.gray.700'),
                        '[class~="lead"]': {
                            color: theme('colors.gray.600'),
                        },
                        a: {
                            color: theme('colors.primary.600'),
                            '&:hover': {
                                color: theme('colors.primary.700'),
                            },
                        },
                        strong: {
                            color: theme('colors.gray.900'),
                        },
                        'h1, h2, h3, h4': {
                            color: theme('colors.gray.900'),
                        },
                    },
                },
                invert: {
                    css: {
                        color: theme('colors.gray.300'),
                        '[class~="lead"]': {
                            color: theme('colors.gray.400'),
                        },
                        a: {
                            color: theme('colors.primary.400'),
                            '&:hover': {
                                color: theme('colors.primary.300'),
                            },
                        },
                        strong: {
                            color: theme('colors.gray.100'),
                        },
                        'h1, h2, h3, h4': {
                            color: theme('colors.gray.100'),
                        },
                    },
                },
            }),
        },
    },

    plugins: [
        forms,
        typography,
        // إضافة دعم RTL
        function({ addUtilities, addComponents, theme }) {
            // إضافة فئات RTL
            addUtilities({
                '.rtl': {
                    direction: 'rtl',
                },
                '.ltr': {
                    direction: 'ltr',
                },
                '.text-start': {
                    'text-align': 'start',
                },
                '.text-end': {
                    'text-align': 'end',
                },
                '.float-start': {
                    'float': 'inline-start',
                },
                '.float-end': {
                    'float': 'inline-end',
                },
                '.ms-auto': {
                    'margin-inline-start': 'auto',
                },
                '.me-auto': {
                    'margin-inline-end': 'auto',
                },
                '.ps-0': { 'padding-inline-start': '0' },
                '.ps-1': { 'padding-inline-start': theme('spacing.1') },
                '.ps-2': { 'padding-inline-start': theme('spacing.2') },
                '.ps-3': { 'padding-inline-start': theme('spacing.3') },
                '.ps-4': { 'padding-inline-start': theme('spacing.4') },
                '.ps-5': { 'padding-inline-start': theme('spacing.5') },
                '.ps-6': { 'padding-inline-start': theme('spacing.6') },
                '.ps-8': { 'padding-inline-start': theme('spacing.8') },
                '.pe-0': { 'padding-inline-end': '0' },
                '.pe-1': { 'padding-inline-end': theme('spacing.1') },
                '.pe-2': { 'padding-inline-end': theme('spacing.2') },
                '.pe-3': { 'padding-inline-end': theme('spacing.3') },
                '.pe-4': { 'padding-inline-end': theme('spacing.4') },
                '.pe-5': { 'padding-inline-end': theme('spacing.5') },
                '.pe-6': { 'padding-inline-end': theme('spacing.6') },
                '.pe-8': { 'padding-inline-end': theme('spacing.8') },
                '.ms-0': { 'margin-inline-start': '0' },
                '.ms-1': { 'margin-inline-start': theme('spacing.1') },
                '.ms-2': { 'margin-inline-start': theme('spacing.2') },
                '.ms-3': { 'margin-inline-start': theme('spacing.3') },
                '.ms-4': { 'margin-inline-start': theme('spacing.4') },
                '.ms-5': { 'margin-inline-start': theme('spacing.5') },
                '.ms-6': { 'margin-inline-start': theme('spacing.6') },
                '.ms-8': { 'margin-inline-start': theme('spacing.8') },
                '.me-0': { 'margin-inline-end': '0' },
                '.me-1': { 'margin-inline-end': theme('spacing.1') },
                '.me-2': { 'margin-inline-end': theme('spacing.2') },
                '.me-3': { 'margin-inline-end': theme('spacing.3') },
                '.me-4': { 'margin-inline-end': theme('spacing.4') },
                '.me-5': { 'margin-inline-end': theme('spacing.5') },
                '.me-6': { 'margin-inline-end': theme('spacing.6') },
                '.me-8': { 'margin-inline-end': theme('spacing.8') },
            });

            // إضافة مكونات مخصصة
            addComponents({
                '.btn': {
                    padding: `${theme('spacing.2')} ${theme('spacing.4')}`,
                    borderRadius: theme('borderRadius.md'),
                    fontWeight: theme('fontWeight.medium'),
                    fontSize: theme('fontSize.sm'),
                    lineHeight: theme('lineHeight.5'),
                    transition: 'all 0.2s ease-in-out',
                    cursor: 'pointer',
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    '&:focus': {
                        outline: 'none',
                        boxShadow: `0 0 0 3px ${theme('colors.primary.100')}`,
                    },
                    '&:disabled': {
                        opacity: '0.5',
                        cursor: 'not-allowed',
                    },
                },
                '.btn-primary': {
                    backgroundColor: theme('colors.primary.600'),
                    color: theme('colors.white'),
                    '&:hover': {
                        backgroundColor: theme('colors.primary.700'),
                    },
                },
                '.btn-secondary': {
                    backgroundColor: theme('colors.gray.600'),
                    color: theme('colors.white'),
                    '&:hover': {
                        backgroundColor: theme('colors.gray.700'),
                    },
                },
                '.btn-success': {
                    backgroundColor: theme('colors.success.600'),
                    color: theme('colors.white'),
                    '&:hover': {
                        backgroundColor: theme('colors.success.700'),
                    },
                },
                '.btn-danger': {
                    backgroundColor: theme('colors.secondary.600'),
                    color: theme('colors.white'),
                    '&:hover': {
                        backgroundColor: theme('colors.secondary.700'),
                    },
                },
                '.btn-outline': {
                    backgroundColor: 'transparent',
                    borderWidth: '1px',
                    borderColor: theme('colors.gray.300'),
                    color: theme('colors.gray.700'),
                    '&:hover': {
                        backgroundColor: theme('colors.gray.50'),
                    },
                },
                '.card': {
                    backgroundColor: theme('colors.white'),
                    borderRadius: theme('borderRadius.lg'),
                    boxShadow: theme('boxShadow.card'),
                    padding: theme('spacing.6'),
                },
                '.form-input': {
                    borderRadius: theme('borderRadius.md'),
                    borderColor: theme('colors.gray.300'),
                    '&:focus': {
                        borderColor: theme('colors.primary.500'),
                        boxShadow: `0 0 0 3px ${theme('colors.primary.100')}`,
                    },
                },
                '.badge': {
                    display: 'inline-flex',
                    alignItems: 'center',
                    padding: `${theme('spacing.1')} ${theme('spacing.2')}`,
                    fontSize: theme('fontSize.xs'),
                    fontWeight: theme('fontWeight.medium'),
                    borderRadius: theme('borderRadius.full'),
                },
                '.badge-primary': {
                    backgroundColor: theme('colors.primary.100'),
                    color: theme('colors.primary.800'),
                },
                '.badge-success': {
                    backgroundColor: theme('colors.success.100'),
                    color: theme('colors.success.800'),
                },
                '.badge-warning': {
                    backgroundColor: theme('colors.warning.100'),
                    color: theme('colors.warning.800'),
                },
                '.badge-danger': {
                    backgroundColor: theme('colors.secondary.100'),
                    color: theme('colors.secondary.800'),
                },
            });
        },
    ],

    darkMode: 'class',
};

<template>
  <div class="min-h-screen bg-gray-50 rtl">
    <!-- شريط التنقل العلوي -->
    <Navbar 
      :user="$page.props.auth.user"
      :notifications="notifications"
      @toggle-sidebar="toggleSidebar"
      @logout="logout"
    />

    <!-- الحاوية الرئيسية -->
    <div class="flex">
      <!-- الشريط الجانبي -->
      <Sidebar 
        :is-open="sidebarOpen"
        :user="$page.props.auth.user"
        :current-route="$page.url"
        @close="closeSidebar"
      />

      <!-- المحتوى الرئيسي -->
      <main 
        class="flex-1 transition-all duration-300"
        :class="{ 'me-64': sidebarOpen, 'me-0': !sidebarOpen }"
      >
        <!-- مسار التنقل -->
        <div class="bg-white border-b border-gray-200 px-6 py-4">
          <Breadcrumb :items="breadcrumbItems" />
        </div>

        <!-- محتوى الصفحة -->
        <div class="p-6">
          <!-- رسائل التنبيه -->
          <div v-if="$page.props.flash.success" class="mb-6">
            <Alert type="success" :message="$page.props.flash.success" dismissible />
          </div>
          
          <div v-if="$page.props.flash.error" class="mb-6">
            <Alert type="error" :message="$page.props.flash.error" dismissible />
          </div>
          
          <div v-if="$page.props.flash.warning" class="mb-6">
            <Alert type="warning" :message="$page.props.flash.warning" dismissible />
          </div>
          
          <div v-if="$page.props.flash.info" class="mb-6">
            <Alert type="info" :message="$page.props.flash.info" dismissible />
          </div>

          <!-- محتوى الصفحة الفعلي -->
          <slot />
        </div>
      </main>
    </div>

    <!-- نافذة البحث السريع -->
    <QuickSearchModal 
      v-model:open="quickSearchOpen"
      @select="handleQuickSearchSelect"
    />

    <!-- نافذة الإنشاء السريع -->
    <QuickCreateModal 
      v-model:open="quickCreateOpen"
      @create="handleQuickCreate"
    />

    <!-- نافذة الإشعارات -->
    <NotificationsPanel 
      v-model:open="notificationsOpen"
      :notifications="notifications"
      @mark-as-read="markNotificationAsRead"
      @mark-all-as-read="markAllNotificationsAsRead"
    />

    <!-- مؤشر الاتصال -->
    <ConnectionIndicator />

    <!-- مؤشر التحديثات -->
    <UpdateIndicator v-if="updateAvailable" @update="applyUpdate" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { router, usePage } from '@inertiajs/vue3'
import { useToast } from 'vue-toastification'
import Navbar from '@/Components/Navbar.vue'
import Sidebar from '@/Components/Sidebar.vue'
import Breadcrumb from '@/Components/Breadcrumb.vue'
import Alert from '@/Components/Alert.vue'
import QuickSearchModal from '@/Components/QuickSearchModal.vue'
import QuickCreateModal from '@/Components/QuickCreateModal.vue'
import NotificationsPanel from '@/Components/NotificationsPanel.vue'
import ConnectionIndicator from '@/Components/ConnectionIndicator.vue'
import UpdateIndicator from '@/Components/UpdateIndicator.vue'
import { useNotifications } from '@/Composables/useNotifications'
import { useKeyboardShortcuts } from '@/Composables/useKeyboardShortcuts'
import { useServiceWorker } from '@/Composables/useServiceWorker'

const page = usePage()
const toast = useToast()

// حالة الشريط الجانبي
const sidebarOpen = ref(true)

// حالة النوافذ المنبثقة
const quickSearchOpen = ref(false)
const quickCreateOpen = ref(false)
const notificationsOpen = ref(false)

// الإشعارات
const { notifications, markAsRead, markAllAsRead, fetchNotifications } = useNotifications()

// تحديثات التطبيق
const { updateAvailable, applyUpdate } = useServiceWorker()

// مسار التنقل
const breadcrumbItems = computed(() => {
  const url = page.url
  const segments = url.split('/').filter(segment => segment)
  
  const items = [
    { label: 'الرئيسية', href: '/dashboard' }
  ]
  
  // إضافة المسارات حسب الصفحة الحالية
  if (segments.includes('cases')) {
    items.push({ label: 'القضايا', href: '/cases' })
    if (segments.includes('create')) {
      items.push({ label: 'إنشاء قضية جديدة' })
    } else if (segments.length > 1) {
      items.push({ label: 'تفاصيل القضية' })
    }
  } else if (segments.includes('clients')) {
    items.push({ label: 'العملاء', href: '/clients' })
    if (segments.includes('create')) {
      items.push({ label: 'إضافة عميل جديد' })
    } else if (segments.length > 1) {
      items.push({ label: 'ملف العميل' })
    }
  } else if (segments.includes('hearings')) {
    items.push({ label: 'الجلسات', href: '/hearings' })
  } else if (segments.includes('tasks')) {
    items.push({ label: 'المهام', href: '/tasks' })
  } else if (segments.includes('documents')) {
    items.push({ label: 'المستندات', href: '/documents' })
  } else if (segments.includes('invoices')) {
    items.push({ label: 'الفواتير', href: '/invoices' })
  } else if (segments.includes('reports')) {
    items.push({ label: 'التقارير', href: '/reports' })
  } else if (segments.includes('settings')) {
    items.push({ label: 'الإعدادات', href: '/settings' })
  }
  
  return items
})

// وظائف الشريط الجانبي
const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
  localStorage.setItem('sidebarOpen', sidebarOpen.value.toString())
}

const closeSidebar = () => {
  sidebarOpen.value = false
}

// تسجيل الخروج
const logout = () => {
  router.post('/logout')
}

// البحث السريع
const handleQuickSearchSelect = (item) => {
  router.visit(item.url)
  quickSearchOpen.value = false
}

// الإنشاء السريع
const handleQuickCreate = (type) => {
  const routes = {
    case: '/cases/create',
    client: '/clients/create',
    task: '/tasks/create',
    hearing: '/hearings/create',
    invoice: '/invoices/create'
  }
  
  if (routes[type]) {
    router.visit(routes[type])
  }
  
  quickCreateOpen.value = false
}

// إدارة الإشعارات
const markNotificationAsRead = async (notificationId) => {
  try {
    await markAsRead(notificationId)
    toast.success('تم تحديد الإشعار كمقروء')
  } catch (error) {
    toast.error('حدث خطأ أثناء تحديث الإشعار')
  }
}

const markAllNotificationsAsRead = async () => {
  try {
    await markAllAsRead()
    toast.success('تم تحديد جميع الإشعارات كمقروءة')
  } catch (error) {
    toast.error('حدث خطأ أثناء تحديث الإشعارات')
  }
}

// اختصارات لوحة المفاتيح
useKeyboardShortcuts({
  'ctrl+k': () => { quickSearchOpen.value = true },
  'ctrl+n': () => { quickCreateOpen.value = true },
  'ctrl+shift+n': () => { notificationsOpen.value = true },
  'escape': () => {
    quickSearchOpen.value = false
    quickCreateOpen.value = false
    notificationsOpen.value = false
  }
})

// معالجة الأحداث المخصصة
const handleCustomEvents = () => {
  document.addEventListener('open-quick-search', () => {
    quickSearchOpen.value = true
  })
  
  document.addEventListener('open-quick-create', () => {
    quickCreateOpen.value = true
  })
  
  document.addEventListener('close-modals', () => {
    quickSearchOpen.value = false
    quickCreateOpen.value = false
    notificationsOpen.value = false
  })
}

// تحميل البيانات الأولية
onMounted(() => {
  // استرجاع حالة الشريط الجانبي
  const savedSidebarState = localStorage.getItem('sidebarOpen')
  if (savedSidebarState !== null) {
    sidebarOpen.value = savedSidebarState === 'true'
  }
  
  // تحميل الإشعارات
  fetchNotifications()
  
  // إعداد معالجة الأحداث
  handleCustomEvents()
  
  // إعداد تحديث الإشعارات كل دقيقة
  const notificationInterval = setInterval(fetchNotifications, 60000)
  
  // تنظيف المؤقت عند إلغاء تحميل المكون
  onUnmounted(() => {
    clearInterval(notificationInterval)
  })
})

// مراقبة تغييرات الصفحة لتحديث العنوان
watch(() => page.props.title, (newTitle) => {
  if (newTitle) {
    document.title = `${newTitle} - منصة سندان`
  }
}, { immediate: true })

// مراقبة رسائل Flash لعرض التنبيهات
watch(() => page.props.flash, (flash) => {
  if (flash.success) {
    toast.success(flash.success)
  }
  if (flash.error) {
    toast.error(flash.error)
  }
  if (flash.warning) {
    toast.warning(flash.warning)
  }
  if (flash.info) {
    toast.info(flash.info)
  }
}, { deep: true })
</script>

<style scoped>
/* تحسينات CSS إضافية */
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* تحسين عرض الشريط الجانبي على الشاشات الصغيرة */
@media (max-width: 768px) {
  .me-64 {
    margin-inline-end: 0 !important;
  }
}

/* تحسين الأداء للرسوم المتحركة */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

/* إخفاء شريط التمرير في بعض المتصفحات */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
</style>

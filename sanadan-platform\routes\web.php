<?php

use App\Http\Controllers\DashboardController;
use App\Http\Controllers\LegalCaseController;
use App\Http\Controllers\ClientController;
use App\Http\Controllers\HearingController;
use App\Http\Controllers\TaskController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\InvoiceController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\CalendarController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

// الصفحة الرئيسية العامة
Route::get('/', function () {
    return view('welcome');
})->name('home');

// صفحات المصادقة
Route::middleware('guest')->group(function () {
    Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);
    Route::get('/register', [AuthController::class, 'showRegistrationForm'])->name('register');
    Route::post('/register', [AuthController::class, 'register']);
    Route::get('/forgot-password', [AuthController::class, 'showForgotPasswordForm'])->name('password.request');
    Route::post('/forgot-password', [AuthController::class, 'sendResetLinkEmail'])->name('password.email');
    Route::get('/reset-password/{token}', [AuthController::class, 'showResetPasswordForm'])->name('password.reset');
    Route::post('/reset-password', [AuthController::class, 'resetPassword'])->name('password.update');
});

// المسارات المحمية بالمصادقة
Route::middleware(['auth', 'verified'])->group(function () {
    
    // تسجيل الخروج
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');
    
    // لوحة التحكم الرئيسية
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/stats', [DashboardController::class, 'getStats'])->name('dashboard.stats');
    
    // إدارة الملف الشخصي
    Route::prefix('profile')->name('profile.')->group(function () {
        Route::get('/', [ProfileController::class, 'show'])->name('show');
        Route::get('/edit', [ProfileController::class, 'edit'])->name('edit');
        Route::patch('/', [ProfileController::class, 'update'])->name('update');
        Route::delete('/', [ProfileController::class, 'destroy'])->name('destroy');
        Route::post('/avatar', [ProfileController::class, 'updateAvatar'])->name('avatar.update');
        Route::post('/password', [ProfileController::class, 'updatePassword'])->name('password.update');
        Route::post('/two-factor', [ProfileController::class, 'enableTwoFactor'])->name('two-factor.enable');
        Route::delete('/two-factor', [ProfileController::class, 'disableTwoFactor'])->name('two-factor.disable');
    });
    
    // إدارة القضايا
    Route::resource('cases', LegalCaseController::class)->names([
        'index' => 'cases.index',
        'create' => 'cases.create',
        'store' => 'cases.store',
        'show' => 'cases.show',
        'edit' => 'cases.edit',
        'update' => 'cases.update',
        'destroy' => 'cases.destroy',
    ]);
    
    // مسارات إضافية للقضايا
    Route::prefix('cases')->name('cases.')->group(function () {
        Route::post('/{case}/close', [LegalCaseController::class, 'close'])->name('close');
        Route::post('/{case}/reopen', [LegalCaseController::class, 'reopen'])->name('reopen');
        Route::post('/{case}/parties', [LegalCaseController::class, 'addParty'])->name('parties.add');
        Route::post('/{case}/references', [LegalCaseController::class, 'addLegalReference'])->name('references.add');
        Route::post('/{case}/update-hearing', [LegalCaseController::class, 'updateNextHearing'])->name('hearing.update');
        Route::get('/search', [LegalCaseController::class, 'quickSearch'])->name('search');
        Route::get('/stats', [LegalCaseController::class, 'getStats'])->name('stats');
        Route::post('/export', [LegalCaseController::class, 'export'])->name('export');
    });
    
    // إدارة العملاء
    Route::resource('clients', ClientController::class);
    
    // مسارات إضافية للعملاء
    Route::prefix('clients')->name('clients.')->group(function () {
        Route::post('/{client}/blacklist', [ClientController::class, 'blacklist'])->name('blacklist');
        Route::post('/{client}/remove-blacklist', [ClientController::class, 'removeFromBlacklist'])->name('remove-blacklist');
        Route::delete('/{client}/documents/{media}', [ClientController::class, 'deleteDocument'])->name('documents.delete');
        Route::post('/{client}/additional-info', [ClientController::class, 'updateAdditionalInfo'])->name('additional-info.update');
        Route::post('/{client}/contact-preferences', [ClientController::class, 'updateContactPreference'])->name('contact-preferences.update');
        Route::get('/{client}/print', [ClientController::class, 'printProfile'])->name('print');
        Route::get('/{client}/create-case', [ClientController::class, 'createCase'])->name('create-case');
        Route::get('/{client}/create-invoice', [ClientController::class, 'createInvoice'])->name('create-invoice');
        Route::get('/search', [ClientController::class, 'quickSearch'])->name('search');
        Route::get('/stats', [ClientController::class, 'getStats'])->name('stats');
        Route::post('/export', [ClientController::class, 'export'])->name('export');
    });
    
    // إدارة الجلسات
    Route::resource('hearings', HearingController::class);
    Route::prefix('hearings')->name('hearings.')->group(function () {
        Route::post('/{hearing}/postpone', [HearingController::class, 'postpone'])->name('postpone');
        Route::post('/{hearing}/cancel', [HearingController::class, 'cancel'])->name('cancel');
        Route::post('/{hearing}/complete', [HearingController::class, 'complete'])->name('complete');
        Route::get('/calendar', [HearingController::class, 'calendar'])->name('calendar');
        Route::post('/export', [HearingController::class, 'export'])->name('export');
    });
    
    // إدارة المهام
    Route::resource('tasks', TaskController::class);
    Route::prefix('tasks')->name('tasks.')->group(function () {
        Route::post('/{task}/complete', [TaskController::class, 'complete'])->name('complete');
        Route::post('/{task}/assign', [TaskController::class, 'assign'])->name('assign');
        Route::get('/my-tasks', [TaskController::class, 'myTasks'])->name('my-tasks');
        Route::post('/bulk-update', [TaskController::class, 'bulkUpdate'])->name('bulk-update');
    });
    
    // إدارة المستندات
    Route::resource('documents', DocumentController::class);
    Route::prefix('documents')->name('documents.')->group(function () {
        Route::get('/{document}/download', [DocumentController::class, 'download'])->name('download');
        Route::get('/{document}/preview', [DocumentController::class, 'preview'])->name('preview');
        Route::post('/{document}/sign', [DocumentController::class, 'sign'])->name('sign');
        Route::post('/bulk-upload', [DocumentController::class, 'bulkUpload'])->name('bulk-upload');
        Route::get('/templates', [DocumentController::class, 'templates'])->name('templates');
        Route::post('/generate', [DocumentController::class, 'generate'])->name('generate');
    });
    
    // إدارة الفواتير
    Route::resource('invoices', InvoiceController::class);
    Route::prefix('invoices')->name('invoices.')->group(function () {
        Route::post('/{invoice}/send', [InvoiceController::class, 'send'])->name('send');
        Route::get('/{invoice}/pdf', [InvoiceController::class, 'pdf'])->name('pdf');
        Route::post('/{invoice}/duplicate', [InvoiceController::class, 'duplicate'])->name('duplicate');
        Route::post('/{invoice}/cancel', [InvoiceController::class, 'cancel'])->name('cancel');
        Route::get('/overdue', [InvoiceController::class, 'overdue'])->name('overdue');
        Route::post('/bulk-send', [InvoiceController::class, 'bulkSend'])->name('bulk-send');
    });
    
    // إدارة المدفوعات
    Route::resource('payments', PaymentController::class);
    Route::prefix('payments')->name('payments.')->group(function () {
        Route::post('/{payment}/refund', [PaymentController::class, 'refund'])->name('refund');
        Route::get('/gateways', [PaymentController::class, 'gateways'])->name('gateways');
        Route::post('/process', [PaymentController::class, 'process'])->name('process');
        Route::get('/success', [PaymentController::class, 'success'])->name('success');
        Route::get('/cancel', [PaymentController::class, 'cancel'])->name('cancel');
    });
    
    // التقويم
    Route::prefix('calendar')->name('calendar.')->group(function () {
        Route::get('/', [CalendarController::class, 'index'])->name('index');
        Route::get('/events', [CalendarController::class, 'events'])->name('events');
        Route::post('/events', [CalendarController::class, 'store'])->name('events.store');
        Route::put('/events/{event}', [CalendarController::class, 'update'])->name('events.update');
        Route::delete('/events/{event}', [CalendarController::class, 'destroy'])->name('events.destroy');
    });
    
    // التقارير
    Route::prefix('reports')->name('reports.')->group(function () {
        Route::get('/', [ReportController::class, 'index'])->name('index');
        Route::get('/cases', [ReportController::class, 'cases'])->name('cases');
        Route::get('/financial', [ReportController::class, 'financial'])->name('financial');
        Route::get('/performance', [ReportController::class, 'performance'])->name('performance');
        Route::get('/clients', [ReportController::class, 'clients'])->name('clients');
        Route::post('/generate', [ReportController::class, 'generate'])->name('generate');
        Route::get('/export/{type}', [ReportController::class, 'export'])->name('export');
    });
    
    // إدارة المستخدمين (للمديرين فقط)
    Route::middleware('can:manage_users')->group(function () {
        Route::resource('users', UserController::class);
        Route::prefix('users')->name('users.')->group(function () {
            Route::post('/{user}/suspend', [UserController::class, 'suspend'])->name('suspend');
            Route::post('/{user}/reactivate', [UserController::class, 'reactivate'])->name('reactivate');
            Route::post('/{user}/reset-password', [UserController::class, 'resetPassword'])->name('reset-password');
            Route::get('/roles', [UserController::class, 'roles'])->name('roles');
            Route::post('/roles', [UserController::class, 'storeRole'])->name('roles.store');
            Route::put('/roles/{role}', [UserController::class, 'updateRole'])->name('roles.update');
            Route::delete('/roles/{role}', [UserController::class, 'destroyRole'])->name('roles.destroy');
        });
    });
    
    // الإعدادات (للمديرين فقط)
    Route::middleware('can:manage_settings')->group(function () {
        Route::prefix('settings')->name('settings.')->group(function () {
            Route::get('/', [SettingsController::class, 'index'])->name('index');
            Route::get('/general', [SettingsController::class, 'general'])->name('general');
            Route::post('/general', [SettingsController::class, 'updateGeneral'])->name('general.update');
            Route::get('/subscription', [SettingsController::class, 'subscription'])->name('subscription');
            Route::post('/subscription', [SettingsController::class, 'updateSubscription'])->name('subscription.update');
            Route::get('/integrations', [SettingsController::class, 'integrations'])->name('integrations');
            Route::post('/integrations', [SettingsController::class, 'updateIntegrations'])->name('integrations.update');
            Route::get('/backup', [SettingsController::class, 'backup'])->name('backup');
            Route::post('/backup/create', [SettingsController::class, 'createBackup'])->name('backup.create');
            Route::get('/backup/download/{file}', [SettingsController::class, 'downloadBackup'])->name('backup.download');
        });
    });
});

// مسارات API للتطبيقات الخارجية
Route::prefix('api/v1')->middleware(['auth:sanctum'])->group(function () {
    Route::apiResource('cases', 'Api\LegalCaseController');
    Route::apiResource('clients', 'Api\ClientController');
    Route::apiResource('hearings', 'Api\HearingController');
    Route::apiResource('tasks', 'Api\TaskController');
    Route::apiResource('documents', 'Api\DocumentController');
    Route::apiResource('invoices', 'Api\InvoiceController');
    Route::apiResource('payments', 'Api\PaymentController');
    
    // إحصائيات API
    Route::get('stats/dashboard', 'Api\StatsController@dashboard');
    Route::get('stats/cases', 'Api\StatsController@cases');
    Route::get('stats/financial', 'Api\StatsController@financial');
});

// مسارات الدفع العامة (بدون مصادقة)
Route::prefix('payment')->name('payment.')->group(function () {
    Route::get('/invoice/{invoice}/pay', [PaymentController::class, 'showPaymentForm'])->name('form');
    Route::post('/process/{invoice}', [PaymentController::class, 'processPayment'])->name('process');
    Route::get('/callback/{gateway}', [PaymentController::class, 'callback'])->name('callback');
    Route::post('/webhook/{gateway}', [PaymentController::class, 'webhook'])->name('webhook');
});

// مسارات عامة للعملاء (عرض الفواتير والمستندات)
Route::prefix('client-portal')->name('client.')->group(function () {
    Route::get('/invoice/{invoice}', [InvoiceController::class, 'clientView'])->name('invoice.view');
    Route::get('/document/{document}', [DocumentController::class, 'clientView'])->name('document.view');
    Route::get('/case/{case}', [LegalCaseController::class, 'clientView'])->name('case.view');
});

# منصة سندان - تطبيق جوال لإدارة مكاتب المحاماة

<div align="center">
  <img src="./assets/icon.png" alt="منصة سندان" width="120" height="120">
  
  <h3>نظام إدارة مكاتب المحاماة الأكثر تطوراً في الكويت والمنطقة العربية</h3>
  
  [![React Native](https://img.shields.io/badge/React%20Native-0.73-blue.svg)](https://reactnative.dev/)
  [![Expo](https://img.shields.io/badge/Expo-50.0-black.svg)](https://expo.dev/)
  [![TypeScript](https://img.shields.io/badge/TypeScript-5.3-blue.svg)](https://www.typescriptlang.org/)
  [![License](https://img.shields.io/badge/License-MIT-green.svg)](./LICENSE)
</div>

## 📱 نظرة عامة

منصة سندان هي تطبيق جوال متطور مصمم خصيصاً لإدارة مكاتب المحاماة في دولة الكويت والمنطقة العربية. يوفر التطبيق حلولاً شاملة لإدارة القضايا، العملاء، الجلسات، المستندات، والنظام المالي بطريقة احترافية ومتطورة.

## ✨ الميزات الرئيسية

### 🏛️ إدارة القضايا
- **تتبع شامل للقضايا** مع إدارة الحالات والأولويات
- **جدولة الجلسات** مع تنبيهات ذكية
- **إدارة المستندات** مع تصنيف وأرشفة متقدمة
- **تتبع الوقت والمهام** لكل قضية
- **تقارير مفصلة** عن تقدم القضايا

### 👥 إدارة العملاء
- **ملفات شاملة للعملاء** مع جميع البيانات الضرورية
- **تتبع التفاعلات والمراسلات** مع العملاء
- **إدارة العقود والاتفاقيات**
- **نظام تقييم العملاء** والملاحظات

### 💰 النظام المالي
- **إدارة الفواتير والمدفوعات** مع دعم العملات المتعددة
- **تكامل مع بوابات الدفع الكويتية** (K-Net)
- **تقارير مالية مفصلة** وتحليلات الإيرادات
- **إدارة المصروفات** وتتبع التكاليف

### 📅 إدارة المواعيد والجلسات
- **تقويم تفاعلي** مع عرض شامل للمواعيد
- **تنبيهات ذكية** للجلسات والمهام المهمة
- **تزامن مع التقويمات الخارجية** (Google Calendar, Outlook)
- **إدارة التعارضات الزمنية**

### 📄 إدارة المستندات
- **تخزين آمن ومنظم** لجميع المستندات
- **بحث متقدم** في المحتوى والبيانات الوصفية
- **توقيع إلكتروني** للمستندات
- **قوالب مستندات قانونية** جاهزة للاستخدام

### 🤖 الذكاء الاصطناعي
- **مساعد قانوني ذكي** للإجابة على الاستفسارات
- **إنشاء المستندات تلقائياً** باستخدام الذكاء الاصطناعي
- **تحليل البيانات الذكي** وتوقع النتائج
- **اقتراحات تحسين الإنتاجية**

## 🛠️ التقنيات المستخدمة

### Frontend
- **React Native 0.73** - إطار العمل الأساسي
- **Expo 50.0** - منصة التطوير والنشر
- **TypeScript 5.3** - لغة البرمجة المكتوبة
- **React Navigation 6** - نظام التنقل
- **React Native Paper** - مكتبة واجهة المستخدم
- **React Query** - إدارة حالة الخادم
- **Formik & Yup** - إدارة النماذج والتحقق

### Backend Integration
- **Axios** - عميل HTTP
- **AsyncStorage** - التخزين المحلي
- **Expo SecureStore** - التخزين الآمن
- **React Native MMKV** - تخزين عالي الأداء

### UI/UX
- **Material Design 3** - نظام التصميم
- **Tailwind CSS** - إطار عمل CSS (مخصص)
- **React Native Vector Icons** - الأيقونات
- **React Native Animatable** - الرسوم المتحركة
- **React Native Reanimated** - رسوم متحركة متقدمة

### الميزات المتقدمة
- **Expo Notifications** - الإشعارات المحلية والبعيدة
- **Expo Camera** - التقاط الصور والمستندات
- **Expo Document Picker** - اختيار الملفات
- **React Native Biometrics** - المصادقة البيومترية
- **React Native Maps** - الخرائط والموقع
- **React Native PDF** - عرض ملفات PDF

## 🚀 البدء السريع

### المتطلبات الأساسية
- Node.js 18+ 
- npm أو yarn
- Expo CLI
- Android Studio (للتطوير على Android)
- Xcode (للتطوير على iOS)

### التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/sanadan-platform/mobile-app.git
cd sanadan-mobile-app
```

2. **تثبيت التبعيات**
```bash
npm install
# أو
yarn install
```

3. **إعداد متغيرات البيئة**
```bash
cp .env.example .env
# قم بتحرير ملف .env وإضافة القيم المطلوبة
```

4. **تشغيل التطبيق**
```bash
# تشغيل على محاكي/جهاز Android
npm run android

# تشغيل على محاكي/جهاز iOS
npm run ios

# تشغيل على المتصفح
npm run web

# تشغيل خادم التطوير
npm start
```

## 📁 هيكل المشروع

```
sanadan-mobile-app/
├── src/
│   ├── components/          # المكونات القابلة لإعادة الاستخدام
│   ├── screens/            # شاشات التطبيق
│   ├── navigation/         # إعدادات التنقل
│   ├── services/           # خدمات API والبيانات
│   ├── contexts/           # سياقات React
│   ├── hooks/              # خطافات مخصصة
│   ├── utils/              # دوال مساعدة
│   ├── types/              # تعريفات TypeScript
│   ├── theme/              # إعدادات التصميم والألوان
│   ├── config/             # إعدادات التطبيق
│   └── assets/             # الصور والخطوط
├── assets/                 # أصول Expo
├── docs/                   # الوثائق
└── tests/                  # الاختبارات
```

## 🔧 إعدادات التطوير

### متغيرات البيئة
```env
# API Configuration
API_BASE_URL=https://api.sanadan.com/v1
API_TIMEOUT=30000

# Authentication
AUTH_DOMAIN=sanadan.auth0.com
AUTH_CLIENT_ID=your_client_id

# Payment Gateways
KNET_MERCHANT_ID=your_merchant_id
STRIPE_PUBLISHABLE_KEY=pk_test_...

# Analytics
ANALYTICS_API_KEY=your_analytics_key
CRASHLYTICS_ENABLED=true

# Features
ENABLE_AI_ASSISTANT=true
ENABLE_BIOMETRIC_AUTH=true
ENABLE_OFFLINE_MODE=true
```

### أوامر البناء

```bash
# بناء للإنتاج
npm run build

# بناء Android APK
npm run build:android

# بناء iOS IPA
npm run build:ios

# تشغيل الاختبارات
npm test

# فحص الكود
npm run lint

# تنسيق الكود
npm run format
```

## 📱 الشاشات الرئيسية

### 🏠 لوحة التحكم
- إحصائيات شاملة للمكتب
- الجلسات والمهام القادمة
- تقارير الأداء المالي
- إشعارات مهمة

### ⚖️ إدارة القضايا
- قائمة القضايا مع فلترة وبحث
- تفاصيل القضية الشاملة
- إضافة وتعديل القضايا
- تتبع تقدم القضايا

### 👤 إدارة العملاء
- دليل العملاء الشامل
- ملف العميل التفصيلي
- تاريخ التعاملات
- إضافة عملاء جدد

### 📅 التقويم والجلسات
- عرض تقويمي للجلسات
- تفاصيل الجلسات
- جدولة جلسات جديدة
- تنبيهات الجلسات

### 📄 المستندات
- مكتبة المستندات المنظمة
- عارض المستندات المتقدم
- رفع وتصنيف المستندات
- البحث في المحتوى

### 💳 الفواتير والمدفوعات
- إدارة الفواتير
- تتبع المدفوعات
- تقارير مالية
- ربط بوابات الدفع

## 🔐 الأمان والخصوصية

### ميزات الأمان
- **تشفير البيانات** من النهاية إلى النهاية
- **مصادقة ثنائية العوامل** (2FA)
- **مصادقة بيومترية** (بصمة الإصبع / Face ID)
- **حماية من لقطات الشاشة** في البيانات الحساسة
- **كشف الأجهزة المخترقة** (Root/Jailbreak)

### الخصوصية
- **امتثال لقوانين حماية البيانات** الكويتية والدولية
- **تشفير البيانات المحلية** والسحابية
- **سياسة خصوصية شفافة**
- **تحكم المستخدم في البيانات**

## 🌐 الدعم متعدد اللغات

### اللغات المدعومة
- **العربية** (اللغة الأساسية) - RTL
- **الإنجليزية** - LTR

### الميزات اللغوية
- **واجهة مستخدم مترجمة بالكامل**
- **دعم RTL كامل** للعربية
- **خطوط عربية احترافية** (Cairo, Amiri)
- **تنسيق التواريخ والأرقام** حسب اللغة
- **رسائل الخطأ والتنبيهات** مترجمة

## 📊 التحليلات والتقارير

### أنواع التقارير
- **تقارير مالية** شهرية وسنوية
- **تقارير أداء المحامين**
- **إحصائيات القضايا** والنتائج
- **تقارير الوقت المستغرق**
- **تحليل رضا العملاء**

### التصدير
- **PDF** للتقارير الرسمية
- **Excel** للتحليل المتقدم
- **CSV** للبيانات الخام

## 🔄 التزامن والعمل دون اتصال

### العمل دون اتصال
- **تخزين محلي** للبيانات المهمة
- **مزامنة تلقائية** عند استعادة الاتصال
- **إشعارات محلية** للمهام والجلسات
- **عرض البيانات المحفوظة** دون اتصال

### التزامن
- **مزامنة في الوقت الفعلي** للبيانات المهمة
- **حل التعارضات** التلقائي
- **نسخ احتياطية** دورية
- **استعادة البيانات** في حالة الفقدان

## 🧪 الاختبارات

### أنواع الاختبارات
- **اختبارات الوحدة** (Unit Tests)
- **اختبارات التكامل** (Integration Tests)
- **اختبارات واجهة المستخدم** (E2E Tests)
- **اختبارات الأداء** (Performance Tests)

### تشغيل الاختبارات
```bash
# جميع الاختبارات
npm test

# اختبارات الوحدة
npm run test:unit

# اختبارات التكامل
npm run test:integration

# اختبارات E2E
npm run test:e2e

# تغطية الكود
npm run test:coverage
```

## 📦 النشر والتوزيع

### متاجر التطبيقات
- **Google Play Store** (Android)
- **Apple App Store** (iOS)
- **Huawei AppGallery** (Android)

### التوزيع المؤسسي
- **Enterprise Distribution** للمؤسسات الكبيرة
- **TestFlight** للاختبار التجريبي (iOS)
- **Firebase App Distribution** للاختبار (Android)

## 🤝 المساهمة

نرحب بمساهماتكم في تطوير منصة سندان! يرجى قراءة [دليل المساهمة](./CONTRIBUTING.md) للمزيد من التفاصيل.

### خطوات المساهمة
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](./LICENSE) للتفاصيل.

## 📞 الدعم والتواصل

### معلومات التواصل
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +965-2222-3333
- **الموقع الإلكتروني**: https://sanadan.com
- **وثائق API**: https://docs.sanadan.com

### الدعم الفني
- **ساعات العمل**: الأحد - الخميس، 8:00 ص - 6:00 م (توقيت الكويت)
- **الدعم العاجل**: متاح 24/7 للعملاء المؤسسيين
- **قاعدة المعرفة**: https://help.sanadan.com

---

<div align="center">
  <p>صُنع بـ ❤️ في الكويت لخدمة المجتمع القانوني العربي</p>
  <p>© 2024 منصة سندان. جميع الحقوق محفوظة.</p>
</div>

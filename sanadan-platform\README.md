# منصة سندان لأعمال المحاماة

<div align="center">
  <img src="public/images/logo.svg" alt="منصة سندان" width="200">
  
  <h3>نظام إدارة مكاتب المحاماة الأكثر تطوراً في الكويت والمنطقة العربية</h3>
  
  [![Lara<PERSON>](https://img.shields.io/badge/Laravel-11.x-red.svg)](https://laravel.com)
  [![Vue.js](https://img.shields.io/badge/Vue.js-3.x-green.svg)](https://vuejs.org)
  [![PHP](https://img.shields.io/badge/PHP-8.3+-blue.svg)](https://php.net)
  [![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
</div>

## 📋 نظرة عامة

منصة سندان هي حل SaaS متكامل ومتطور لإدارة مكاتب المحاماة، مصممة خصيصاً للبيئة القانونية في دولة الكويت والمنطقة العربية. تقدم المنصة مجموعة شاملة من الأدوات والميزات التي تساعد المحامين وموظفي مكاتب المحاماة على إدارة أعمالهم بكفاءة عالية.

## ✨ الميزات الرئيسية

### 🏛️ إدارة القضايا
- **تتبع شامل للقضايا**: إدارة كاملة لدورة حياة القضية من البداية حتى النهاية
- **تصنيف متقدم**: تصنيف القضايا حسب النوع والأولوية والحالة
- **ربط الأطراف**: إدارة جميع أطراف القضية والمراجع القانونية
- **تتبع المواعيد**: تذكيرات تلقائية للجلسات والمواعيد المهمة

### 👥 إدارة العملاء
- **ملفات شاملة**: ملفات تفصيلية للعملاء مع جميع البيانات الضرورية
- **تصنيف متنوع**: دعم العملاء الأفراد والشركات والجهات الحكومية
- **تتبع التفاعل**: سجل كامل لجميع التفاعلات والمراسلات
- **إدارة المستندات**: ربط وتنظيم جميع مستندات العميل

### 📅 إدارة الجلسات والمواعيد
- **تقويم تفاعلي**: تقويم شامل لجميع الجلسات والمواعيد
- **تنبيهات ذكية**: إشعارات تلقائية قبل الجلسات
- **إدارة المحاكم**: قاعدة بيانات شاملة للمحاكم والدوائر
- **تتبع النتائج**: تسجيل نتائج الجلسات والقرارات

### 📋 إدارة المهام
- **تخصيص المهام**: توزيع المهام على أعضاء الفريق
- **تتبع التقدم**: مراقبة حالة إنجاز المهام
- **أولويات ذكية**: تصنيف المهام حسب الأولوية والاستعجال
- **تقارير الإنتاجية**: تقارير مفصلة عن أداء الفريق

### 📄 إدارة المستندات
- **تخزين آمن**: تخزين سحابي آمن لجميع المستندات
- **تصنيف ذكي**: تصنيف تلقائي للمستندات حسب النوع والقضية
- **بحث متقدم**: محرك بحث قوي للعثور على المستندات بسرعة
- **توقيع إلكتروني**: إمكانية التوقيع الإلكتروني للمستندات

### 💰 النظام المالي
- **إدارة الفواتير**: إنشاء وإرسال الفواتير بسهولة
- **تتبع المدفوعات**: مراقبة حالة المدفوعات والمتأخرات
- **تكامل بوابات الدفع**: دعم K-Net وStripe وPayPal
- **تقارير مالية**: تقارير مفصلة عن الإيرادات والمصروفات

### 📊 التقارير والتحليلات
- **لوحة تحكم تفاعلية**: إحصائيات مباشرة وتحليلات شاملة
- **تقارير مخصصة**: إنشاء تقارير مخصصة حسب الحاجة
- **رسوم بيانية**: عرض البيانات بصرياً بطريقة واضحة
- **تصدير البيانات**: تصدير التقارير بصيغ مختلفة

## 🛠️ التقنيات المستخدمة

### Backend
- **Laravel 11**: إطار عمل PHP الحديث والقوي
- **PHP 8.3+**: أحدث إصدار من PHP مع تحسينات الأداء
- **MySQL 8.0**: قاعدة بيانات موثوقة وسريعة
- **Redis**: للتخزين المؤقت والجلسات
- **Laravel Sanctum**: للمصادقة والتفويض

### Frontend
- **Vue.js 3**: إطار عمل JavaScript تفاعلي
- **TypeScript**: للكتابة الآمنة والتطوير المتقدم
- **Tailwind CSS**: للتصميم السريع والمتجاوب
- **Inertia.js**: لربط Laravel مع Vue.js بسلاسة

### الحزم والمكتبات
- **Stancl/Tenancy**: لدعم Multi-tenancy
- **Spatie Packages**: للصلاحيات وتسجيل النشاط
- **Laravel Scout**: للبحث المتقدم
- **DomPDF**: لإنشاء ملفات PDF
- **Laravel Excel**: لاستيراد وتصدير البيانات

## 🚀 متطلبات التشغيل

### متطلبات الخادم
- PHP 8.3 أو أحدث
- Composer 2.0+
- Node.js 18.0+ و npm 8.0+
- MySQL 8.0 أو MariaDB 10.4+
- Redis 7.0+
- Nginx أو Apache

### متطلبات التطوير
- Git
- VS Code أو PHPStorm
- Postman للاختبار
- Docker (اختياري)

## 📦 التثبيت والإعداد

### 1. استنساخ المشروع
```bash
git clone https://github.com/sanadan-platform/sanadan-legal-platform.git
cd sanadan-platform
```

### 2. تثبيت التبعيات
```bash
# تثبيت تبعيات PHP
composer install

# تثبيت تبعيات JavaScript
npm install
```

### 3. إعداد البيئة
```bash
# نسخ ملف البيئة
cp .env.example .env

# إنشاء مفتاح التطبيق
php artisan key:generate
```

### 4. إعداد قاعدة البيانات
```bash
# تحديث ملف .env بمعلومات قاعدة البيانات
# ثم تشغيل الهجرات
php artisan migrate

# تشغيل البذور (البيانات الأولية)
php artisan db:seed
```

### 5. إعداد التخزين
```bash
# ربط مجلد التخزين العام
php artisan storage:link

# إعداد صلاحيات المجلدات
chmod -R 775 storage bootstrap/cache
```

### 6. بناء الأصول
```bash
# للتطوير
npm run dev

# للإنتاج
npm run build
```

### 7. تشغيل الخادم
```bash
# تشغيل خادم Laravel
php artisan serve

# في نافذة طرفية أخرى، تشغيل Vite
npm run dev
```

## 🔧 الإعداد المتقدم

### إعداد Multi-tenancy
```bash
# إنشاء مستأجر جديد
php artisan tenants:create example.sanadan.com

# تشغيل هجرات المستأجر
php artisan tenants:migrate
```

### إعداد البحث
```bash
# إعداد Meilisearch
php artisan scout:import "App\Models\LegalCase"
php artisan scout:import "App\Models\Client"
```

### إعداد المهام المجدولة
```bash
# إضافة إلى crontab
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

### إعداد Queue Workers
```bash
# تشغيل معالج المهام
php artisan queue:work --sleep=3 --tries=3
```

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# جميع الاختبارات
php artisan test

# اختبارات محددة
php artisan test --filter=CaseTest

# اختبارات مع تغطية الكود
php artisan test --coverage
```

### اختبارات JavaScript
```bash
# تشغيل اختبارات Vue.js
npm run test

# اختبارات مع المراقبة
npm run test:watch
```

## 📚 الوثائق

### دليل المطور
- [دليل API](docs/api.md)
- [دليل قاعدة البيانات](docs/database.md)
- [دليل Frontend](docs/frontend.md)
- [دليل النشر](docs/deployment.md)

### دليل المستخدم
- [دليل البداية السريعة](docs/quick-start.md)
- [إدارة القضايا](docs/cases.md)
- [إدارة العملاء](docs/clients.md)
- [النظام المالي](docs/finance.md)

## 🔐 الأمان

### الميزات الأمنية
- تشفير كامل للبيانات الحساسة
- مصادقة ثنائية العوامل
- تسجيل شامل لجميع العمليات
- حماية من هجمات CSRF و XSS
- نسخ احتياطية تلقائية

### أفضل الممارسات
- تحديث دوري للتبعيات
- مراجعة دورية للصلاحيات
- مراقبة النشاط المشبوه
- تشفير الاتصالات (HTTPS)

## 🌍 الدعم والمساهمة

### الحصول على المساعدة
- [مركز المساعدة](https://help.sanadan.com)
- [منتدى المجتمع](https://community.sanadan.com)
- [البريد الإلكتروني](mailto:<EMAIL>)
- [الدردشة المباشرة](https://sanadan.com/chat)

### المساهمة في المشروع
1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

### إرشادات المساهمة
- اتباع معايير الكود المحددة
- كتابة اختبارات للميزات الجديدة
- توثيق التغييرات
- احترام قواعد المجتمع

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

نشكر جميع المساهمين والمطورين الذين ساعدوا في بناء هذه المنصة:

- فريق Laravel لإطار العمل الرائع
- فريق Vue.js للواجهة التفاعلية
- مجتمع المطورين العرب للدعم والمساندة
- عملاؤنا لثقتهم وملاحظاتهم القيمة

## 📞 التواصل

- **الموقع الرسمي**: [https://sanadan.com](https://sanadan.com)
- **البريد الإلكتروني**: [<EMAIL>](mailto:<EMAIL>)
- **تويتر**: [@SanadanPlatform](https://twitter.com/SanadanPlatform)
- **لينكد إن**: [Sanadan Platform](https://linkedin.com/company/sanadan-platform)

---

<div align="center">
  <p>صُنع بـ ❤️ في الكويت للمحامين العرب</p>
  <p>© 2024 منصة سندان. جميع الحقوق محفوظة.</p>
</div>

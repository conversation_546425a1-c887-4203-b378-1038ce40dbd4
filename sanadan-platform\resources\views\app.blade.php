<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title inertia>{{ config('app.name', 'منصة سندان') }}</title>

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">

        <!-- Favicon -->
        <link rel="icon" type="image/x-icon" href="/favicon.ico">
        <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
        <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
        <link rel="manifest" href="/site.webmanifest">

        <!-- Meta Tags -->
        <meta name="description" content="منصة سندان - نظام إدارة مكاتب المحاماة الأكثر تطوراً في الكويت والمنطقة العربية">
        <meta name="keywords" content="محاماة, قانون, الكويت, إدارة, مكتب محاماة, قضايا, عملاء">
        <meta name="author" content="منصة سندان">
        
        <!-- Open Graph -->
        <meta property="og:title" content="{{ config('app.name', 'منصة سندان') }}">
        <meta property="og:description" content="نظام إدارة مكاتب المحاماة الأكثر تطوراً في الكويت والمنطقة العربية">
        <meta property="og:type" content="website">
        <meta property="og:url" content="{{ url('/') }}">
        <meta property="og:image" content="{{ asset('images/og-image.png') }}">
        
        <!-- Twitter Card -->
        <meta name="twitter:card" content="summary_large_image">
        <meta name="twitter:title" content="{{ config('app.name', 'منصة سندان') }}">
        <meta name="twitter:description" content="نظام إدارة مكاتب المحاماة الأكثر تطوراً في الكويت والمنطقة العربية">
        <meta name="twitter:image" content="{{ asset('images/twitter-card.png') }}">

        <!-- PWA Meta Tags -->
        <meta name="theme-color" content="#3B82F6">
        <meta name="mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-capable" content="yes">
        <meta name="apple-mobile-web-app-status-bar-style" content="default">
        <meta name="apple-mobile-web-app-title" content="سندان">

        <!-- Scripts -->
        @routes
        @vite(['resources/js/app.js', "resources/js/Pages/{$page['component']}.vue"])
        @inertiaHead
    </head>
    <body class="font-sans antialiased bg-gray-50">
        @inertia

        <!-- Service Worker Registration -->
        <script>
            if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                    navigator.serviceWorker.register('/sw.js')
                        .then(function(registration) {
                            console.log('SW registered: ', registration);
                        })
                        .catch(function(registrationError) {
                            console.log('SW registration failed: ', registrationError);
                        });
                });
            }
        </script>

        <!-- Global Variables -->
        <script>
            window.Laravel = {
                csrfToken: '{{ csrf_token() }}',
                locale: '{{ app()->getLocale() }}',
                direction: '{{ app()->getLocale() === "ar" ? "rtl" : "ltr" }}',
                timezone: '{{ config("app.timezone") }}',
                currency: 'KWD',
                @auth
                user: {!! json_encode(auth()->user()) !!}
                @else
                user: null
                @endauth
            };
        </script>

        <!-- Loading Screen -->
        <div id="loading-screen" class="fixed inset-0 bg-white z-50 flex items-center justify-center">
            <div class="text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
                <p class="text-gray-600">جاري التحميل...</p>
            </div>
        </div>

        <style>
            #loading-screen {
                transition: opacity 0.3s ease-in-out;
            }
            
            .fade-out {
                opacity: 0;
                pointer-events: none;
            }
        </style>

        <script>
            // Hide loading screen when page is loaded
            window.addEventListener('load', function() {
                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen) {
                    loadingScreen.classList.add('fade-out');
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 300);
                }
            });

            // Hide loading screen after 3 seconds as fallback
            setTimeout(function() {
                const loadingScreen = document.getElementById('loading-screen');
                if (loadingScreen && !loadingScreen.classList.contains('fade-out')) {
                    loadingScreen.classList.add('fade-out');
                    setTimeout(() => {
                        loadingScreen.style.display = 'none';
                    }, 300);
                }
            }, 3000);
        </script>
    </body>
</html>

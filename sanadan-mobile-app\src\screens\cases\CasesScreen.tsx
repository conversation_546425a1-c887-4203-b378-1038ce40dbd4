import React, { useState, useCallback, useMemo } from 'react';
import {
  View,
  FlatList,
  StyleSheet,
  RefreshControl,
  Alert,
} from 'react-native';
import {
  Text,
  Searchbar,
  FAB,
  Chip,
  Menu,
  IconButton,
  Card,
  Badge,
  Avatar,
} from 'react-native-paper';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { useQuery } from 'react-query';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

// المكونات والخدمات
import { useTheme } from '../../contexts/ThemeContext';
import { useLanguage } from '../../contexts/LanguageContext';
import { casesService } from '../../services/casesService';
import { LegalCase, SearchFilters } from '../../types';
import LoadingSpinner from '../../components/LoadingSpinner';
import ErrorMessage from '../../components/ErrorMessage';
import EmptyState from '../../components/EmptyState';
import CaseCard from '../../components/CaseCard';
import FilterModal from '../../components/FilterModal';
import SortModal from '../../components/SortModal';

export default function CasesScreen() {
  const navigation = useNavigation();
  const { theme } = useTheme();
  const { t } = useLanguage();

  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<SearchFilters>({});
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [refreshing, setRefreshing] = useState(false);
  const [filterVisible, setFilterVisible] = useState(false);
  const [sortVisible, setSortVisible] = useState(false);
  const [menuVisible, setMenuVisible] = useState(false);

  // استعلام القضايا
  const {
    data: casesData,
    isLoading,
    error,
    refetch,
  } = useQuery(
    ['cases', searchQuery, filters, sortBy, sortOrder],
    () => casesService.getCases({
      query: searchQuery,
      ...filters,
      sortBy,
      sortOrder,
    }),
    {
      keepPreviousData: true,
      staleTime: 2 * 60 * 1000,
    }
  );

  const cases = casesData?.data || [];
  const totalCases = casesData?.meta?.total || 0;

  // تحديث البيانات عند التركيز على الشاشة
  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch])
  );

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('خطأ في تحديث القضايا:', error);
    } finally {
      setRefreshing(false);
    }
  }, [refetch]);

  const handleSearch = useCallback((query: string) => {
    setSearchQuery(query);
  }, []);

  const handleFilterApply = useCallback((newFilters: SearchFilters) => {
    setFilters(newFilters);
    setFilterVisible(false);
  }, []);

  const handleSortApply = useCallback((field: string, order: 'asc' | 'desc') => {
    setSortBy(field);
    setSortOrder(order);
    setSortVisible(false);
  }, []);

  const handleCasePress = useCallback((caseId: string) => {
    navigation.navigate('CaseDetails', { caseId });
  }, [navigation]);

  const handleCreateCase = useCallback(() => {
    navigation.navigate('CreateCase');
  }, [navigation]);

  const handleCaseAction = useCallback((action: string, caseId: string) => {
    switch (action) {
      case 'edit':
        navigation.navigate('EditCase', { caseId });
        break;
      case 'delete':
        Alert.alert(
          'حذف القضية',
          'هل أنت متأكد من حذف هذه القضية؟ لا يمكن التراجع عن هذا الإجراء.',
          [
            { text: 'إلغاء', style: 'cancel' },
            {
              text: 'حذف',
              style: 'destructive',
              onPress: () => deleteCase(caseId),
            },
          ]
        );
        break;
      case 'archive':
        archiveCase(caseId);
        break;
      default:
        break;
    }
  }, [navigation]);

  const deleteCase = async (caseId: string) => {
    try {
      await casesService.deleteCase(caseId);
      refetch();
    } catch (error) {
      Alert.alert('خطأ', 'فشل في حذف القضية. يرجى المحاولة مرة أخرى.');
    }
  };

  const archiveCase = async (caseId: string) => {
    try {
      await casesService.archiveCase(caseId);
      refetch();
    } catch (error) {
      Alert.alert('خطأ', 'فشل في أرشفة القضية. يرجى المحاولة مرة أخرى.');
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      open: theme.colors.primary,
      in_progress: theme.colors.warning,
      pending: theme.colors.outline,
      closed: theme.colors.success,
      dismissed: theme.colors.error,
    };
    return colors[status] || theme.colors.outline;
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      low: theme.colors.success,
      medium: theme.colors.warning,
      high: theme.colors.error,
      urgent: theme.colors.error,
    };
    return colors[priority] || theme.colors.outline;
  };

  const renderCaseItem = ({ item }: { item: LegalCase }) => (
    <CaseCard
      case={item}
      onPress={() => handleCasePress(item.id)}
      onAction={(action) => handleCaseAction(action, item.id)}
    />
  );

  const renderHeader = () => (
    <View style={styles.header}>
      {/* شريط البحث */}
      <Searchbar
        placeholder={t('searchCases')}
        onChangeText={handleSearch}
        value={searchQuery}
        style={styles.searchbar}
        icon="magnify"
        clearIcon="close"
      />

      {/* أزرار الفلترة والترتيب */}
      <View style={styles.actionBar}>
        <View style={styles.filterChips}>
          <Chip
            icon="filter"
            onPress={() => setFilterVisible(true)}
            selected={Object.keys(filters).length > 0}
            style={styles.chip}
          >
            {t('filter')}
          </Chip>
          <Chip
            icon="sort"
            onPress={() => setSortVisible(true)}
            style={styles.chip}
          >
            {t('sort')}
          </Chip>
        </View>

        <Menu
          visible={menuVisible}
          onDismiss={() => setMenuVisible(false)}
          anchor={
            <IconButton
              icon="dots-vertical"
              onPress={() => setMenuVisible(true)}
            />
          }
        >
          <Menu.Item
            onPress={() => {
              setMenuVisible(false);
              // تصدير القضايا
            }}
            title={t('export')}
            leadingIcon="download"
          />
          <Menu.Item
            onPress={() => {
              setMenuVisible(false);
              // إعدادات العرض
            }}
            title={t('viewSettings')}
            leadingIcon="cog"
          />
        </Menu>
      </View>

      {/* عداد النتائج */}
      <Text style={[styles.resultsCount, { color: theme.colors.onSurfaceVariant }]}>
        {t('casesCount', { count: totalCases })}
      </Text>
    </View>
  );

  const renderEmptyState = () => (
    <EmptyState
      icon="folder-outline"
      title={searchQuery ? t('noCasesFound') : t('noCasesYet')}
      subtitle={
        searchQuery
          ? t('tryDifferentSearch')
          : t('createFirstCase')
      }
      actionLabel={searchQuery ? undefined : t('createCase')}
      onAction={searchQuery ? undefined : handleCreateCase}
    />
  );

  if (isLoading && !cases.length) {
    return <LoadingSpinner />;
  }

  if (error) {
    return (
      <ErrorMessage
        message={t('errorLoadingCases')}
        onRetry={refetch}
      />
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <FlatList
        data={cases}
        renderItem={renderCaseItem}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmptyState}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
      />

      {/* زر إنشاء قضية جديدة */}
      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={handleCreateCase}
        label={t('newCase')}
      />

      {/* نافذة الفلترة */}
      <FilterModal
        visible={filterVisible}
        onDismiss={() => setFilterVisible(false)}
        onApply={handleFilterApply}
        currentFilters={filters}
        type="cases"
      />

      {/* نافذة الترتيب */}
      <SortModal
        visible={sortVisible}
        onDismiss={() => setSortVisible(false)}
        onApply={handleSortApply}
        currentSort={{ field: sortBy, order: sortOrder }}
        type="cases"
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  listContainer: {
    flexGrow: 1,
    paddingBottom: 100,
  },
  header: {
    padding: 16,
    paddingBottom: 8,
  },
  searchbar: {
    marginBottom: 16,
  },
  actionBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  filterChips: {
    flexDirection: 'row',
    gap: 8,
  },
  chip: {
    marginRight: 8,
  },
  resultsCount: {
    fontSize: 14,
    marginTop: 8,
  },
  separator: {
    height: 8,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createDrawerNavigator } from '@react-navigation/drawer';
import { useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

// الشاشات
import SplashScreen from '../screens/SplashScreen';
import OnboardingScreen from '../screens/OnboardingScreen';
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import ForgotPasswordScreen from '../screens/auth/ForgotPasswordScreen';
import OTPVerificationScreen from '../screens/auth/OTPVerificationScreen';
import BiometricSetupScreen from '../screens/auth/BiometricSetupScreen';

// الشاشات الرئيسية
import DashboardScreen from '../screens/dashboard/DashboardScreen';
import CasesScreen from '../screens/cases/CasesScreen';
import CaseDetailsScreen from '../screens/cases/CaseDetailsScreen';
import CreateCaseScreen from '../screens/cases/CreateCaseScreen';
import ClientsScreen from '../screens/clients/ClientsScreen';
import ClientDetailsScreen from '../screens/clients/ClientDetailsScreen';
import CreateClientScreen from '../screens/clients/CreateClientScreen';
import HearingsScreen from '../screens/hearings/HearingsScreen';
import HearingDetailsScreen from '../screens/hearings/HearingDetailsScreen';
import CreateHearingScreen from '../screens/hearings/CreateHearingScreen';
import TasksScreen from '../screens/tasks/TasksScreen';
import TaskDetailsScreen from '../screens/tasks/TaskDetailsScreen';
import CreateTaskScreen from '../screens/tasks/CreateTaskScreen';
import DocumentsScreen from '../screens/documents/DocumentsScreen';
import DocumentViewerScreen from '../screens/documents/DocumentViewerScreen';
import InvoicesScreen from '../screens/invoices/InvoicesScreen';
import InvoiceDetailsScreen from '../screens/invoices/InvoiceDetailsScreen';
import CreateInvoiceScreen from '../screens/invoices/CreateInvoiceScreen';
import PaymentsScreen from '../screens/payments/PaymentsScreen';
import ReportsScreen from '../screens/reports/ReportsScreen';
import CalendarScreen from '../screens/calendar/CalendarScreen';
import NotificationsScreen from '../screens/notifications/NotificationsScreen';
import ProfileScreen from '../screens/profile/ProfileScreen';
import SettingsScreen from '../screens/settings/SettingsScreen';
import SearchScreen from '../screens/search/SearchScreen';

// السياق
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';

// أنواع التنقل
export type RootStackParamList = {
  Splash: undefined;
  Onboarding: undefined;
  Auth: undefined;
  Main: undefined;
};

export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
  ForgotPassword: undefined;
  OTPVerification: { email: string; type: 'register' | 'reset' };
  BiometricSetup: undefined;
};

export type MainTabParamList = {
  Dashboard: undefined;
  Cases: undefined;
  Clients: undefined;
  Calendar: undefined;
  More: undefined;
};

export type CasesStackParamList = {
  CasesList: undefined;
  CaseDetails: { caseId: string };
  CreateCase: { clientId?: string };
  EditCase: { caseId: string };
};

export type ClientsStackParamList = {
  ClientsList: undefined;
  ClientDetails: { clientId: string };
  CreateClient: undefined;
  EditClient: { clientId: string };
};

export type MoreStackParamList = {
  MoreMenu: undefined;
  Hearings: undefined;
  HearingDetails: { hearingId: string };
  CreateHearing: { caseId?: string };
  Tasks: undefined;
  TaskDetails: { taskId: string };
  CreateTask: { caseId?: string };
  Documents: undefined;
  DocumentViewer: { documentId: string };
  Invoices: undefined;
  InvoiceDetails: { invoiceId: string };
  CreateInvoice: { clientId?: string; caseId?: string };
  Payments: undefined;
  Reports: undefined;
  Notifications: undefined;
  Profile: undefined;
  Settings: undefined;
  Search: undefined;
};

const RootStack = createStackNavigator<RootStackParamList>();
const AuthStack = createStackNavigator<AuthStackParamList>();
const MainTab = createBottomTabNavigator<MainTabParamList>();
const CasesStack = createStackNavigator<CasesStackParamList>();
const ClientsStack = createStackNavigator<ClientsStackParamList>();
const MoreStack = createStackNavigator<MoreStackParamList>();

// مكون التبويبات الرئيسية
function MainTabNavigator() {
  const theme = useTheme();
  const { t } = useLanguage();

  return (
    <MainTab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Dashboard':
              iconName = focused ? 'view-dashboard' : 'view-dashboard-outline';
              break;
            case 'Cases':
              iconName = focused ? 'folder' : 'folder-outline';
              break;
            case 'Clients':
              iconName = focused ? 'account-group' : 'account-group-outline';
              break;
            case 'Calendar':
              iconName = focused ? 'calendar' : 'calendar-outline';
              break;
            case 'More':
              iconName = focused ? 'menu' : 'menu';
              break;
            default:
              iconName = 'circle';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.onSurfaceVariant,
        tabBarStyle: {
          backgroundColor: theme.colors.surface,
          borderTopColor: theme.colors.outline,
          paddingBottom: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontFamily: 'Cairo-Regular',
        },
        headerShown: false,
      })}
    >
      <MainTab.Screen 
        name="Dashboard" 
        component={DashboardScreen}
        options={{ tabBarLabel: t('dashboard') }}
      />
      <MainTab.Screen 
        name="Cases" 
        component={CasesStackNavigator}
        options={{ tabBarLabel: t('cases') }}
      />
      <MainTab.Screen 
        name="Clients" 
        component={ClientsStackNavigator}
        options={{ tabBarLabel: t('clients') }}
      />
      <MainTab.Screen 
        name="Calendar" 
        component={CalendarScreen}
        options={{ tabBarLabel: t('calendar') }}
      />
      <MainTab.Screen 
        name="More" 
        component={MoreStackNavigator}
        options={{ tabBarLabel: t('more') }}
      />
    </MainTab.Navigator>
  );
}

// مكون تنقل القضايا
function CasesStackNavigator() {
  const theme = useTheme();

  return (
    <CasesStack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.surface,
        },
        headerTintColor: theme.colors.onSurface,
        headerTitleStyle: {
          fontFamily: 'Cairo-Bold',
        },
      }}
    >
      <CasesStack.Screen 
        name="CasesList" 
        component={CasesScreen}
        options={{ title: 'القضايا' }}
      />
      <CasesStack.Screen 
        name="CaseDetails" 
        component={CaseDetailsScreen}
        options={{ title: 'تفاصيل القضية' }}
      />
      <CasesStack.Screen 
        name="CreateCase" 
        component={CreateCaseScreen}
        options={{ title: 'قضية جديدة' }}
      />
    </CasesStack.Navigator>
  );
}

// مكون تنقل العملاء
function ClientsStackNavigator() {
  const theme = useTheme();

  return (
    <ClientsStack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.surface,
        },
        headerTintColor: theme.colors.onSurface,
        headerTitleStyle: {
          fontFamily: 'Cairo-Bold',
        },
      }}
    >
      <ClientsStack.Screen 
        name="ClientsList" 
        component={ClientsScreen}
        options={{ title: 'العملاء' }}
      />
      <ClientsStack.Screen 
        name="ClientDetails" 
        component={ClientDetailsScreen}
        options={{ title: 'ملف العميل' }}
      />
      <ClientsStack.Screen 
        name="CreateClient" 
        component={CreateClientScreen}
        options={{ title: 'عميل جديد' }}
      />
    </ClientsStack.Navigator>
  );
}

// مكون تنقل المزيد
function MoreStackNavigator() {
  const theme = useTheme();

  return (
    <MoreStack.Navigator
      screenOptions={{
        headerStyle: {
          backgroundColor: theme.colors.surface,
        },
        headerTintColor: theme.colors.onSurface,
        headerTitleStyle: {
          fontFamily: 'Cairo-Bold',
        },
      }}
    >
      <MoreStack.Screen 
        name="MoreMenu" 
        component={MoreMenuScreen}
        options={{ title: 'المزيد' }}
      />
      <MoreStack.Screen 
        name="Hearings" 
        component={HearingsScreen}
        options={{ title: 'الجلسات' }}
      />
      <MoreStack.Screen 
        name="HearingDetails" 
        component={HearingDetailsScreen}
        options={{ title: 'تفاصيل الجلسة' }}
      />
      <MoreStack.Screen 
        name="CreateHearing" 
        component={CreateHearingScreen}
        options={{ title: 'جلسة جديدة' }}
      />
      <MoreStack.Screen 
        name="Tasks" 
        component={TasksScreen}
        options={{ title: 'المهام' }}
      />
      <MoreStack.Screen 
        name="TaskDetails" 
        component={TaskDetailsScreen}
        options={{ title: 'تفاصيل المهمة' }}
      />
      <MoreStack.Screen 
        name="CreateTask" 
        component={CreateTaskScreen}
        options={{ title: 'مهمة جديدة' }}
      />
      <MoreStack.Screen 
        name="Documents" 
        component={DocumentsScreen}
        options={{ title: 'المستندات' }}
      />
      <MoreStack.Screen 
        name="DocumentViewer" 
        component={DocumentViewerScreen}
        options={{ title: 'عرض المستند' }}
      />
      <MoreStack.Screen 
        name="Invoices" 
        component={InvoicesScreen}
        options={{ title: 'الفواتير' }}
      />
      <MoreStack.Screen 
        name="InvoiceDetails" 
        component={InvoiceDetailsScreen}
        options={{ title: 'تفاصيل الفاتورة' }}
      />
      <MoreStack.Screen 
        name="CreateInvoice" 
        component={CreateInvoiceScreen}
        options={{ title: 'فاتورة جديدة' }}
      />
      <MoreStack.Screen 
        name="Payments" 
        component={PaymentsScreen}
        options={{ title: 'المدفوعات' }}
      />
      <MoreStack.Screen 
        name="Reports" 
        component={ReportsScreen}
        options={{ title: 'التقارير' }}
      />
      <MoreStack.Screen 
        name="Notifications" 
        component={NotificationsScreen}
        options={{ title: 'الإشعارات' }}
      />
      <MoreStack.Screen 
        name="Profile" 
        component={ProfileScreen}
        options={{ title: 'الملف الشخصي' }}
      />
      <MoreStack.Screen 
        name="Settings" 
        component={SettingsScreen}
        options={{ title: 'الإعدادات' }}
      />
      <MoreStack.Screen 
        name="Search" 
        component={SearchScreen}
        options={{ title: 'البحث' }}
      />
    </MoreStack.Navigator>
  );
}

// مكون تنقل المصادقة
function AuthNavigator() {
  const theme = useTheme();

  return (
    <AuthStack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: theme.colors.background },
      }}
    >
      <AuthStack.Screen name="Login" component={LoginScreen} />
      <AuthStack.Screen name="Register" component={RegisterScreen} />
      <AuthStack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
      <AuthStack.Screen name="OTPVerification" component={OTPVerificationScreen} />
      <AuthStack.Screen name="BiometricSetup" component={BiometricSetupScreen} />
    </AuthStack.Navigator>
  );
}

// مكون التنقل الرئيسي
export default function AppNavigator() {
  const { user, isLoading, isFirstTime } = useAuth();
  const theme = useTheme();

  return (
    <RootStack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: theme.colors.background },
      }}
    >
      {isLoading ? (
        <RootStack.Screen name="Splash" component={SplashScreen} />
      ) : isFirstTime ? (
        <RootStack.Screen name="Onboarding" component={OnboardingScreen} />
      ) : user ? (
        <RootStack.Screen name="Main" component={MainTabNavigator} />
      ) : (
        <RootStack.Screen name="Auth" component={AuthNavigator} />
      )}
    </RootStack.Navigator>
  );
}

// شاشة قائمة المزيد
function MoreMenuScreen() {
  // سيتم تنفيذها لاحقاً
  return null;
}

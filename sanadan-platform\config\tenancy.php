<?php

declare(strict_types=1);

use Stancl\Tenancy\Database\Models\Domain;

return [
    'tenant_model' => \App\Models\Tenant::class,
    'id_generator' => Stancl\Tenancy\UUIDGenerator::class,

    'domain_model' => Domain::class,

    /*
    |--------------------------------------------------------------------------
    | Migration parameters
    |--------------------------------------------------------------------------
    */

    'migration_parameters' => [
        '--force' => true, // This needs to be true to run migrations in production.
        '--path' => [database_path('migrations/tenant')],
        '--realpath' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Seeder parameters
    |--------------------------------------------------------------------------
    */

    'seeder_parameters' => [
        '--class' => 'TenantSeeder', // root seeder class
        '--force' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Features
    |--------------------------------------------------------------------------
    */

    'features' => [
        // Stancl\Tenancy\Features\UserImpersonation::class,
        // Stancl\Tenancy\Features\TelescopeTags::class,
        Stancl\Tenancy\Features\UniversalRoutes::class,
        Stancl\Tenancy\Features\TenantConfig::class, // https://tenancyforlaravel.com/docs/v3/features/tenant-config
        // Stancl\Tenancy\Features\CrossDomainRedirect::class, // https://tenancyforlaravel.com/docs/v3/features/cross-domain-redirect
        // Stancl\Tenancy\Features\ViteBundler::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Storage drivers
    |--------------------------------------------------------------------------
    */

    'storage_drivers' => [
        'db' => Stancl\Tenancy\StorageDrivers\Database\DatabaseStorageDriver::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Database tenancy config
    |--------------------------------------------------------------------------
    */

    'database' => [
        'central_connection' => env('DB_CONNECTION', 'mysql'),

        /*
        |--------------------------------------------------------------------------
        | Template tenant connection
        |--------------------------------------------------------------------------
        */

        'template_tenant_connection' => null,

        /*
        |--------------------------------------------------------------------------
        | Tenant database names
        |--------------------------------------------------------------------------
        */

        'prefix' => 'tenant',
        'suffix' => '',

        /*
        |--------------------------------------------------------------------------
        | TenantDatabaseManagers
        |--------------------------------------------------------------------------
        */

        'managers' => [
            'mysql' => Stancl\Tenancy\TenantDatabaseManagers\MySQLDatabaseManager::class,
            'mariadb' => Stancl\Tenancy\TenantDatabaseManagers\MySQLDatabaseManager::class,
            'pgsql' => Stancl\Tenancy\TenantDatabaseManagers\PostgreSQLDatabaseManager::class,
            'sqlite' => Stancl\Tenancy\TenantDatabaseManagers\SQLiteDatabaseManager::class,

            /**
             * Use this database manager for MySQL to have a DB user created for each tenant database.
             * You can customize the grants given to these users by changing the $grants property.
             */
            // 'mysql' => Stancl\Tenancy\TenantDatabaseManagers\PermissionControlledMySQLDatabaseManager::class,

            /**
             * Disable the pgsql manager above, and enable the one below if you
             * want to separate tenant DBs by schemas rather than databases.
             */
            // 'pgsql' => Stancl\Tenancy\TenantDatabaseManagers\PostgreSQLSchemaManager::class, // Separate by schema instead of database
        ],

        /*
        |--------------------------------------------------------------------------
        | Database password generator
        |--------------------------------------------------------------------------
        */

        'password_generator' => Stancl\Tenancy\DatabasePasswordGenerators\RandomDatabasePasswordGenerator::class,

        /*
        |--------------------------------------------------------------------------
        | Cache configuration
        |--------------------------------------------------------------------------
        */

        'cache' => [
            'store' => null, // Set to null to use the default cache store.
            'prefix' => 'stancl_tenancy_db_',
            'ttl' => 3600, // seconds
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache tenancy config
    |--------------------------------------------------------------------------
    */

    'cache' => [
        'tag_base' => 'tenant', // This tag_base, followed by the tenant_id, will form a tag that will be applied on each cache call.
    ],

    /*
    |--------------------------------------------------------------------------
    | Filesystem tenancy config
    |--------------------------------------------------------------------------
    */

    'filesystem' => [
        /*
        |--------------------------------------------------------------------------
        | Asset helper URL
        |--------------------------------------------------------------------------
        */

        'asset_helper_tenancy' => true,

        /*
        |--------------------------------------------------------------------------
        | Filesystem connections
        |--------------------------------------------------------------------------
        */

        'disks' => [
            'local',
            'public',
            's3',
        ],

        /*
        |--------------------------------------------------------------------------
        | URL override
        |--------------------------------------------------------------------------
        */

        'url_override' => true,

        /*
        |--------------------------------------------------------------------------
        | Suffix storage path
        |--------------------------------------------------------------------------
        */

        'suffix_base' => 'tenant',
        'suffix_parameters' => [
            'suffix' => '',
            'prefix' => '%tenant%', // Will be replaced with the tenant key.
            'disk_root' => '', // Will be replaced with the disk root.
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Redis tenancy config
    |--------------------------------------------------------------------------
    */

    'redis' => [
        'prefix_base' => 'tenant', // Each key in Redis will get a prefix that consists of this prefix_base, followed by the tenant id.
        'prefixed_connections' => [ // Redis connections whose keys are prefixed.
            'default',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Features used by tenancy bootstrappers
    |--------------------------------------------------------------------------
    */

    'bootstrappers' => [
        Stancl\Tenancy\Bootstrappers\DatabaseTenancyBootstrapper::class,
        Stancl\Tenancy\Bootstrappers\CacheTenancyBootstrapper::class,
        Stancl\Tenancy\Bootstrappers\FilesystemTenancyBootstrapper::class,
        Stancl\Tenancy\Bootstrappers\QueueTenancyBootstrapper::class,
        // Stancl\Tenancy\Bootstrappers\RedisTenancyBootstrapper::class, // Note: phpredis is needed
    ],

    /*
    |--------------------------------------------------------------------------
    | Database migrations
    |--------------------------------------------------------------------------
    */

    'database_migrations' => true,

    /*
    |--------------------------------------------------------------------------
    | Exempt domains
    |--------------------------------------------------------------------------
    */

    'exempt_domains' => [
        'www.sanadan.com',
        'sanadan.com',
        'admin.sanadan.com',
        'api.sanadan.com',
    ],

    /*
    |--------------------------------------------------------------------------
    | Central domains
    |--------------------------------------------------------------------------
    */

    'central_domains' => [
        '127.0.0.1',
        'localhost',
        'sanadan.test',
        'admin.sanadan.test',
    ],
];

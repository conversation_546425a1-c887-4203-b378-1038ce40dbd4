import axios from 'axios';
import _ from 'lodash';

/**
 * إعداد Axios كمكتبة HTTP الافتراضية
 * نقوم بتكوين Axios ليتعامل مع CSRF tokens تلقائياً
 */
window.axios = axios;

window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

// إعداد CSRF Token
const token = document.head.querySelector('meta[name="csrf-token"]');
if (token) {
    window.axios.defaults.headers.common['X-CSRF-TOKEN'] = token.content;
} else {
    console.error('CSRF token not found: https://laravel.com/docs/csrf#csrf-x-csrf-token');
}

/**
 * إعداد Lodash كمكتبة مساعدة
 */
window._ = _;

/**
 * إعداد Echo للإشعارات المباشرة
 * يمكن استخدام Pusher أو Socket.IO أو Ably
 */
// import Echo from 'laravel-echo';
// import Pusher from 'pusher-js';

// window.Pusher = Pusher;

// window.Echo = new Echo({
//     broadcaster: 'pusher',
//     key: import.meta.env.VITE_PUSHER_APP_KEY,
//     cluster: import.meta.env.VITE_PUSHER_APP_CLUSTER ?? 'mt1',
//     wsHost: import.meta.env.VITE_PUSHER_HOST ? import.meta.env.VITE_PUSHER_HOST : `ws-${import.meta.env.VITE_PUSHER_APP_CLUSTER}.pusher-channels.com`,
//     wsPort: import.meta.env.VITE_PUSHER_PORT ?? 80,
//     wssPort: import.meta.env.VITE_PUSHER_PORT ?? 443,
//     forceTLS: (import.meta.env.VITE_PUSHER_SCHEME ?? 'https') === 'https',
//     enabledTransports: ['ws', 'wss'],
// });

/**
 * إعداد متغيرات Laravel العامة
 */
window.Laravel = {
    csrfToken: document.head.querySelector('meta[name="csrf-token"]')?.content,
    user: null, // سيتم تعيينه من خلال Inertia
    locale: document.documentElement.lang || 'ar',
    direction: document.dir || 'rtl',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    currency: 'KWD',
};

/**
 * إعداد مساعدات عامة للتطبيق
 */
window.SanadanHelpers = {
    /**
     * تنسيق التاريخ حسب اللغة المحددة
     */
    formatDate(date, format = 'short') {
        if (!date) return '';
        
        const dateObj = new Date(date);
        const locale = window.Laravel.locale === 'ar' ? 'ar-KW' : 'en-US';
        
        const options = {
            short: { year: 'numeric', month: 'short', day: 'numeric' },
            long: { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' },
            time: { hour: '2-digit', minute: '2-digit' },
            datetime: { 
                year: 'numeric', month: 'short', day: 'numeric',
                hour: '2-digit', minute: '2-digit'
            }
        };
        
        return dateObj.toLocaleDateString(locale, options[format] || options.short);
    },

    /**
     * تنسيق العملة الكويتية
     */
    formatCurrency(amount, currency = 'KWD') {
        if (amount === null || amount === undefined) return '';
        
        const locale = window.Laravel.locale === 'ar' ? 'ar-KW' : 'en-KW';
        
        return new Intl.NumberFormat(locale, {
            style: 'currency',
            currency: currency,
            minimumFractionDigits: 3,
            maximumFractionDigits: 3,
        }).format(amount);
    },

    /**
     * تنسيق الأرقام
     */
    formatNumber(number, decimals = 0) {
        if (number === null || number === undefined) return '';
        
        const locale = window.Laravel.locale === 'ar' ? 'ar-KW' : 'en-KW';
        
        return new Intl.NumberFormat(locale, {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals,
        }).format(number);
    },

    /**
     * تحويل الأرقام إلى العربية
     */
    toArabicNumbers(str) {
        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        return str.toString().replace(/[0-9]/g, (w) => arabicNumbers[+w]);
    },

    /**
     * تحويل الأرقام إلى الإنجليزية
     */
    toEnglishNumbers(str) {
        const arabicNumbers = ['٠', '١', '٢', '٣', '٤', '٥', '٦', '٧', '٨', '٩'];
        return str.toString().replace(/[٠-٩]/g, (w) => arabicNumbers.indexOf(w));
    },

    /**
     * التحقق من صحة البريد الإلكتروني
     */
    validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },

    /**
     * التحقق من صحة رقم الهاتف الكويتي
     */
    validateKuwaitPhone(phone) {
        const re = /^(\+965|965)?[2569]\d{7}$/;
        return re.test(phone.replace(/\s/g, ''));
    },

    /**
     * التحقق من صحة الرقم المدني الكويتي
     */
    validateKuwaitCivilId(civilId) {
        if (!civilId || civilId.length !== 12) return false;
        
        // خوارزمية التحقق من الرقم المدني الكويتي
        const weights = [2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        let sum = 0;
        
        for (let i = 0; i < 11; i++) {
            sum += parseInt(civilId[i]) * weights[i];
        }
        
        const remainder = sum % 11;
        const checkDigit = remainder < 2 ? remainder : 11 - remainder;
        
        return checkDigit === parseInt(civilId[11]);
    },

    /**
     * إنشاء slug من النص العربي
     */
    createSlug(text) {
        return text
            .toLowerCase()
            .trim()
            .replace(/[\s\W-]+/g, '-')
            .replace(/^-+|-+$/g, '');
    },

    /**
     * اختصار النص مع إضافة نقاط
     */
    truncate(text, length = 100, suffix = '...') {
        if (!text || text.length <= length) return text;
        return text.substring(0, length).trim() + suffix;
    },

    /**
     * تحويل الحجم بالبايت إلى وحدة قابلة للقراءة
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 بايت';
        
        const k = 1024;
        const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * نسخ النص إلى الحافظة
     */
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            return true;
        } catch (err) {
            // fallback للمتصفحات القديمة
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                document.body.removeChild(textArea);
                return true;
            } catch (err) {
                document.body.removeChild(textArea);
                return false;
            }
        }
    },

    /**
     * تحديد لون الحالة
     */
    getStatusColor(status) {
        const colors = {
            // حالات القضايا
            'open': 'blue',
            'in_progress': 'yellow',
            'pending': 'gray',
            'on_hold': 'orange',
            'closed': 'green',
            'dismissed': 'red',
            'settled': 'green',
            'appealed': 'purple',
            
            // حالات المهام
            'completed': 'green',
            'cancelled': 'red',
            
            // حالات الفواتير
            'draft': 'gray',
            'sent': 'blue',
            'paid': 'green',
            'partial': 'yellow',
            'overdue': 'red',
            
            // الأولويات
            'low': 'green',
            'medium': 'yellow',
            'high': 'orange',
            'urgent': 'red',
            
            // حالات عامة
            'active': 'green',
            'inactive': 'gray',
            'suspended': 'red',
            'blacklisted': 'red',
        };
        
        return colors[status] || 'gray';
    },

    /**
     * تحديد أيقونة الحالة
     */
    getStatusIcon(status) {
        const icons = {
            'open': 'folder-open',
            'in_progress': 'clock',
            'pending': 'pause',
            'closed': 'check-circle',
            'completed': 'check-circle',
            'cancelled': 'x-circle',
            'paid': 'check-circle',
            'overdue': 'exclamation-triangle',
            'urgent': 'exclamation',
            'active': 'check',
            'inactive': 'minus',
        };
        
        return icons[status] || 'circle';
    },

    /**
     * تحويل الوقت النسبي (منذ كم من الوقت)
     */
    timeAgo(date) {
        if (!date) return '';
        
        const now = new Date();
        const past = new Date(date);
        const diffInSeconds = Math.floor((now - past) / 1000);
        
        const intervals = {
            year: 31536000,
            month: 2592000,
            week: 604800,
            day: 86400,
            hour: 3600,
            minute: 60
        };
        
        const rtf = new Intl.RelativeTimeFormat(window.Laravel.locale, { numeric: 'auto' });
        
        for (const [unit, seconds] of Object.entries(intervals)) {
            const interval = Math.floor(diffInSeconds / seconds);
            if (interval >= 1) {
                return rtf.format(-interval, unit);
            }
        }
        
        return rtf.format(-diffInSeconds, 'second');
    },

    /**
     * إنشاء رابط WhatsApp
     */
    createWhatsAppLink(phone, message = '') {
        const cleanPhone = phone.replace(/\D/g, '');
        const encodedMessage = encodeURIComponent(message);
        return `https://wa.me/${cleanPhone}?text=${encodedMessage}`;
    },

    /**
     * إنشاء رابط البريد الإلكتروني
     */
    createEmailLink(email, subject = '', body = '') {
        const encodedSubject = encodeURIComponent(subject);
        const encodedBody = encodeURIComponent(body);
        return `mailto:${email}?subject=${encodedSubject}&body=${encodedBody}`;
    },
};

/**
 * إعداد مراقب الاتصال بالإنترنت
 */
window.addEventListener('online', () => {
    console.log('اتصال الإنترنت متاح');
    document.body.classList.remove('offline');
});

window.addEventListener('offline', () => {
    console.log('لا يوجد اتصال بالإنترنت');
    document.body.classList.add('offline');
});

/**
 * إعداد مراقب تغيير حجم الشاشة
 */
window.addEventListener('resize', _.debounce(() => {
    // تحديث متغيرات CSS للشاشة
    document.documentElement.style.setProperty('--vh', `${window.innerHeight * 0.01}px`);
}, 250));

// تعيين المتغير الأولي
document.documentElement.style.setProperty('--vh', `${window.innerHeight * 0.01}px`);

/**
 * إعداد مراقب الوضع المظلم
 */
const darkModeMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
darkModeMediaQuery.addEventListener('change', (e) => {
    if (!localStorage.getItem('theme')) {
        document.documentElement.classList.toggle('dark', e.matches);
    }
});

// تطبيق الوضع المحفوظ أو الافتراضي
const savedTheme = localStorage.getItem('theme');
if (savedTheme) {
    document.documentElement.classList.toggle('dark', savedTheme === 'dark');
} else {
    document.documentElement.classList.toggle('dark', darkModeMediaQuery.matches);
}

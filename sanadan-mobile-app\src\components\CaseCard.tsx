import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import {
  Text,
  Card,
  Chip,
  IconButton,
  Menu,
  Avatar,
  Badge,
  useTheme,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

import { LegalCase } from '../types';
import { colors, getStatusColor, getPriorityColor } from '../theme/theme';

interface CaseCardProps {
  case: LegalCase;
  onPress: () => void;
  onAction: (action: string) => void;
  showClient?: boolean;
  showLawyer?: boolean;
  compact?: boolean;
}

export default function CaseCard({
  case: caseData,
  onPress,
  onAction,
  showClient = true,
  showLawyer = true,
  compact = false,
}: CaseCardProps) {
  const theme = useTheme();
  const [menuVisible, setMenuVisible] = useState(false);

  const getStatusLabel = (status: string): string => {
    const labels = {
      open: 'مفتوحة',
      in_progress: 'قيد المتابعة',
      pending: 'معلقة',
      on_hold: 'متوقفة',
      closed: 'مغلقة',
      dismissed: 'مرفوضة',
      settled: 'مسوية',
      appealed: 'مستأنفة',
    };
    return labels[status] || status;
  };

  const getPriorityLabel = (priority: string): string => {
    const labels = {
      low: 'منخفضة',
      medium: 'متوسطة',
      high: 'عالية',
      urgent: 'عاجل',
    };
    return labels[priority] || priority;
  };

  const formatDate = (date: Date | string): string => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return format(dateObj, 'dd MMM yyyy', { locale: ar });
  };

  const getDaysFromCreation = (): number => {
    const createdDate = new Date(caseData.createdAt);
    const today = new Date();
    const diffTime = Math.abs(today.getTime() - createdDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const getUrgencyIndicator = () => {
    if (caseData.priority === 'urgent') {
      return (
        <View style={[styles.urgencyIndicator, { backgroundColor: colors.secondary[500] }]}>
          <Icon name="alert" size={12} color={colors.white} />
        </View>
      );
    }
    return null;
  };

  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.7}>
      <Card style={[styles.card, { backgroundColor: theme.colors.surface }]}>
        {getUrgencyIndicator()}
        
        <Card.Content style={[styles.content, compact && styles.compactContent]}>
          {/* الرأس */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <Text style={[styles.caseNumber, { color: theme.colors.primary }]}>
                #{caseData.caseNumber}
              </Text>
              <Text style={[styles.title, { color: theme.colors.onSurface }]} numberOfLines={2}>
                {caseData.title}
              </Text>
            </View>
            
            <Menu
              visible={menuVisible}
              onDismiss={() => setMenuVisible(false)}
              anchor={
                <IconButton
                  icon="dots-vertical"
                  size={20}
                  onPress={() => setMenuVisible(true)}
                />
              }
            >
              <Menu.Item
                onPress={() => {
                  setMenuVisible(false);
                  onAction('view');
                }}
                title="عرض"
                leadingIcon="eye"
              />
              <Menu.Item
                onPress={() => {
                  setMenuVisible(false);
                  onAction('edit');
                }}
                title="تعديل"
                leadingIcon="pencil"
              />
              <Menu.Item
                onPress={() => {
                  setMenuVisible(false);
                  onAction('archive');
                }}
                title="أرشفة"
                leadingIcon="archive"
              />
              <Menu.Item
                onPress={() => {
                  setMenuVisible(false);
                  onAction('delete');
                }}
                title="حذف"
                leadingIcon="delete"
                titleStyle={{ color: theme.colors.error }}
              />
            </Menu>
          </View>

          {/* الوصف */}
          {!compact && caseData.description && (
            <Text
              style={[styles.description, { color: theme.colors.onSurfaceVariant }]}
              numberOfLines={2}
            >
              {caseData.description}
            </Text>
          )}

          {/* الحالة والأولوية */}
          <View style={styles.statusRow}>
            <Chip
              mode="flat"
              style={[
                styles.statusChip,
                { backgroundColor: `${getStatusColor(caseData.status)}20` },
              ]}
              textStyle={[
                styles.chipText,
                { color: getStatusColor(caseData.status) },
              ]}
            >
              {getStatusLabel(caseData.status)}
            </Chip>
            
            <Chip
              mode="flat"
              style={[
                styles.priorityChip,
                { backgroundColor: `${getPriorityColor(caseData.priority)}20` },
              ]}
              textStyle={[
                styles.chipText,
                { color: getPriorityColor(caseData.priority) },
              ]}
            >
              {getPriorityLabel(caseData.priority)}
            </Chip>
          </View>

          {/* معلومات العميل والمحامي */}
          {!compact && (showClient || showLawyer) && (
            <View style={styles.peopleRow}>
              {showClient && caseData.client && (
                <View style={styles.personInfo}>
                  <Avatar.Text
                    size={24}
                    label={caseData.client.name.charAt(0)}
                    style={styles.avatar}
                  />
                  <Text
                    style={[styles.personName, { color: theme.colors.onSurfaceVariant }]}
                    numberOfLines={1}
                  >
                    {caseData.client.name}
                  </Text>
                </View>
              )}
              
              {showLawyer && caseData.lawyer && (
                <View style={styles.personInfo}>
                  <Avatar.Text
                    size={24}
                    label={caseData.lawyer.name.charAt(0)}
                    style={[styles.avatar, { backgroundColor: colors.success[500] }]}
                  />
                  <Text
                    style={[styles.personName, { color: theme.colors.onSurfaceVariant }]}
                    numberOfLines={1}
                  >
                    {caseData.lawyer.name}
                  </Text>
                </View>
              )}
            </View>
          )}

          {/* التواريخ والإحصائيات */}
          <View style={styles.footer}>
            <View style={styles.dateInfo}>
              <Icon
                name="calendar"
                size={14}
                color={theme.colors.onSurfaceVariant}
                style={styles.dateIcon}
              />
              <Text style={[styles.dateText, { color: theme.colors.onSurfaceVariant }]}>
                {formatDate(caseData.createdAt)}
              </Text>
              <Text style={[styles.daysText, { color: theme.colors.onSurfaceVariant }]}>
                ({getDaysFromCreation()} يوم)
              </Text>
            </View>

            <View style={styles.statsRow}>
              {/* عدد الجلسات */}
              {caseData.hearingsCount > 0 && (
                <View style={styles.statItem}>
                  <Icon name="gavel" size={14} color={theme.colors.onSurfaceVariant} />
                  <Text style={[styles.statText, { color: theme.colors.onSurfaceVariant }]}>
                    {caseData.hearingsCount}
                  </Text>
                </View>
              )}

              {/* عدد المستندات */}
              {caseData.documentsCount > 0 && (
                <View style={styles.statItem}>
                  <Icon name="file-document" size={14} color={theme.colors.onSurfaceVariant} />
                  <Text style={[styles.statText, { color: theme.colors.onSurfaceVariant }]}>
                    {caseData.documentsCount}
                  </Text>
                </View>
              )}

              {/* عدد المهام */}
              {caseData.tasksCount > 0 && (
                <View style={styles.statItem}>
                  <Icon name="check-circle" size={14} color={theme.colors.onSurfaceVariant} />
                  <Text style={[styles.statText, { color: theme.colors.onSurfaceVariant }]}>
                    {caseData.tasksCount}
                  </Text>
                </View>
              )}
            </View>
          </View>

          {/* مؤشر الرسوم */}
          {caseData.feesAmount && (
            <View style={styles.feesContainer}>
              <Icon
                name="currency-usd"
                size={14}
                color={colors.gold[600]}
                style={styles.feesIcon}
              />
              <Text style={[styles.feesText, { color: colors.gold[600] }]}>
                {caseData.feesAmount.toLocaleString('ar-KW')} د.ك
              </Text>
            </View>
          )}
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    marginHorizontal: 16,
    marginVertical: 4,
    elevation: 2,
    borderRadius: 12,
    position: 'relative',
  },
  urgencyIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1,
  },
  content: {
    padding: 16,
  },
  compactContent: {
    padding: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  headerLeft: {
    flex: 1,
    marginRight: 8,
  },
  caseNumber: {
    fontSize: 12,
    fontWeight: '600',
    marginBottom: 4,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    lineHeight: 22,
  },
  description: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  statusRow: {
    flexDirection: 'row',
    marginBottom: 12,
    gap: 8,
  },
  statusChip: {
    height: 28,
  },
  priorityChip: {
    height: 28,
  },
  chipText: {
    fontSize: 12,
    fontWeight: '500',
  },
  peopleRow: {
    flexDirection: 'row',
    marginBottom: 12,
    gap: 16,
  },
  personInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  avatar: {
    marginRight: 8,
  },
  personName: {
    fontSize: 12,
    flex: 1,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  dateIcon: {
    marginRight: 4,
  },
  dateText: {
    fontSize: 12,
    marginRight: 4,
  },
  daysText: {
    fontSize: 11,
    opacity: 0.7,
  },
  statsRow: {
    flexDirection: 'row',
    gap: 12,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 12,
  },
  feesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
  },
  feesIcon: {
    marginRight: 4,
  },
  feesText: {
    fontSize: 14,
    fontWeight: '600',
  },
});

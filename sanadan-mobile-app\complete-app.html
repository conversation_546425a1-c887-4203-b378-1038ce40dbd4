<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>منصة سندان - تطبيق جوال كامل</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['Cairo', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
        }
        .phone-frame {
            width: 375px;
            height: 812px;
            background: linear-gradient(145deg, #1f2937, #374151);
            border-radius: 40px;
            padding: 8px;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            position: relative;
        }
        .phone-screen {
            width: 100%;
            height: 100%;
            background: #f9fafb;
            border-radius: 32px;
            overflow: hidden;
            position: relative;
        }
        .notch {
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 150px;
            height: 30px;
            background: #000;
            border-radius: 0 0 20px 20px;
            z-index: 10;
        }
        .status-bar {
            height: 44px;
            background: #3b82f6;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
            color: white;
            font-size: 14px;
            font-weight: 600;
        }
        .app-content {
            height: calc(100% - 44px - 80px);
            overflow-y: auto;
            background: #f9fafb;
        }
        .bottom-nav {
            height: 80px;
            background: white;
            border-top: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 10px 0;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 8px;
            border-radius: 8px;
        }
        .nav-item.active {
            color: #3b82f6;
            background: #eff6ff;
        }
        .nav-item:hover {
            color: #3b82f6;
            background: #f3f4f6;
        }
        .screen {
            display: none;
            padding: 16px;
            height: 100%;
            overflow-y: auto;
        }
        .screen.active {
            display: block;
        }
        .card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
            margin-bottom: 16px;
        }
        .stat-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .form-group {
            margin-bottom: 16px;
        }
        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Cairo', sans-serif;
        }
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        .btn {
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            font-family: 'Cairo', sans-serif;
        }
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        .btn-primary:hover {
            background: #2563eb;
        }
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        .list-item {
            display: flex;
            align-items: center;
            padding: 12px;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        .list-item:hover {
            background: #f9fafb;
        }
        .list-item:last-child {
            border-bottom: none;
        }
        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 600;
        }
        .badge-success {
            background: #dcfce7;
            color: #166534;
        }
        .badge-warning {
            background: #fef3c7;
            color: #92400e;
        }
        .badge-danger {
            background: #fee2e2;
            color: #991b1b;
        }
        .badge-info {
            background: #dbeafe;
            color: #1e40af;
        }
        .slide-in {
            animation: slideIn 0.5s ease-out;
        }
        @keyframes slideIn {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
    </style>
</head>
<body class="bg-gray-100 font-arabic min-h-screen flex items-center justify-center p-8">
    <div class="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
        <!-- معلومات التطبيق -->
        <div class="space-y-8">
            <div class="text-center lg:text-right">
                <div class="flex items-center justify-center lg:justify-start gap-4 mb-6">
                    <div class="w-16 h-16 bg-primary-600 rounded-2xl flex items-center justify-center">
                        <i class="fas fa-balance-scale text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">منصة سندان</h1>
                        <p class="text-primary-600 font-semibold">تطبيق جوال متكامل</p>
                    </div>
                </div>
                <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-4">
                    نظام إدارة مكاتب المحاماة
                    <span class="block text-primary-600">الأكثر تطوراً في الكويت</span>
                </h2>
                <p class="text-xl text-gray-600 mb-8">
                    تطبيق جوال شامل مع جميع الشاشات والميزات المطلوبة لإدارة مكتب المحاماة بكفاءة عالية
                </p>
            </div>

            <!-- الميزات المتاحة -->
            <div class="bg-white rounded-2xl p-6 shadow-lg">
                <h3 class="text-xl font-bold text-gray-900 mb-4">الشاشات المتاحة في التطبيق</h3>
                <div class="grid grid-cols-2 gap-4">
                    <div class="flex items-center gap-3">
                        <i class="fas fa-sign-in-alt text-primary-600"></i>
                        <span>تسجيل الدخول</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-user-plus text-green-600"></i>
                        <span>إنشاء حساب</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-tachometer-alt text-blue-600"></i>
                        <span>لوحة التحكم</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-folder text-yellow-600"></i>
                        <span>إدارة القضايا</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-users text-purple-600"></i>
                        <span>إدارة العملاء</span>
                    </div>
                    <div class="flex items-center gap-3">
                        <i class="fas fa-calendar text-red-600"></i>
                        <span>التقويم</span>
                    </div>
                </div>
            </div>

            <!-- إرشادات الاستخدام -->
            <div class="bg-primary-50 rounded-2xl p-6">
                <h3 class="text-lg font-bold text-primary-900 mb-3">كيفية الاستخدام</h3>
                <ul class="space-y-2 text-primary-800">
                    <li class="flex items-center gap-2">
                        <i class="fas fa-check-circle text-primary-600"></i>
                        انقر على الأزرار في الشريط السفلي للتنقل
                    </li>
                    <li class="flex items-center gap-2">
                        <i class="fas fa-check-circle text-primary-600"></i>
                        جرب تسجيل الدخول والتسجيل
                    </li>
                    <li class="flex items-center gap-2">
                        <i class="fas fa-check-circle text-primary-600"></i>
                        استكشف لوحة التحكم والإحصائيات
                    </li>
                    <li class="flex items-center gap-2">
                        <i class="fas fa-check-circle text-primary-600"></i>
                        تفاعل مع القوائم والبطاقات
                    </li>
                </ul>
            </div>
        </div>

        <!-- محاكي الهاتف -->
        <div class="flex justify-center">
            <div class="phone-frame">
                <div class="notch"></div>
                <div class="phone-screen">
                    <!-- شريط الحالة -->
                    <div class="status-bar">
                        <div class="flex items-center gap-2">
                            <span id="current-time">9:41</span>
                        </div>
                        <div class="flex items-center gap-1">
                            <i class="fas fa-signal text-sm"></i>
                            <i class="fas fa-wifi text-sm"></i>
                            <i class="fas fa-battery-three-quarters text-sm"></i>
                        </div>
                    </div>

                    <!-- محتوى التطبيق -->
                    <div class="app-content" id="app-content">
                        <!-- شاشة تسجيل الدخول -->
                        <div id="login-screen" class="screen active">
                            <div class="flex flex-col items-center justify-center h-full p-6">
                                <div class="w-20 h-20 bg-primary-600 rounded-full flex items-center justify-center mb-6">
                                    <i class="fas fa-balance-scale text-white text-2xl"></i>
                                </div>
                                <h1 class="text-2xl font-bold text-gray-900 mb-2">منصة سندان</h1>
                                <p class="text-gray-600 mb-8 text-center">نظام إدارة مكاتب المحاماة</p>

                                <div class="w-full max-w-sm space-y-4">
                                    <div class="form-group">
                                        <input type="email" class="form-input" placeholder="البريد الإلكتروني" id="login-email">
                                    </div>
                                    <div class="form-group">
                                        <input type="password" class="form-input" placeholder="كلمة المرور" id="login-password">
                                    </div>
                                    <div class="flex items-center justify-between text-sm">
                                        <label class="flex items-center gap-2">
                                            <input type="checkbox" class="rounded">
                                            <span>تذكرني</span>
                                        </label>
                                        <a href="#" class="text-primary-600">نسيت كلمة المرور؟</a>
                                    </div>
                                    <button class="btn btn-primary w-full" onclick="login()">تسجيل الدخول</button>
                                    <div class="text-center">
                                        <span class="text-gray-600">ليس لديك حساب؟ </span>
                                        <button class="text-primary-600 font-semibold" onclick="showScreen('register')">إنشاء حساب</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- شاشة التسجيل -->
                        <div id="register-screen" class="screen">
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-6">
                                    <button onclick="showScreen('login')" class="text-primary-600">
                                        <i class="fas fa-arrow-right"></i>
                                    </button>
                                    <h2 class="text-xl font-bold">إنشاء حساب جديد</h2>
                                    <div></div>
                                </div>

                                <div class="space-y-4">
                                    <div class="form-group">
                                        <input type="text" class="form-input" placeholder="الاسم الكامل">
                                    </div>
                                    <div class="form-group">
                                        <input type="email" class="form-input" placeholder="البريد الإلكتروني">
                                    </div>
                                    <div class="form-group">
                                        <input type="tel" class="form-input" placeholder="رقم الهاتف">
                                    </div>
                                    <div class="form-group">
                                        <input type="text" class="form-input" placeholder="اسم مكتب المحاماة">
                                    </div>
                                    <div class="form-group">
                                        <input type="text" class="form-input" placeholder="رقم الترخيص">
                                    </div>
                                    <div class="form-group">
                                        <input type="password" class="form-input" placeholder="كلمة المرور">
                                    </div>
                                    <div class="form-group">
                                        <input type="password" class="form-input" placeholder="تأكيد كلمة المرور">
                                    </div>

                                    <div class="space-y-2">
                                        <label class="flex items-center gap-2 text-sm">
                                            <input type="checkbox" class="rounded">
                                            <span>أوافق على شروط الاستخدام</span>
                                        </label>
                                        <label class="flex items-center gap-2 text-sm">
                                            <input type="checkbox" class="rounded">
                                            <span>أوافق على سياسة الخصوصية</span>
                                        </label>
                                    </div>

                                    <button class="btn btn-primary w-full" onclick="register()">إنشاء الحساب</button>
                                </div>
                            </div>
                        </div>

                        <!-- لوحة التحكم -->
                        <div id="dashboard-screen" class="screen">
                            <!-- ترحيب -->
                            <div class="card bg-gradient-to-r from-primary-600 to-primary-700 text-white slide-in">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-primary-100 text-sm">مرحباً بك</p>
                                        <h2 class="text-xl font-bold">أحمد المحامي</h2>
                                        <p class="text-primary-100 text-sm">مكتب المحاماة الرائد</p>
                                    </div>
                                    <div class="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                </div>
                            </div>

                            <!-- الإحصائيات -->
                            <div class="stats-grid">
                                <div class="stat-card slide-in" style="animation-delay: 0.1s">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-folder text-blue-600 text-sm"></i>
                                    </div>
                                    <div class="text-2xl font-bold text-gray-900" id="total-cases">127</div>
                                    <div class="text-sm text-gray-600">إجمالي القضايا</div>
                                </div>

                                <div class="stat-card slide-in" style="animation-delay: 0.2s">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-folder-open text-green-600 text-sm"></i>
                                    </div>
                                    <div class="text-2xl font-bold text-gray-900" id="active-cases">89</div>
                                    <div class="text-sm text-gray-600">القضايا النشطة</div>
                                </div>

                                <div class="stat-card slide-in" style="animation-delay: 0.3s">
                                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-users text-yellow-600 text-sm"></i>
                                    </div>
                                    <div class="text-2xl font-bold text-gray-900" id="total-clients">245</div>
                                    <div class="text-sm text-gray-600">العملاء</div>
                                </div>

                                <div class="stat-card slide-in" style="animation-delay: 0.4s">
                                    <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-2">
                                        <i class="fas fa-calendar text-purple-600 text-sm"></i>
                                    </div>
                                    <div class="text-2xl font-bold text-gray-900" id="upcoming-hearings">12</div>
                                    <div class="text-sm text-gray-600">جلسات قادمة</div>
                                </div>
                            </div>

                            <!-- الجلسات القادمة -->
                            <div class="card slide-in" style="animation-delay: 0.5s">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="font-semibold text-gray-900">الجلسات القادمة</h3>
                                    <button class="text-primary-600 text-sm">عرض الكل</button>
                                </div>
                                <div class="space-y-3">
                                    <div class="list-item">
                                        <div class="flex-1">
                                            <div class="font-medium text-gray-900">قضية التجارة الإلكترونية</div>
                                            <div class="text-sm text-gray-500">محكمة الكويت الكلية</div>
                                        </div>
                                        <div class="text-sm text-primary-600 font-medium">غداً 10:00 ص</div>
                                    </div>
                                    <div class="list-item">
                                        <div class="flex-1">
                                            <div class="font-medium text-gray-900">قضية العقار التجاري</div>
                                            <div class="text-sm text-gray-500">محكمة الجهراء</div>
                                        </div>
                                        <div class="text-sm text-primary-600 font-medium">الأحد 2:30 م</div>
                                    </div>
                                </div>
                            </div>

                            <!-- المهام العاجلة -->
                            <div class="card slide-in" style="animation-delay: 0.6s">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="font-semibold text-gray-900">المهام العاجلة</h3>
                                    <button class="text-primary-600 text-sm">عرض الكل</button>
                                </div>
                                <div class="space-y-3">
                                    <div class="list-item bg-red-50">
                                        <div class="w-4 h-4 border-2 border-red-500 rounded ml-3"></div>
                                        <div class="flex-1">
                                            <div class="font-medium text-gray-900">مراجعة عقد الشراكة</div>
                                            <div class="text-sm text-red-600">موعد الاستحقاق: اليوم</div>
                                        </div>
                                    </div>
                                    <div class="list-item bg-yellow-50">
                                        <div class="w-4 h-4 border-2 border-yellow-500 rounded ml-3"></div>
                                        <div class="flex-1">
                                            <div class="font-medium text-gray-900">إعداد مذكرة دفاع</div>
                                            <div class="text-sm text-yellow-600">موعد الاستحقاق: غداً</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- شاشة القضايا -->
                        <div id="cases-screen" class="screen">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-xl font-bold">القضايا</h2>
                                <button class="btn btn-primary">
                                    <i class="fas fa-plus ml-2"></i>
                                    قضية جديدة
                                </button>
                            </div>

                            <!-- شريط البحث -->
                            <div class="mb-4">
                                <input type="text" class="form-input" placeholder="البحث في القضايا...">
                            </div>

                            <!-- فلاتر -->
                            <div class="flex gap-2 mb-4 overflow-x-auto">
                                <button class="badge badge-info">الكل</button>
                                <button class="badge">نشطة</button>
                                <button class="badge">معلقة</button>
                                <button class="badge">مكتملة</button>
                            </div>

                            <!-- قائمة القضايا -->
                            <div class="space-y-3">
                                <div class="card">
                                    <div class="flex items-start justify-between mb-2">
                                        <div class="flex-1">
                                            <div class="flex items-center gap-2 mb-1">
                                                <span class="text-primary-600 font-semibold">#TC-2024-001</span>
                                                <span class="badge badge-warning">قيد المتابعة</span>
                                            </div>
                                            <h3 class="font-semibold text-gray-900">قضية التجارة الإلكترونية</h3>
                                            <p class="text-sm text-gray-600">شركة الخليج للتجارة</p>
                                        </div>
                                        <button class="text-gray-400">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                    </div>
                                    <div class="flex items-center justify-between text-sm text-gray-500">
                                        <span>آخر تحديث: منذ ساعة</span>
                                        <div class="flex items-center gap-4">
                                            <span><i class="fas fa-calendar ml-1"></i>3 جلسات</span>
                                            <span><i class="fas fa-file ml-1"></i>12 مستند</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="flex items-start justify-between mb-2">
                                        <div class="flex-1">
                                            <div class="flex items-center gap-2 mb-1">
                                                <span class="text-primary-600 font-semibold">#LB-2024-156</span>
                                                <span class="badge badge-info">معلقة</span>
                                            </div>
                                            <h3 class="font-semibold text-gray-900">قضية عمالية</h3>
                                            <p class="text-sm text-gray-600">محمد أحمد الصالح</p>
                                        </div>
                                        <button class="text-gray-400">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                    </div>
                                    <div class="flex items-center justify-between text-sm text-gray-500">
                                        <span>آخر تحديث: منذ يومين</span>
                                        <div class="flex items-center gap-4">
                                            <span><i class="fas fa-calendar ml-1"></i>1 جلسة</span>
                                            <span><i class="fas fa-file ml-1"></i>8 مستندات</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="flex items-start justify-between mb-2">
                                        <div class="flex-1">
                                            <div class="flex items-center gap-2 mb-1">
                                                <span class="text-primary-600 font-semibold">#CM-2024-089</span>
                                                <span class="badge badge-success">مكتملة</span>
                                            </div>
                                            <h3 class="font-semibold text-gray-900">قضية نزاع تجاري</h3>
                                            <p class="text-sm text-gray-600">شركة النور للاستثمار</p>
                                        </div>
                                        <button class="text-gray-400">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                    </div>
                                    <div class="flex items-center justify-between text-sm text-gray-500">
                                        <span>آخر تحديث: منذ أسبوع</span>
                                        <div class="flex items-center gap-4">
                                            <span><i class="fas fa-calendar ml-1"></i>5 جلسات</span>
                                            <span><i class="fas fa-file ml-1"></i>25 مستند</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- شاشة العملاء -->
                        <div id="clients-screen" class="screen">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-xl font-bold">العملاء</h2>
                                <button type="button" class="btn btn-primary">
                                    <i class="fas fa-plus ml-2"></i>
                                    عميل جديد
                                </button>
                            </div>

                            <!-- شريط البحث -->
                            <div class="mb-4">
                                <input type="text" class="form-input" placeholder="البحث في العملاء...">
                            </div>

                            <!-- قائمة العملاء -->
                            <div class="space-y-3">
                                <div class="card">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <div class="w-12 h-12 bg-primary-100 rounded-full flex items-center justify-center">
                                                <span class="text-primary-600 font-bold">ش</span>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold text-gray-900">شركة الخليج للتجارة</h3>
                                                <p class="text-sm text-gray-600">شركة • 3 قضايا نشطة</p>
                                                <p class="text-xs text-gray-500">+965 2222 3333</p>
                                            </div>
                                        </div>
                                        <button type="button" class="text-gray-400">
                                            <i class="fas fa-chevron-left"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                                                <span class="text-green-600 font-bold">م</span>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold text-gray-900">محمد أحمد الصالح</h3>
                                                <p class="text-sm text-gray-600">فرد • قضية واحدة</p>
                                                <p class="text-xs text-gray-500">+965 9999 8888</p>
                                            </div>
                                        </div>
                                        <button type="button" class="text-gray-400">
                                            <i class="fas fa-chevron-left"></i>
                                        </button>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-3">
                                            <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                                                <span class="text-yellow-600 font-bold">ش</span>
                                            </div>
                                            <div>
                                                <h3 class="font-semibold text-gray-900">شركة النور للاستثمار</h3>
                                                <p class="text-sm text-gray-600">شركة • 2 قضية مكتملة</p>
                                                <p class="text-xs text-gray-500">+965 2444 5555</p>
                                            </div>
                                        </div>
                                        <button type="button" class="text-gray-400">
                                            <i class="fas fa-chevron-left"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- شاشة التقويم -->
                        <div id="calendar-screen" class="screen">
                            <div class="flex items-center justify-between mb-6">
                                <h2 class="text-xl font-bold">التقويم</h2>
                                <button type="button" class="btn btn-primary">
                                    <i class="fas fa-plus ml-2"></i>
                                    موعد جديد
                                </button>
                            </div>

                            <!-- التقويم المبسط -->
                            <div class="card mb-4">
                                <div class="text-center mb-4">
                                    <h3 class="text-lg font-bold">ديسمبر 2024</h3>
                                </div>
                                <div class="grid grid-cols-7 gap-1 text-center text-sm">
                                    <div class="font-semibold text-gray-600 p-2">ح</div>
                                    <div class="font-semibold text-gray-600 p-2">ن</div>
                                    <div class="font-semibold text-gray-600 p-2">ث</div>
                                    <div class="font-semibold text-gray-600 p-2">ر</div>
                                    <div class="font-semibold text-gray-600 p-2">خ</div>
                                    <div class="font-semibold text-gray-600 p-2">ج</div>
                                    <div class="font-semibold text-gray-600 p-2">س</div>

                                    <div class="p-2">1</div>
                                    <div class="p-2">2</div>
                                    <div class="p-2">3</div>
                                    <div class="p-2">4</div>
                                    <div class="p-2">5</div>
                                    <div class="p-2">6</div>
                                    <div class="p-2">7</div>

                                    <div class="p-2">8</div>
                                    <div class="p-2">9</div>
                                    <div class="p-2">10</div>
                                    <div class="p-2">11</div>
                                    <div class="p-2">12</div>
                                    <div class="p-2">13</div>
                                    <div class="p-2">14</div>

                                    <div class="p-2">15</div>
                                    <div class="p-2">16</div>
                                    <div class="p-2">17</div>
                                    <div class="p-2">18</div>
                                    <div class="p-2">19</div>
                                    <div class="p-2">20</div>
                                    <div class="p-2">21</div>

                                    <div class="p-2">22</div>
                                    <div class="p-2">23</div>
                                    <div class="p-2 bg-primary-100 text-primary-600 rounded font-bold">24</div>
                                    <div class="p-2 bg-red-100 text-red-600 rounded">25</div>
                                    <div class="p-2">26</div>
                                    <div class="p-2 bg-yellow-100 text-yellow-600 rounded">27</div>
                                    <div class="p-2">28</div>

                                    <div class="p-2">29</div>
                                    <div class="p-2">30</div>
                                    <div class="p-2">31</div>
                                </div>
                            </div>

                            <!-- المواعيد اليوم -->
                            <div class="card">
                                <h3 class="font-semibold text-gray-900 mb-4">مواعيد اليوم</h3>
                                <div class="space-y-3">
                                    <div class="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                                        <div class="w-3 h-3 bg-blue-500 rounded-full"></div>
                                        <div class="flex-1">
                                            <div class="font-medium text-gray-900">جلسة محكمة</div>
                                            <div class="text-sm text-gray-600">قضية التجارة الإلكترونية</div>
                                        </div>
                                        <div class="text-sm text-blue-600 font-medium">10:00 ص</div>
                                    </div>

                                    <div class="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                        <div class="flex-1">
                                            <div class="font-medium text-gray-900">اجتماع عميل</div>
                                            <div class="text-sm text-gray-600">مراجعة العقد</div>
                                        </div>
                                        <div class="text-sm text-green-600 font-medium">2:30 م</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- شاشة المزيد -->
                        <div id="more-screen" class="screen">
                            <h2 class="text-xl font-bold mb-6">المزيد</h2>

                            <div class="space-y-4">
                                <div class="card">
                                    <div class="list-item">
                                        <i class="fas fa-gavel text-primary-600 w-6"></i>
                                        <span class="flex-1">الجلسات</span>
                                        <i class="fas fa-chevron-left text-gray-400"></i>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="list-item">
                                        <i class="fas fa-tasks text-green-600 w-6"></i>
                                        <span class="flex-1">المهام</span>
                                        <i class="fas fa-chevron-left text-gray-400"></i>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="list-item">
                                        <i class="fas fa-file-alt text-blue-600 w-6"></i>
                                        <span class="flex-1">المستندات</span>
                                        <i class="fas fa-chevron-left text-gray-400"></i>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="list-item">
                                        <i class="fas fa-file-invoice text-yellow-600 w-6"></i>
                                        <span class="flex-1">الفواتير</span>
                                        <i class="fas fa-chevron-left text-gray-400"></i>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="list-item">
                                        <i class="fas fa-chart-bar text-purple-600 w-6"></i>
                                        <span class="flex-1">التقارير</span>
                                        <i class="fas fa-chevron-left text-gray-400"></i>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="list-item">
                                        <i class="fas fa-bell text-red-600 w-6"></i>
                                        <span class="flex-1">الإشعارات</span>
                                        <span class="badge badge-danger">3</span>
                                        <i class="fas fa-chevron-left text-gray-400"></i>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="list-item">
                                        <i class="fas fa-cog text-gray-600 w-6"></i>
                                        <span class="flex-1">الإعدادات</span>
                                        <i class="fas fa-chevron-left text-gray-400"></i>
                                    </div>
                                </div>

                                <div class="card">
                                    <div class="list-item">
                                        <i class="fas fa-sign-out-alt text-red-600 w-6"></i>
                                        <span class="flex-1 text-red-600">تسجيل الخروج</span>
                                        <i class="fas fa-chevron-left text-gray-400"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- شريط التنقل السفلي -->
                    <div class="bottom-nav">
                        <div class="nav-item active" onclick="switchScreen('dashboard')">
                            <i class="fas fa-home text-lg"></i>
                            <span class="text-xs">الرئيسية</span>
                        </div>
                        <div class="nav-item" onclick="switchScreen('cases')">
                            <i class="fas fa-folder text-lg"></i>
                            <span class="text-xs">القضايا</span>
                        </div>
                        <div class="nav-item" onclick="switchScreen('clients')">
                            <i class="fas fa-users text-lg"></i>
                            <span class="text-xs">العملاء</span>
                        </div>
                        <div class="nav-item" onclick="switchScreen('calendar')">
                            <i class="fas fa-calendar text-lg"></i>
                            <span class="text-xs">التقويم</span>
                        </div>
                        <div class="nav-item" onclick="switchScreen('more')">
                            <i class="fas fa-ellipsis-h text-lg"></i>
                            <span class="text-xs">المزيد</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // متغيرات التطبيق
        let currentScreen = 'login';
        let isLoggedIn = false;

        // تحديث الوقت
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-KW', {
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            });
            document.getElementById('current-time').textContent = timeString;
        }

        // تبديل الشاشات
        function switchScreen(screenName) {
            // إخفاء جميع الشاشات
            document.querySelectorAll('.screen').forEach(screen => {
                screen.classList.remove('active');
            });

            // إظهار الشاشة المحددة
            const targetScreen = document.getElementById(screenName + '-screen');
            if (targetScreen) {
                targetScreen.classList.add('active');
                targetScreen.classList.add('fade-in');
                currentScreen = screenName;
            }

            // تحديث شريط التنقل
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // تفعيل العنصر المحدد في شريط التنقل
            const activeNavItem = document.querySelector(`[onclick="switchScreen('${screenName}')"]`);
            if (activeNavItem) {
                activeNavItem.classList.add('active');
            }
        }

        // دالة تسجيل الدخول
        function login() {
            const email = document.getElementById('login-email').value;
            const password = document.getElementById('login-password').value;

            if (!email || !password) {
                alert('يرجى إدخال البريد الإلكتروني وكلمة المرور');
                return;
            }

            // محاكاة تسجيل الدخول
            setTimeout(() => {
                isLoggedIn = true;
                switchScreen('dashboard');
                animateStats();
                showSuccessMessage('تم تسجيل الدخول بنجاح!');
            }, 1000);
        }

        // دالة التسجيل
        function register() {
            setTimeout(() => {
                isLoggedIn = true;
                switchScreen('dashboard');
                animateStats();
                showSuccessMessage('تم إنشاء الحساب بنجاح!');
            }, 1500);
        }

        // تحريك الإحصائيات
        function animateStats() {
            setTimeout(() => {
                animateCounter('total-cases', 127);
                animateCounter('active-cases', 89);
                animateCounter('total-clients', 245);
                animateCounter('upcoming-hearings', 12);
            }, 500);
        }

        // تحريك العدادات
        function animateCounter(elementId, targetValue, duration = 2000) {
            const element = document.getElementById(elementId);
            if (!element) return;

            const startValue = 0;
            const increment = targetValue / (duration / 16);
            let currentValue = startValue;

            const timer = setInterval(() => {
                currentValue += increment;
                if (currentValue >= targetValue) {
                    currentValue = targetValue;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(currentValue);
            }, 16);
        }

        // إظهار رسالة نجاح
        function showSuccessMessage(message) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'fixed top-4 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50';
            messageDiv.textContent = message;
            document.body.appendChild(messageDiv);

            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        // دالة عرض الشاشة
        function showScreen(screenName) {
            switchScreen(screenName);
        }

        // تهيئة التطبيق
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);
        });
    </script>
</body>
</html>
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as SecureStore from 'expo-secure-store';
import { Alert } from 'react-native';
import { ApiResponse, PaginatedResponse } from '../types';

// إعدادات API
const API_BASE_URL = __DEV__ 
  ? 'http://localhost:8000/api/v1' 
  : 'https://api.sanadan.com/v1';

const API_TIMEOUT = 30000; // 30 ثانية

class ApiService {
  private api: AxiosInstance;
  private isRefreshing = false;
  private failedQueue: Array<{
    resolve: (value?: any) => void;
    reject: (reason?: any) => void;
  }> = [];

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: API_TIMEOUT,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor لإضافة التوكن
    this.api.interceptors.request.use(
      async (config) => {
        const token = await SecureStore.getItemAsync('authToken');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // إضافة معرف الجهاز
        const deviceId = await this.getDeviceId();
        if (deviceId) {
          config.headers['X-Device-ID'] = deviceId;
        }

        // إضافة اللغة المفضلة
        const language = await AsyncStorage.getItem('preferredLanguage') || 'ar';
        config.headers['Accept-Language'] = language;

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor للتعامل مع الأخطاء
    this.api.interceptors.response.use(
      (response) => {
        return response;
      },
      async (error) => {
        const originalRequest = error.config;

        // التعامل مع خطأ 401 (غير مصرح)
        if (error.response?.status === 401 && !originalRequest._retry) {
          if (this.isRefreshing) {
            // إذا كان التحديث جارياً، انتظار النتيجة
            return new Promise((resolve, reject) => {
              this.failedQueue.push({ resolve, reject });
            }).then((token) => {
              originalRequest.headers.Authorization = `Bearer ${token}`;
              return this.api(originalRequest);
            }).catch((err) => {
              return Promise.reject(err);
            });
          }

          originalRequest._retry = true;
          this.isRefreshing = true;

          try {
            const newToken = await this.refreshToken();
            this.processQueue(null, newToken);
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return this.api(originalRequest);
          } catch (refreshError) {
            this.processQueue(refreshError, null);
            await this.handleAuthError();
            return Promise.reject(refreshError);
          } finally {
            this.isRefreshing = false;
          }
        }

        // التعامل مع أخطاء الشبكة
        if (!error.response) {
          this.handleNetworkError(error);
        }

        return Promise.reject(error);
      }
    );
  }

  private processQueue(error: any, token: string | null = null) {
    this.failedQueue.forEach(({ resolve, reject }) => {
      if (error) {
        reject(error);
      } else {
        resolve(token);
      }
    });

    this.failedQueue = [];
  }

  private async refreshToken(): Promise<string> {
    try {
      const refreshToken = await SecureStore.getItemAsync('refreshToken');
      if (!refreshToken) {
        throw new Error('لا يوجد رمز تحديث');
      }

      const response = await axios.post(`${API_BASE_URL}/auth/refresh`, {
        refresh_token: refreshToken,
      });

      const { access_token, refresh_token } = response.data;
      
      await SecureStore.setItemAsync('authToken', access_token);
      await SecureStore.setItemAsync('refreshToken', refresh_token);

      return access_token;
    } catch (error) {
      await SecureStore.deleteItemAsync('authToken');
      await SecureStore.deleteItemAsync('refreshToken');
      throw error;
    }
  }

  private async handleAuthError() {
    // حذف التوكنات
    await SecureStore.deleteItemAsync('authToken');
    await SecureStore.deleteItemAsync('refreshToken');
    
    // إعادة توجيه للمصادقة
    // يمكن استخدام navigation service هنا
    Alert.alert(
      'انتهت جلسة العمل',
      'يرجى تسجيل الدخول مرة أخرى',
      [{ text: 'موافق' }]
    );
  }

  private handleNetworkError(error: any) {
    if (error.code === 'ECONNABORTED') {
      Alert.alert('خطأ', 'انتهت مهلة الاتصال. يرجى المحاولة مرة أخرى.');
    } else if (error.message === 'Network Error') {
      Alert.alert('خطأ في الشبكة', 'تحقق من اتصالك بالإنترنت وحاول مرة أخرى.');
    }
  }

  private async getDeviceId(): Promise<string> {
    try {
      let deviceId = await AsyncStorage.getItem('deviceId');
      if (!deviceId) {
        deviceId = this.generateDeviceId();
        await AsyncStorage.setItem('deviceId', deviceId);
      }
      return deviceId;
    } catch (error) {
      return this.generateDeviceId();
    }
  }

  private generateDeviceId(): string {
    return 'device_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36);
  }

  // طرق HTTP الأساسية
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.api.get(url, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.api.post(url, data, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.api.put(url, data, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async patch<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.api.patch(url, data, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    try {
      const response: AxiosResponse<ApiResponse<T>> = await this.api.delete(url, config);
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // طرق خاصة للبيانات المقسمة
  async getPaginated<T>(url: string, params?: any): Promise<PaginatedResponse<T>> {
    try {
      const response: AxiosResponse<PaginatedResponse<T>> = await this.api.get(url, { params });
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // رفع الملفات
  async uploadFile<T>(
    url: string,
    file: any,
    onProgress?: (progress: number) => void
  ): Promise<ApiResponse<T>> {
    try {
      const formData = new FormData();
      formData.append('file', {
        uri: file.uri,
        type: file.type,
        name: file.name,
      } as any);

      const response: AxiosResponse<ApiResponse<T>> = await this.api.post(url, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            const progress = (progressEvent.loaded / progressEvent.total) * 100;
            onProgress(progress);
          }
        },
      });

      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  // تحميل الملفات
  async downloadFile(url: string, onProgress?: (progress: number) => void): Promise<Blob> {
    try {
      const response = await this.api.get(url, {
        responseType: 'blob',
        onDownloadProgress: (progressEvent) => {
          if (onProgress && progressEvent.total) {
            const progress = (progressEvent.loaded / progressEvent.total) * 100;
            onProgress(progress);
          }
        },
      });

      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  private handleError(error: any): Error {
    if (error.response) {
      // الخادم رد بخطأ
      const { status, data } = error.response;
      
      if (data?.message) {
        return new Error(data.message);
      }

      switch (status) {
        case 400:
          return new Error('طلب غير صحيح');
        case 401:
          return new Error('غير مصرح لك بالوصول');
        case 403:
          return new Error('ممنوع الوصول');
        case 404:
          return new Error('المورد غير موجود');
        case 422:
          return new Error('بيانات غير صحيحة');
        case 429:
          return new Error('تم تجاوز حد الطلبات');
        case 500:
          return new Error('خطأ في الخادم');
        case 503:
          return new Error('الخدمة غير متاحة مؤقتاً');
        default:
          return new Error(`خطأ غير متوقع: ${status}`);
      }
    } else if (error.request) {
      // لم يتم استلام رد من الخادم
      return new Error('لا يمكن الوصول للخادم. تحقق من اتصالك بالإنترنت.');
    } else {
      // خطأ في إعداد الطلب
      return new Error(error.message || 'حدث خطأ غير متوقع');
    }
  }

  // إلغاء الطلبات
  createCancelToken() {
    return axios.CancelToken.source();
  }

  // فحص حالة الاتصال
  async checkConnection(): Promise<boolean> {
    try {
      await this.api.get('/health', { timeout: 5000 });
      return true;
    } catch (error) {
      return false;
    }
  }

  // الحصول على معلومات API
  async getApiInfo(): Promise<any> {
    try {
      const response = await this.api.get('/info');
      return response.data;
    } catch (error) {
      throw this.handleError(error);
    }
  }
}

// إنشاء مثيل واحد من الخدمة
export const apiService = new ApiService();
export default apiService;

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل الهجرة
     */
    public function up(): void
    {
        Schema::create('tasks', function (Blueprint $table) {
            $table->id();
            $table->string('title'); // عنوان المهمة
            $table->text('description')->nullable(); // وصف المهمة
            $table->foreignId('assigned_to')->constrained('users'); // المكلف بالمهمة
            $table->foreignId('legal_case_id')->nullable()->constrained('legal_cases')->onDelete('cascade');
            $table->foreignId('client_id')->nullable()->constrained('clients');
            $table->datetime('due_date')->nullable(); // تاريخ الاستحقاق
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->enum('status', ['pending', 'in_progress', 'completed', 'cancelled'])->default('pending');
            $table->enum('type', ['call', 'meeting', 'document', 'research', 'court', 'follow_up', 'other'])->default('other');
            $table->integer('estimated_hours')->nullable(); // الساعات المتوقعة
            $table->integer('actual_hours')->nullable(); // الساعات الفعلية
            $table->text('notes')->nullable();
            $table->datetime('completed_at')->nullable(); // تاريخ الإنجاز
            $table->boolean('reminder_sent')->default(false);
            $table->foreignId('created_by')->constrained('users');
            $table->timestamps();
            
            $table->index(['assigned_to', 'status']);
            $table->index(['due_date', 'status']);
            $table->index(['legal_case_id', 'status']);
            $table->index('priority');
        });
    }

    /**
     * التراجع عن الهجرة
     */
    public function down(): void
    {
        Schema::dropIfExists('tasks');
    }
};

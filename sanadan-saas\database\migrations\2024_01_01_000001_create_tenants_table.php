<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tenants', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم مكتب المحاماة
            $table->string('slug')->unique(); // معرف فريد للمكتب
            $table->string('domain')->nullable()->unique(); // نطاق مخصص
            $table->string('subdomain')->unique(); // النطاق الفرعي
            $table->string('email')->unique();
            $table->string('phone')->nullable();
            $table->text('address')->nullable();
            $table->string('license_number')->nullable(); // رقم الترخيص
            $table->string('commercial_registration')->nullable(); // السجل التجاري
            $table->string('logo')->nullable();
            $table->string('primary_color')->default('#3b82f6');
            $table->string('secondary_color')->default('#1e40af');
            
            // إعدادات الاشتراك
            $table->enum('plan', ['trial', 'basic', 'professional', 'enterprise'])->default('trial');
            $table->timestamp('trial_ends_at')->nullable();
            $table->timestamp('subscription_ends_at')->nullable();
            $table->boolean('is_active')->default(true);
            $table->integer('max_users')->default(5);
            $table->integer('max_cases')->default(100);
            $table->integer('max_clients')->default(500);
            $table->integer('max_storage_gb')->default(5);
            
            // إعدادات النظام
            $table->string('timezone')->default('Asia/Kuwait');
            $table->string('currency')->default('KWD');
            $table->string('language')->default('ar');
            $table->json('settings')->nullable(); // إعدادات إضافية
            
            // معلومات الفوترة
            $table->string('billing_name')->nullable();
            $table->string('billing_email')->nullable();
            $table->text('billing_address')->nullable();
            $table->string('tax_number')->nullable();
            
            $table->timestamps();
            $table->softDeletes();
            
            // فهارس
            $table->index(['is_active', 'plan']);
            $table->index('subscription_ends_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tenants');
    }
};
